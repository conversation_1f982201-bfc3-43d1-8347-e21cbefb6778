package com.zhisuo.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 用户令牌实体类
 */
@Data
@TableName("user_tokens")
public class UserToken {
    
    @TableId
    private String tokenId;       // 令牌ID
    
    private String userId;        // 关联用户ID
    
    private String token;         // 令牌值
    
    private String deviceType;    // 设备类型
    
    private Date expireTime;      // 过期时间
    
    private Date createTime;      // 创建时间
    
    private Date lastActiveTime;  // 最后活跃时间
} 