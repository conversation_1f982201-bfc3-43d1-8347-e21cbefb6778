package com.zhisuo.app.dto.response;

import lombok.Data;

import java.util.List;

/**
 * 搜索建议响应DTO
 */
@Data
public class SearchSuggestionResponse {
    
    /**
     * 搜索关键词
     */
    private String keyword;
    
    /**
     * 关键词联想建议
     */
    private List<String> suggestions;
    
    /**
     * 热门搜索词
     */
    private List<String> hotKeywords;
    
    /**
     * 相关标签
     */
    private List<TagSuggestion> relatedTags;
    
    /**
     * 历史搜索（如果用户已登录）
     */
    private List<String> historyKeywords;

    /**
     * 热点话题建议
     */
    private List<TopicSuggestion> topicSuggestions;

    /**
     * 热点话题建议内部类
     */
    @Data
    public static class TopicSuggestion {
        /**
         * 热点话题ID
         */
        private String topicId;

        /**
         * 话题标题
         */
        private String title;

        /**
         * 热度值
         */
        private String hotValue;

        /**
         * 搜索量
         */
        private Integer searchCount;
    }

    /**
     * 标签建议内部类
     */
    @Data
    public static class TagSuggestion {
        /**
         * 标签ID
         */
        private String tagId;
        
        /**
         * 标签名称
         */
        private String tagName;
        
        /**
         * 相关内容数量
         */
        private Long contentCount;
    }
}
