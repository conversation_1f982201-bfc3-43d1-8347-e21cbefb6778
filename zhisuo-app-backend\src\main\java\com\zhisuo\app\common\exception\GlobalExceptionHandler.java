package com.zhisuo.app.common.exception;

import com.zhisuo.app.common.ErrorCode;
import com.zhisuo.app.common.Result;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.BindException;
import org.springframework.validation.BindingResult;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import javax.validation.ConstraintViolationException;

/**
 * 全局异常处理
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {
    
    /**
     * 处理业务异常
     */
    @ExceptionHandler(BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }
    
    /**
     * 处理参数校验异常
     */
    @ExceptionHandler({MethodArgumentNotValidException.class, BindException.class})
    public Result<Void> handleValidationException(Exception e) {
        BindingResult bindingResult = null;
        if (e instanceof MethodArgumentNotValidException) {
            bindingResult = ((MethodArgumentNotValidException) e).getBindingResult();
        } else if (e instanceof BindException) {
            bindingResult = ((BindException) e).getBindingResult();
        }
        
        StringBuilder sb = new StringBuilder("参数校验失败: ");
        if (bindingResult != null && bindingResult.hasErrors()) {
            FieldError fieldError = bindingResult.getFieldError();
            if (fieldError != null) {
                sb.append(fieldError.getDefaultMessage());
            }
        }
        
        log.warn("参数校验异常: {}", sb.toString());
        return Result.error(ErrorCode.INVALID_PARAMETER.getCode(), sb.toString());
    }
    
    /**
     * 处理参数校验异常
     */
    @ExceptionHandler(ConstraintViolationException.class)
    public Result<Void> handleConstraintViolationException(ConstraintViolationException e) {
        log.warn("参数校验异常: {}", e.getMessage());
        return Result.error(ErrorCode.INVALID_PARAMETER.getCode(), "参数校验失败: " + e.getMessage());
    }
    
    /**
     * 处理其他异常
     */
    @ExceptionHandler(Exception.class)
    public Result<Void> handleException(Exception e) {
        log.error("未知异常: {}", e.getMessage(), e);
        // 在开发环境下返回详细错误信息，生产环境下返回通用错误信息
        String message = e.getMessage();
        if (message != null && message.length() > 200) {
            message = message.substring(0, 200) + "...";
        }
        return Result.error(ErrorCode.SYSTEM_ERROR.getCode(),
                           "系统异常: " + (message != null ? message : "未知错误"));
    }
} 