package com.zhisuo.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 系统通知实体类
 */
@Data
@TableName("system_notices")
public class SystemNotice {
    
    @TableId
    private String noticeId;     // 通知ID
    
    private String title;        // 通知标题
    
    private String content;      // 通知内容
    
    private Integer noticeType;  // 通知类型(0:系统通知,1:版本更新,2:活动通知)
    
    private String imageUrl;     // 图片URL
    
    private Date publishTime;    // 发布时间
    
    private Date expireTime;     // 过期时间
    
    private Integer status;      // 状态(1:有效,0:无效)
} 