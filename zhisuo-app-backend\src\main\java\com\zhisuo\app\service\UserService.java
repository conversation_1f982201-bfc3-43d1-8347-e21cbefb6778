package com.zhisuo.app.service;

import com.zhisuo.app.dto.request.PasswordLoginRequest;
import com.zhisuo.app.dto.request.SetPasswordRequest;
import com.zhisuo.app.dto.request.SmsCodeRequest;
import com.zhisuo.app.dto.request.SmsLoginRequest;
import com.zhisuo.app.dto.request.TokenRefreshRequest;
import com.zhisuo.app.dto.request.UpdateUserInfoRequest;
import com.zhisuo.app.dto.response.LoginResponse;
import com.zhisuo.app.dto.response.SmsCodeResponse;
import com.zhisuo.app.dto.response.TokenRefreshResponse;
import com.zhisuo.app.entity.User;

import java.util.Map;

/**
 * 用户Service接口
 */
public interface UserService {
    
    /**
     * 发送短信验证码
     *
     * @param request 短信验证码请求
     * @return 短信验证码响应
     */
    SmsCodeResponse sendSmsCode(SmsCodeRequest request);
    
    /**
     * 短信验证码登录
     *
     * @param request 短信验证码登录请求
     * @return 登录响应
     */
    LoginResponse loginBySms(SmsLoginRequest request);
    
    /**
     * 密码登录
     *
     * @param request 密码登录请求
     * @return 登录响应
     */
    LoginResponse loginByPassword(PasswordLoginRequest request);
    
    /**
     * 刷新令牌
     *
     * @param request 刷新令牌请求
     * @return 刷新令牌响应
     */
    TokenRefreshResponse refreshToken(TokenRefreshRequest request);
    
    /**
     * 退出登录
     *
     * @param userId   用户ID
     * @param token    令牌
     */
    void logout(String userId, String token);
    
    /**
     * 根据ID获取用户信息
     *
     * @param userId 用户ID
     * @return 用户信息
     */
    User getUserById(String userId);
    
    /**
     * 根据手机号获取用户信息
     *
     * @param phone 手机号
     * @return 用户信息
     */
    User getUserByPhone(String phone);
    
    /**
     * 设置用户密码
     *
     * @param request 设置密码请求
     * @return 登录响应
     */
    LoginResponse setPassword(SetPasswordRequest request);
    
    /**
     * 跳过密码设置，使用默认密码
     *
     * @param userId 用户ID
     * @param deviceId 设备ID
     * @param deviceType 设备类型
     * @return 登录响应
     */
    LoginResponse skipSetPassword(String userId, String deviceId, String deviceType);
    
    /**
     * 更新用户信息
     *
     * @param userId 用户ID
     * @param request 用户信息更新请求
     * @return 更新后的用户信息
     */
    User updateUserInfo(String userId, UpdateUserInfoRequest request);
    
    /**
     * 获取当前登录用户信息
     *
     * @return 用户信息DTO
     */
    com.zhisuo.app.dto.response.UserInfoResponse getCurrentUserInfo();
    
    /**
     * 修改用户密码
     *
     * @param userId 用户ID
     * @param request 修改密码请求
     * @return 是否修改成功
     */
    boolean updatePassword(String userId, com.zhisuo.app.dto.request.UpdatePasswordRequest request);

    /**
     * 获取用户统计数据
     *
     * @param userId 用户ID
     * @return 用户统计数据（收藏数、分析数、关注数等）
     */
    Map<String, Object> getUserStats(String userId);

    /**
     * 获取用户兴趣分布
     *
     * @param userId 用户ID
     * @return 用户兴趣分布数据
     */
    Map<String, Object> getUserInterests(String userId);

    /**
     * 获取用户最近分析
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 用户最近分析列表
     */
    Map<String, Object> getUserAnalysis(String userId, Integer page, Integer size);

}