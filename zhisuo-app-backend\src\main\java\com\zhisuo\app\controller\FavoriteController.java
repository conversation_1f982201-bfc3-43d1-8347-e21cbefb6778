package com.zhisuo.app.controller;

import com.zhisuo.app.common.Result;
import com.zhisuo.app.common.context.UserContext;
import com.zhisuo.app.dto.request.FavoriteRequest;
import com.zhisuo.app.service.FavoriteService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 收藏控制器
 */
@RestController
@RequestMapping("/v1/favorites")
public class FavoriteController {
    
    @Autowired
    private FavoriteService favoriteService;
    
    /**
     * 切换收藏状态
     * 如果已收藏则取消收藏，如果未收藏则收藏
     *
     * @param request 收藏请求
     * @return 收藏状态
     */
    @PostMapping
    public Result<Map<String, Object>> toggleFavorite(@RequestBody @Valid FavoriteRequest request) {
        String userId = UserContext.getUserId();

        boolean isFavorited = favoriteService.toggleFavorite(
            request.getContentId(), request.getContentType(), userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("isFavorited", isFavorited);
        result.put("message", isFavorited ? "收藏成功" : "取消收藏成功");
        
        return Result.success(result);
    }
    
    /**
     * 检查用户是否已收藏
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 收藏状态
     */
    @GetMapping("/check")
    public Result<Map<String, Object>> checkFavoriteStatus(
            @RequestParam String contentId,
            @RequestParam String contentType) {
        
        String userId = UserContext.getUserId();

        boolean isFavorited = favoriteService.isFavorited(contentId, contentType, userId);

        Map<String, Object> result = new HashMap<>();
        result.put("isFavorited", isFavorited);

        return Result.success(result);
    }

    /**
     * 获取用户收藏的内容列表
     *
     * @param contentType 内容类型（可选）
     * @param page 页码
     * @param size 每页大小
     * @return 收藏列表
     */
    @GetMapping("/my")
    public Result<Map<String, Object>> getMyFavorites(
            @RequestParam(required = false) String contentType,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {

        String userId = UserContext.getUserId();

        Map<String, Object> result = favoriteService.getUserFavorites(userId, contentType, page, size);

        return Result.success(result);
    }

    /**
     * 批量删除收藏
     *
     * @param favoriteIds 收藏ID列表
     * @return 结果
     */
    @DeleteMapping("/batch")
    public Result<Void> deleteFavorites(@RequestBody java.util.List<String> favoriteIds) {
        String userId = UserContext.getUserId();
        
        favoriteService.deleteFavorites(favoriteIds, userId);
        return Result.success();
    }
}
