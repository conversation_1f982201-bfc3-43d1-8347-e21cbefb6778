package com.zhisuo.app.dto.response;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 评论响应DTO
 */
@Data
public class CommentResponse {
    
    private String commentId;
    private String contentId;
    private String contentType;
    private String userId;
    private String username;
    private String userAvatar;
    private String content;
    private String parentId;
    private Integer likeCount;
    private Date createTime;
    private Integer status;
    
    // 用户相关状态
    private Boolean isLiked; // 当前用户是否已点赞
    
    // 回复相关
    private String replyToUsername; // 回复的用户名（动态计算）
    private List<CommentResponse> replies; // 回复列表
}
