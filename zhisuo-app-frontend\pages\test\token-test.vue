<template>
	<view class="container">
		<view class="header">
			<text class="title">Token测试页面</text>
		</view>
		
		<view class="content">
			<view class="section">
				<text class="section-title">当前Token状态</text>
				<view class="token-info">
					<text class="token-text">{{ tokenInfo }}</text>
				</view>
				<button class="btn" @click="checkToken">检查Token</button>
			</view>
			
			<view class="section">
				<text class="section-title">用户信息测试</text>
				<view class="user-info">
					<text class="info-text">{{ userInfo }}</text>
				</view>
				<button class="btn" @click="getUserInfo">获取用户信息</button>
			</view>
			
			<view class="section">
				<text class="section-title">AI分析保存测试</text>
				<view class="test-result">
					<text class="result-text">{{ testResult }}</text>
				</view>
				<button class="btn" @click="testAnalysisSave">测试分析保存</button>
			</view>
		</view>
	</view>
</template>

<script>
import { generateAnalysisAndSave } from '../../common/api/aiService.js';

export default {
	data() {
		return {
			tokenInfo: '未检查',
			userInfo: '未获取',
			testResult: '未测试'
		};
	},
	
	onLoad() {
		this.checkToken();
	},
	
	methods: {
		checkToken() {
			const token = uni.getStorageSync('token');
			const refreshToken = uni.getStorageSync('refreshToken');
			
			if (token) {
				this.tokenInfo = `Token存在: ${token.substring(0, 30)}...\n长度: ${token.length}`;
				if (refreshToken) {
					this.tokenInfo += `\nRefreshToken存在: ${refreshToken.substring(0, 30)}...`;
				}
			} else {
				this.tokenInfo = 'Token不存在，请先登录';
			}
		},
		
		async getUserInfo() {
			try {
				const token = uni.getStorageSync('token');
				if (!token) {
					this.userInfo = '没有token，无法获取用户信息';
					return;
				}
				
				const response = await fetch('http://localhost:8080/v1/user/info', {
					method: 'GET',
					headers: {
						'Authorization': `Bearer ${token}`,
						'Content-Type': 'application/json'
					}
				});
				
				const result = await response.json();
				
				if (result.code === 0) {
					this.userInfo = `用户ID: ${result.data.userId}\n昵称: ${result.data.nickname}\n手机: ${result.data.phone}`;
				} else {
					this.userInfo = `获取失败: ${result.message}`;
				}
			} catch (error) {
				this.userInfo = `请求失败: ${error.message}`;
			}
		},
		
		async testAnalysisSave() {
			try {
				const token = uni.getStorageSync('token');
				if (!token) {
					this.testResult = '没有token，无法测试';
					return;
				}
				
				this.testResult = '测试中...';
				
				const testData = {
					title: '测试文章标题',
					description: '这是一个测试文章的描述',
					content: '这是测试文章的完整内容，用于验证AI分析保存功能是否正常工作。',
					viewCount: 100,
					likeCount: 10
				};
				
				const result = await generateAnalysisAndSave('article', testData);
				
				this.testResult = `测试成功!\n分析ID: ${result.analysisId}\n已保存: ${result.savedToDatabase}\n摘要: ${result.summary.substring(0, 50)}...`;
				
			} catch (error) {
				this.testResult = `测试失败: ${error.message}`;
				console.error('测试失败详情:', error);
			}
		}
	}
};
</script>

<style scoped>
.container {
	padding: 20rpx;
	background-color: #f5f5f5;
	min-height: 100vh;
}

.header {
	text-align: center;
	margin-bottom: 40rpx;
}

.title {
	font-size: 36rpx;
	font-weight: bold;
	color: #333;
}

.content {
	background-color: white;
	border-radius: 16rpx;
	padding: 30rpx;
}

.section {
	margin-bottom: 40rpx;
	padding-bottom: 30rpx;
	border-bottom: 1rpx solid #eee;
}

.section:last-child {
	border-bottom: none;
	margin-bottom: 0;
}

.section-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333;
	display: block;
	margin-bottom: 20rpx;
}

.token-info, .user-info, .test-result {
	background-color: #f8f9fa;
	padding: 20rpx;
	border-radius: 8rpx;
	margin-bottom: 20rpx;
	min-height: 80rpx;
}

.token-text, .info-text, .result-text {
	font-size: 28rpx;
	color: #666;
	line-height: 1.5;
	word-break: break-all;
}

.btn {
	background-color: #007AFF;
	color: white;
	border: none;
	border-radius: 8rpx;
	padding: 20rpx 40rpx;
	font-size: 28rpx;
}

.btn:active {
	background-color: #0056CC;
}
</style>
