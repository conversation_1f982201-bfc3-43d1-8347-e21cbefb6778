<template>
	<view class="custom-icon" :class="iconClass" :style="iconStyle">
		<uni-icons v-if="useUniIcon" :type="type" :size="size" :color="color"></uni-icons>
		<view v-else class="css-icon" :class="`icon-${type}`"></view>
	</view>
</template>

<script>
export default {
	name: 'CustomIcon',
	props: {
		type: {
			type: String,
			required: true
		},
		size: {
			type: [String, Number],
			default: 16
		},
		color: {
			type: String,
			default: '#999'
		}
	},
	computed: {
		// 定义哪些图标使用CSS实现，哪些使用uni-icons
		useUniIcon() {
			const cssIcons = ['search', 'clear', 'close', 'top', 'bottom'];
			return !cssIcons.includes(this.type);
		},
		iconClass() {
			return {
				'use-uni-icon': this.useUniIcon,
				'use-css-icon': !this.useUniIcon
			};
		},
		iconStyle() {
			return {
				fontSize: this.size + 'rpx',
				color: this.color
			};
		}
	}
};
</script>

<style scoped>
.custom-icon {
	display: inline-flex;
	align-items: center;
	justify-content: center;
}

.css-icon {
	width: 1em;
	height: 1em;
	display: inline-block;
	position: relative;
}

/* CSS图标实现 */
/* 优化的搜索图标CSS实现 */
.icon-search::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 0.7em;
	height: 0.7em;
	border: 2px solid currentColor;
	border-radius: 50%;
}

.icon-search::after {
	content: '';
	position: absolute;
	top: 65%;
	left: 65%;
	transform: translate(-50%, -50%) rotate(45deg);
	width: 2px;
	height: 0.35em;
	background: currentColor;
	border-radius: 1px;
}

.icon-clear::before,
.icon-close::before {
	content: '×';
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	font-size: 1.4em;
	line-height: 1;
	font-weight: 400;
}

.icon-top::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%) rotate(-45deg);
	width: 0.4em;
	height: 0.4em;
	border-top: 2px solid currentColor;
	border-right: 2px solid currentColor;
}

.icon-bottom::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%) rotate(135deg);
	width: 0.4em;
	height: 0.4em;
	border-top: 2px solid currentColor;
	border-right: 2px solid currentColor;
}

.icon-reload::before {
	content: '';
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 0.8em;
	height: 0.8em;
	border: 2px solid currentColor;
	border-radius: 50%;
	border-top-color: transparent;
}

.icon-reload::after {
	content: '';
	position: absolute;
	top: 0.1em;
	left: 50%;
	transform: translateX(-50%);
	width: 0;
	height: 0;
	border-left: 0.15em solid transparent;
	border-right: 0.15em solid transparent;
	border-bottom: 0.25em solid currentColor;
}
</style>
