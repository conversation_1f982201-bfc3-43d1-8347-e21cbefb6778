package com.zhisuo.app.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 评论创建请求DTO
 */
@Data
public class CommentCreateRequest {
    
    @NotBlank(message = "内容ID不能为空")
    private String contentId;
    
    @NotBlank(message = "内容类型不能为空")
    private String contentType;
    
    @NotBlank(message = "评论内容不能为空")
    @Size(max = 1000, message = "评论内容不能超过1000个字符")
    private String content;
    
    private String parentId; // 父评论ID，回复时使用
}
