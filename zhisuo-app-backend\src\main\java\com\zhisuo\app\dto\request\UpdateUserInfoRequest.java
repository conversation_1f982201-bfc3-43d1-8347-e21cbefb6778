package com.zhisuo.app.dto.request;

import javax.validation.constraints.Size;

public class UpdateUserInfoRequest {

    @Size(max = 12, message = "昵称不能超过12个字符")
    private String nickname;
    
    private String avatar;
    
    public String getNickname() {
        return nickname;
    }
    
    public void setNickname(String nickname) {
        this.nickname = nickname;
    }
    
    public String getAvatar() {
        return avatar;
    }
    
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
} 