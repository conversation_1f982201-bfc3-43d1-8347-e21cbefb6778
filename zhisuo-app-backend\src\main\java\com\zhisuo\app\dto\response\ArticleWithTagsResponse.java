package com.zhisuo.app.dto.response;

import com.zhisuo.app.entity.Article;
import com.zhisuo.app.entity.Tag;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 包含标签的文章响应DTO
 */
@Data
public class ArticleWithTagsResponse {
    
    private String articleId;      // 文章ID
    private String title;          // 文章标题
    private String description;    // 文章描述
    private String content;        // 文章内容
    private String coverImage;     // 封面图URL
    private String source;         // 内容来源
    private String sourceUrl;      // 来源URL
    private String iconUrl;        // 图标URL
    private Integer viewCount;     // 阅读量
    private Integer commentCount;  // 评论量
    private Integer likeCount;     // 点赞量
    private Date collectTime;      // 收集时间
    private Date publishTime;      // 发布时间
    private Date createTime;       // 创建时间
    private Date updateTime;       // 更新时间
    private Integer status;        // 状态
    
    // 标签信息
    private List<String> tags;     // 标签名称列表
    private List<Tag> tagDetails;  // 标签详细信息列表（可选）
    
    /**
     * 从Article实体转换为响应DTO
     * 
     * @param article 文章实体
     * @return 响应DTO
     */
    public static ArticleWithTagsResponse fromEntity(Article article) {
        if (article == null) {
            return null;
        }
        
        ArticleWithTagsResponse response = new ArticleWithTagsResponse();
        response.setArticleId(article.getArticleId());
        response.setTitle(article.getTitle());
        response.setDescription(article.getDescription());
        response.setContent(article.getContent());
        response.setCoverImage(article.getCoverImage());
        response.setSource(article.getSource());
        response.setSourceUrl(article.getSourceUrl());
        response.setIconUrl(article.getIconUrl());
        response.setViewCount(article.getViewCount());
        response.setCommentCount(article.getCommentCount());
        response.setLikeCount(article.getLikeCount());
        response.setCollectTime(article.getCollectTime());
        response.setPublishTime(article.getPublishTime());
        response.setCreateTime(article.getCreateTime());
        response.setUpdateTime(article.getUpdateTime());
        response.setStatus(article.getStatus());
        
        return response;
    }
    
    /**
     * 从Article实体和标签列表转换为响应DTO
     * 
     * @param article 文章实体
     * @param tags 标签列表
     * @return 响应DTO
     */
    public static ArticleWithTagsResponse fromEntityWithTags(Article article, List<Tag> tags) {
        ArticleWithTagsResponse response = fromEntity(article);
        if (response != null && tags != null) {
            response.setTagDetails(tags);
            response.setTags(tags.stream()
                    .map(Tag::getTagName)
                    .collect(java.util.stream.Collectors.toList()));
        }
        return response;
    }
}
