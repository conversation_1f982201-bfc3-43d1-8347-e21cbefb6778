package com.zhisuo.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 评论实体类
 */
@Data
@TableName("comments")
public class Comment {
    
    @TableId
    private String commentId;    // 评论ID
    
    private String contentId;    // 内容ID
    
    private String contentType;  // 内容类型(article/topic)
    
    private String userId;       // 用户ID
    
    private String content;      // 评论内容
    
    private String parentId;     // 父评论ID(回复)

    private Integer likeCount;   // 点赞数
    
    private Date createTime;     // 评论时间
    
    private Integer status;      // 状态(1:显示,0:隐藏)
} 