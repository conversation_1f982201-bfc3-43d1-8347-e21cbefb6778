package com.zhisuo.app.controller;

import com.zhisuo.app.common.Result;
import com.zhisuo.app.common.context.UserContext;
import com.zhisuo.app.dto.request.AIAnalysisRequest;
import com.zhisuo.app.dto.request.AIChatRequest;
import com.zhisuo.app.dto.response.AIAnalysisResponse;
import com.zhisuo.app.dto.response.AIChatResponse;
import com.zhisuo.app.entity.UserAnalysis;
import com.zhisuo.app.mapper.UserAnalysisMapper;
import com.zhisuo.app.service.AIService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Date;

/**
 * AI功能控制器
 */
@Slf4j
@RestController
@RequestMapping("/v1/ai")
public class AIController {
    
    @Autowired
    private AIService aiService;

    @Autowired
    private UserAnalysisMapper userAnalysisMapper;
    
    /**
     * AI对话
     */
    @PostMapping("/chat")
    public Result<AIChatResponse> chat(@Valid @RequestBody AIChatRequest request) {
        log.info("AI对话请求: {}", request.getMessage());
        
        try {
            AIChatResponse response = aiService.chat(request);
            log.info("AI对话响应: {}", response.getReply());
            return Result.success(response);
        } catch (Exception e) {
            log.error("AI对话异常: {}", e.getMessage(), e);
            return Result.error("AI对话服务异常: " + e.getMessage());
        }
    }
    
    /**
     * AI内容分析
     */
    @PostMapping("/analyze")
    public Result<AIAnalysisResponse> analyze(@Valid @RequestBody AIAnalysisRequest request) {
        log.info("AI分析请求: pageType={}, title={}",
                request.getPageType(),
                request.getContentData() != null ? request.getContentData().getTitle() : "无");

        try {
            AIAnalysisResponse response = aiService.analyze(request);
            log.info("AI分析响应: summary={}", response.getSummary());
            return Result.success(response);
        } catch (Exception e) {
            log.error("AI分析异常: {}", e.getMessage(), e);
            return Result.error("AI分析服务异常: " + e.getMessage());
        }
    }


    
    /**
     * 获取分析详情
     */
    @GetMapping("/analysis/{analysisId}")
    public Result<UserAnalysis> getAnalysisDetail(@PathVariable String analysisId) {
        log.info("获取分析详情请求: analysisId={}", analysisId);

        try {
            UserAnalysis analysis = userAnalysisMapper.selectByAnalysisId(analysisId);
            if (analysis == null) {
                return Result.error("分析记录不存在");
            }

            log.info("获取分析详情成功: title={}", analysis.getTitle());
            return Result.success(analysis);
        } catch (Exception e) {
            log.error("获取分析详情异常: {}", e.getMessage(), e);
            return Result.error("获取分析详情失败: " + e.getMessage());
        }
    }

    /**
     * 删除分析记录
     */
    @DeleteMapping("/analysis/{analysisId}")
    public Result<Void> deleteAnalysis(@PathVariable String analysisId) {
        log.info("删除分析记录请求: analysisId={}", analysisId);

        try {
            // 从认证拦截器设置的上下文中获取用户ID
            String userId = UserContext.getUserId();
            if (!StringUtils.hasText(userId)) {
                return Result.error("用户未登录，无法删除分析记录");
            }

            // 获取分析记录
            UserAnalysis analysis = userAnalysisMapper.selectByAnalysisId(analysisId);
            if (analysis == null) {
                return Result.error("分析记录不存在");
            }

            // 验证权限：只能删除自己的分析记录
            if (!userId.equals(analysis.getUserId())) {
                return Result.error("无权删除此分析记录");
            }

            // 软删除：将status设置为0
            analysis.setStatus(0);
            analysis.setUpdateTime(new Date());
            userAnalysisMapper.updateById(analysis);

            log.info("删除分析记录成功: analysisId={}, userId={}, title={}",
                    analysisId, userId, analysis.getTitle());
            return Result.success();
        } catch (Exception e) {
            log.error("删除分析记录异常: {}", e.getMessage(), e);
            return Result.error("删除分析记录失败: " + e.getMessage());
        }
    }

    /**
     * 保存已有的分析结果到数据库
     */
    @PostMapping("/save-analysis")
    public Result<AIAnalysisResponse> saveAnalysis(@Valid @RequestBody AIAnalysisRequest request) {
        log.info("保存分析结果请求: pageType={}, title={}, userId={}",
                request.getPageType(),
                request.getContentData() != null ? request.getContentData().getTitle() : "无",
                request.getUserId());

        try {
            // 从认证拦截器设置的上下文中获取用户ID
            String userId = UserContext.getUserId();
            if (!StringUtils.hasText(userId)) {
                return Result.error("用户未登录，无法保存分析结果");
            }

            // 设置用户ID到请求中
            request.setUserId(userId);

            AIAnalysisResponse response = aiService.saveAnalysisResult(request);
            log.info("保存分析结果响应: analysisId={}", response.getAnalysisId());
            return Result.success(response);
        } catch (Exception e) {
            log.error("保存分析结果异常: {}", e.getMessage(), e);
            return Result.error("保存分析结果服务异常: " + e.getMessage());
        }
    }

    /**
     * 健康检查
     */
    @GetMapping("/health")
    public Result<String> health() {
        return Result.success("AI服务运行正常");
    }
}
