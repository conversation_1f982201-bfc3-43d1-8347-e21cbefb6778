<template>
	<view class="page">
		<view class="title">
			<view>Hello！<br>欢迎来到智索</view>
		</view>

		<view class="form">
			<view class="tab">
				<view :class="loginType==1? 'select type1' : 'type1'" @click="loginType=1">快捷登录</view>
				<view :class="loginType==2? 'select type2' : 'type2'" @click="loginType=2">账号登录</view>
			</view>
			<view class="inputs_button">
				<form class="inputs">
					<view class="account">
						<input type="number" v-model="account" :adjust-position="false" placeholder="手机号" />
					</view>
					<view class="password" v-if="loginType == 2">
						<input :type="pwdShow ? 'text' : 'password'" v-model="password" :adjust-position="false"
							placeholder="密码" />
						<uni-icons :type="pwdShow ? 'eye-filled' : 'eye-slash-filled'" size="24" color="#cccccc"
							@click="pwdShow = !pwdShow"></uni-icons>
					</view>
					<view class="vcode" v-if="loginType == 1">
						<input type="number" v-model="vcode" :adjust-position="false" placeholder="验证码" />
						<text v-if="vcodeTime == 0" style="color: #3264ed" @click="getVcode">获取验证码</text>
						<text v-else style="color: #ccc">重新获取({{ vcodeTime }}s)</text>
					</view>
				</form>
				<view class="button">
					<view @click="login">
						登录
					</view>
				</view>
			</view>
		</view>
		<view class="pact">
			<radio :checked="pactChecked" :disabled="!canCheck" activeBackgroundColor="#3264ed"
				activeBorderColor="#3264ed" borderColor="#3264ed" color="#fff" @click="pactChange" />
			<view>我已阅读并同意<text @click="openAgreement('user')">《用户协议》</text>和<text
					@click="openAgreement('privacy')">《隐私协议》</text></view>
		</view>

		<!-- 协议弹窗 -->
		<agreement-popup :show="showAgreement" :title="agreementTitle" :content="agreementContent"
			:confirm-text="'我已阅读并同意'" :require-scroll="true" width="90%" height="75%" @confirm="agreementConfirm"
			@cancel="agreementCancel"></agreement-popup>
	</view>
</template>

<script>
	import agreement from '../../common/agreement.js';
	import AgreementPopup from '../../components/AgreementPopup.vue';
	import Api from '../../common/api.js';

	export default {
		components: {
			AgreementPopup
		},
		data() {
			return {
				account: "",
				password: "",
				vcode: "",

				loginType: 1,

				pwdShow: false,
				pactChecked: false,
				canCheck: false, // 是否可以勾选协议

				vcodeTime: 0,
				vcodeTimer: null,

				// 协议弹窗相关
				showAgreement: false,
				agreementTitle: '',
				agreementContent: '',
				currentAgreementType: '',

				// 协议阅读状态
				userAgreementRead: false,
				privacyAgreementRead: false,

				// 设备信息
				deviceId: '',
				deviceType: ''
			}
		},
		onLoad() {
			// 初始化设备信息
			this.deviceId = uni.getSystemInfoSync().deviceId || `device_${Date.now()}`;
			this.deviceType = uni.getSystemInfoSync().platform || 'unknown';

			// 开发环境提示
			if (process.env.NODE_ENV === 'development') {
				console.log('当前API地址:', Api.BASE_URL);
			}
		},
		methods: {
			login() {
				if (!this.pactChecked) {
					uni.showToast({
						title: '请阅读并同意用户协议和隐私协议',
						icon: 'none'
					});
					return;
				}

				if (this.loginType === 1) { // 快捷登录
					if (!this.account || !this.vcode) {
						uni.showToast({
							title: '请输入手机号和验证码',
							icon: 'none'
						});
						return;
					}
					this.handleSmsLogin();
				} else { // 账号密码登录
					if (!this.account || !this.password) {
						uni.showToast({
							title: '请输入手机号和密码',
							icon: 'none'
						});
						return;
					}
					this.handlePasswordLogin();
				}
			},

			// 短信验证码登录
			handleSmsLogin() {
				uni.showLoading({
					title: '正在登录',
					mask: true
				});

				Api.request({
					url: '/v1/auth/login/sms',
					method: 'POST',
					data: {
						phone: this.account,
						code: this.vcode,
						deviceId: this.deviceId,
						deviceType: this.deviceType
					}
				}).then(res => {
					uni.hideLoading();
					if (res.data && res.data.code === 0) {
						const data = res.data.data;
						// 保存token到本地存储
						uni.setStorageSync('token', data.token);
						uni.setStorageSync('refreshToken', data.refreshToken);
						uni.setStorageSync('userInfo', data.userInfo);

						uni.showToast({
							title: '登录成功',
							icon: 'success'
						});

						// 如果需要设置密码，跳转到密码设置页面
						if (data.needSetPassword) {
							uni.navigateTo({
								url: `/pages/login/setPassword?userId=${data.userInfo.userId}`
							});
						} else {
							// 否则直接跳转到首页
							uni.switchTab({
								url: '/pages/index/index'
							});
						}
					} else {
						uni.showToast({
							title: res.data?.message || '登录失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('登录失败:', err);
					uni.showToast({
						title: '网络异常，请稍后再试',
						icon: 'none'
					});
				});
			},

			// 密码登录
			handlePasswordLogin() {
				uni.showLoading({
					title: '正在登录',
					mask: true
				});

				Api.request({
					url: '/v1/auth/login/password',
					method: 'POST',
					data: {
						phone: this.account,
						password: this.password,
						deviceId: this.deviceId,
						deviceType: this.deviceType
					}
				}).then(res => {
					uni.hideLoading();
					if (res.data && res.data.code === 0) {
						const data = res.data.data;
						// 保存token到本地存储
						uni.setStorageSync('token', data.token);
						uni.setStorageSync('refreshToken', data.refreshToken);
						uni.setStorageSync('userInfo', data.userInfo);

						uni.showToast({
							title: '登录成功',
							icon: 'success'
						});

						// 跳转到首页
						uni.switchTab({
							url: '/pages/index/index'
						});
					} else {
						uni.showToast({
							title: res.data?.message || '账号或密码错误',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('登录失败:', err);
					uni.showToast({
						title: '网络异常，请稍后再试',
						icon: 'none'
					});
				});
			},

			getVcode() {
				if (!this.account || !/^1\d{10}$/.test(this.account)) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}

				Api.request({
					url: '/v1/auth/sms/send',
					method: 'POST',
					data: {
						phone: this.account,
						type: 'login'
					}
				}).then(res => {
					if (res.data && res.data.code === 0) {
						this.vcodeTime = res.data.data.expireTime || 60;
						this.startVcodeTimer();

						uni.showToast({
							title: '验证码已发送',
							icon: 'success'
						});
					} else {
						uni.showToast({
							title: res.data?.message || '验证码发送失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					console.error('发送验证码失败:', err);
					uni.showToast({
						title: '网络异常，请稍后再试',
						icon: 'none'
					});
				});
			},

			startVcodeTimer() {
				this.vcodeTimer = setInterval(() => {
					if (this.vcodeTime > 0) {
						this.vcodeTime--;
					} else {
						clearInterval(this.vcodeTimer);
						this.vcodeTimer = null;
					}
				}, 1000);
			},

			pactChange() {
				if (this.canCheck) {
					this.pactChecked = !this.pactChecked;
				} else {
					uni.showToast({
						title: '请先阅读并同意用户协议和隐私协议',
						icon: 'none'
					});
				}
			},

			openAgreement(type) {
				this.currentAgreementType = type;
				this.agreementTitle = type === 'user' ? '用户协议' : '隐私协议';
				this.agreementContent = type === 'user' ? agreement.userAgreement : agreement.privacyAgreement;
				this.showAgreement = true;
			},

			agreementConfirm() {
				// 标记当前协议已读
				if (this.currentAgreementType === 'user') {
					this.userAgreementRead = true;
				} else {
					this.privacyAgreementRead = true;
				}

				// 检查是否两个协议都已读
				if (this.userAgreementRead && this.privacyAgreementRead) {
					this.canCheck = true;
					this.pactChecked = true;
				}

				this.showAgreement = false;
			},

			agreementCancel() {
				this.showAgreement = false;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		min-height: 100vh;
		background: url("../../static/images/pageBg.png") no-repeat top center;
		background-size: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;

		.title {
			padding-top: 220rpx;
			padding-bottom: 70rpx;

			view {
				padding-left: 64rpx;
				font-size: 48rpx;
				font-weight: 700;
				color: #383838;
			}
		}

		.form {
			flex: 1;
			margin: 0 32rpx;
			border-radius: 40rpx;
			background-color: #fff;
			overflow: hidden;

			.tab {
				display: flex;
				align-items: center;
				justify-content: space-between;
				background-color: #ecf1fb;
				height: 100rpx;
				background: linear-gradient(180deg, #e3ebf9 0%, #ffffff 100%);

				view {
					width: 340rpx;
					line-height: 100rpx;
					text-align: center;
					color: #9e9e9e;
					position: relative;
				}

				view::after {
					content: '';
					width: 50rpx;
					height: 8rpx;
					display: block;
					background-color: #3264ed;
					border-radius: 4rpx;
					position: absolute;
					left: 50%;
					bottom: 14rpx;
					transform: translateX(-50%);
					opacity: 0;
				}

				view.select {
					background-color: #fff;
					color: #3264ed;
				}

				view.type1.select {
					border-radius: 0 40rpx 0 0;
				}

				view.type2.select {
					border-radius: 40rpx 0 0;
				}

				view.select::after {
					opacity: 1;
				}
			}

			.inputs_button {
				background-color: #fff;

				.inputs {
					padding: 150rpx 32rpx 0;
					margin-bottom: 80rpx;

					.account,
					.password,
					.vcode {
						height: 96rpx;
						border-radius: 20rpx;
						padding: 0 48rpx;
						display: flex;
						align-items: center;
						background-color: #f7fafc;

						input {
							flex: 1;
						}
					}

					.account {
						margin-bottom: 48rpx;
					}

					.vcode {
						text {
							text-wrap: nowrap;
							font-size: 26rpx;
							background-color: #fff;
							padding: 14rpx 30rpx;
							border-radius: 12rpx;
						}
					}
				}

				.button {
					padding: 0 32rpx;

					view {
						line-height: 96rpx;
						border-radius: 20rpx;
						text-align: center;
						font-size: 32rpx;
						background-color: #3264ed;
						color: #fff;
					}
				}
			}

		}

		.pact {
			display: flex;
			align-items: center;
			justify-content: center;
			padding-bottom: 120rpx;
			font-size: 24rpx;

			text {
				color: #3264ed;
			}

			radio {
				:deep(.uni-radio-input) {
					border: 1rpx solid #3264ed;
				}

				transform:scale(0.6)
			}
		}
	}
</style>