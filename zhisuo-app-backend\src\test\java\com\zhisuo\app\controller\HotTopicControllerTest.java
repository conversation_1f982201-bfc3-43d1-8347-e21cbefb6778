package com.zhisuo.app.controller;

import com.zhisuo.app.dto.response.HotTopicWithTagsResponse;
import com.zhisuo.app.entity.HotTopic;
import com.zhisuo.app.entity.Tag;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 热点话题DTO转换测试类
 */
class HotTopicWithTagsResponseTest {

    private HotTopic mockHotTopic;
    private List<Tag> mockTags;

    @BeforeEach
    void setUp() {
        // 创建模拟的热点话题
        mockHotTopic = new HotTopic();
        mockHotTopic.setTopicId("test-topic-id");
        mockHotTopic.setTitle("测试热点话题");
        mockHotTopic.setDescription("这是一个测试热点话题");
        mockHotTopic.setSource("测试来源");
        mockHotTopic.setSourceUrl("https://test.com");
        mockHotTopic.setHotValue("1000");
        mockHotTopic.setViewCount(100);
        mockHotTopic.setSearchCount(50);
        mockHotTopic.setTrend(1);
        mockHotTopic.setRank(1);
        mockHotTopic.setCollectTime(new Date());
        mockHotTopic.setUpdateTime(new Date());
        mockHotTopic.setStatus(1);

        // 创建模拟的标签
        Tag tag1 = new Tag();
        tag1.setTagId("tag1");
        tag1.setTagName("科技");

        Tag tag2 = new Tag();
        tag2.setTagId("tag2");
        tag2.setTagName("热点");

        mockTags = Arrays.asList(tag1, tag2);
    }

    @Test
    void testFromEntityWithTagsAndFavorite_NotFavorited() {
        // 测试DTO转换功能 - 未收藏状态
        HotTopicWithTagsResponse response = HotTopicWithTagsResponse.fromEntityWithTagsAndFavorite(
            mockHotTopic, mockTags, false);

        // 验证结果
        assertNotNull(response);
        assertEquals("test-topic-id", response.getTopicId());
        assertEquals("测试热点话题", response.getTitle());
        assertEquals("这是一个测试热点话题", response.getDescription());
        assertEquals("测试来源", response.getSource());
        assertEquals("https://test.com", response.getSourceUrl());
        assertEquals("1000", response.getHotValue());
        assertEquals(100, response.getViewCount());
        assertEquals(50, response.getSearchCount());
        assertEquals(1, response.getTrend());
        assertEquals(1, response.getRank());
        assertEquals(1, response.getStatus());
        assertNotNull(response.getCollectTime());
        assertNotNull(response.getUpdateTime());

        // 验证标签信息
        assertNotNull(response.getTags());
        assertEquals(2, response.getTags().size());
        assertTrue(response.getTags().contains("科技"));
        assertTrue(response.getTags().contains("热点"));

        // 验证收藏状态
        assertEquals(false, response.getIsFavorited());
    }

    @Test
    void testFromEntityWithTagsAndFavorite_Favorited() {
        // 测试DTO转换功能 - 已收藏状态
        HotTopicWithTagsResponse response = HotTopicWithTagsResponse.fromEntityWithTagsAndFavorite(
            mockHotTopic, mockTags, true);

        // 验证结果
        assertNotNull(response);
        assertEquals("test-topic-id", response.getTopicId());
        assertEquals("测试热点话题", response.getTitle());

        // 验证标签信息
        assertNotNull(response.getTags());
        assertEquals(2, response.getTags().size());

        // 验证收藏状态
        assertEquals(true, response.getIsFavorited());
    }

    @Test
    void testFromEntityWithTagsAndFavorite_NullFavoriteStatus() {
        // 测试DTO转换功能 - null收藏状态
        HotTopicWithTagsResponse response = HotTopicWithTagsResponse.fromEntityWithTagsAndFavorite(
            mockHotTopic, mockTags, null);

        // 验证结果
        assertNotNull(response);
        assertEquals("test-topic-id", response.getTopicId());
        assertEquals("测试热点话题", response.getTitle());

        // 验证标签信息
        assertNotNull(response.getTags());
        assertEquals(2, response.getTags().size());

        // 验证收藏状态
        assertNull(response.getIsFavorited());
    }

    @Test
    void testFromEntityWithTagsAndFavorite_NullHotTopic() {
        // 测试DTO转换功能 - null热点话题
        HotTopicWithTagsResponse response = HotTopicWithTagsResponse.fromEntityWithTagsAndFavorite(
            null, mockTags, true);

        // 验证结果
        assertNull(response);
    }
}
