package com.zhisuo.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhisuo.app.entity.UserFavorite;

import java.util.List;
import java.util.Map;

/**
 * 收藏服务接口
 */
public interface FavoriteService extends IService<UserFavorite> {
    
    /**
     * 切换收藏状态
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param userId 用户ID
     * @return 收藏后的状态（true=已收藏，false=已取消收藏）
     */
    boolean toggleFavorite(String contentId, String contentType, String userId);
    
    /**
     * 检查用户是否已收藏
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param userId 用户ID
     * @return 是否已收藏
     */
    boolean isFavorited(String contentId, String contentType, String userId);
    
    /**
     * 获取用户收藏的内容列表
     *
     * @param userId 用户ID
     * @param contentType 内容类型（可选）
     * @param page 页码
     * @param size 每页大小
     * @return 收藏内容列表
     */
    Map<String, Object> getUserFavorites(String userId, String contentType, Integer page, Integer size);
    
    /**
     * 批量删除收藏
     *
     * @param favoriteIds 收藏ID列表
     * @param userId 用户ID
     */
    void deleteFavorites(List<String> favoriteIds, String userId);
}
