package com.zhisuo.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhisuo.app.entity.UserAnalysis;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.Date;
import java.util.Map;

/**
 * 用户分析Mapper接口
 */
@Mapper
public interface UserAnalysisMapper extends BaseMapper<UserAnalysis> {
    
    /**
     * 获取用户分析列表（分页）
     *
     * @param page 分页对象
     * @param userId 用户ID
     * @return 用户分析列表
     */
    @Select("SELECT analysis_id, title, description, content, create_time " +
            "FROM user_analysis WHERE user_id = #{userId} AND status = 1 " +
            "ORDER BY create_time DESC")
    Page<Map<String, Object>> selectUserAnalysis(Page<Map<String, Object>> page, @Param("userId") String userId);

    /**
     * 根据分析ID获取详细分析结果
     *
     * @param analysisId 分析ID
     * @return 分析详情
     */
    @Select("SELECT * FROM user_analysis WHERE analysis_id = #{analysisId} AND status = 1")
    UserAnalysis selectByAnalysisId(@Param("analysisId") String analysisId);
    
    /**
     * 统计用户分析数量
     *
     * @param userId 用户ID
     * @return 分析数量
     */
    @Select("SELECT COUNT(*) FROM user_analysis WHERE user_id = #{userId} AND status = 1")
    long countByUserId(@Param("userId") String userId);

    /**
     * 根据用户ID和文章ID查询分析记录
     *
     * @param userId 用户ID
     * @param articleId 文章ID
     * @return 分析记录
     */
    @Select("SELECT * FROM user_analysis WHERE user_id = #{userId} AND article_id = #{articleId} AND status = 1")
    UserAnalysis selectByUserIdAndArticleId(@Param("userId") String userId, @Param("articleId") String articleId);

    /**
     * 更新分析记录的核心字段
     *
     * @param analysisId 分析ID
     * @param title 标题
     * @param description 描述
     * @param content 内容
     * @param updateTime 更新时间
     * @return 更新行数
     */
    @Update("UPDATE user_analysis SET title = #{title}, description = #{description}, content = #{content}, update_time = #{updateTime} WHERE analysis_id = #{analysisId}")
    int updateAnalysisContent(@Param("analysisId") String analysisId,
                             @Param("title") String title,
                             @Param("description") String description,
                             @Param("content") String content,
                             @Param("updateTime") Date updateTime);
}
