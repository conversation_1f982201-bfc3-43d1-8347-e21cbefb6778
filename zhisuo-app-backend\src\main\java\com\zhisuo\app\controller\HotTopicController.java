package com.zhisuo.app.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhisuo.app.common.Result;
import com.zhisuo.app.common.context.UserContext;
import com.zhisuo.app.dto.response.HotTopicWithTagsResponse;
import com.zhisuo.app.entity.HotTopic;
import com.zhisuo.app.entity.Tag;
import com.zhisuo.app.service.ContentTagService;
import com.zhisuo.app.service.FavoriteService;
import com.zhisuo.app.service.HotTopicService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 热点话题控制器
 */
@RestController
@RequestMapping("/v1/hot-topics")
public class HotTopicController {
    
    @Autowired
    private HotTopicService hotTopicService;

    @Autowired
    private ContentTagService contentTagService;

    @Autowired
    private FavoriteService favoriteService;
    
    /**
     * 获取热点话题列表
     *
     * @param page 页码，默认0
     * @param size 每页大小，默认20
     * @param source 来源平台筛选
     * @return 热点话题列表（分页）
     */
    @GetMapping
    public Result<?> getHotTopics(
            @RequestParam(required = false, defaultValue = "0") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer size,
            @RequestParam(required = false) String source) {

        // 创建分页对象，注意page+1是因为前端从0开始，后端从1开始
        Page<HotTopic> pageRequest = new Page<>(page + 1, size);
        Page<HotTopic> pageResult = hotTopicService.getHotTopicPage(pageRequest, source);

        // 为每个热点话题添加标签信息
        List<HotTopicWithTagsResponse> contentWithTags = pageResult.getRecords().stream()
                .map(hotTopic -> {
                    // 获取标签
                    List<Tag> tags = contentTagService.getContentTags(hotTopic.getTopicId(), "topic");
                    return HotTopicWithTagsResponse.fromEntityWithTags(hotTopic, tags);
                })
                .collect(Collectors.toList());

        // 构建符合接口文档的返回结构
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("content", contentWithTags);
        resultMap.put("totalElements", pageResult.getTotal());
        resultMap.put("totalPages", pageResult.getPages());
        resultMap.put("size", pageResult.getSize());
        resultMap.put("number", page);

        return Result.success(resultMap);
    }
    
    /**
     * 获取今日热点话题列表
     *
     * @param page 页码，默认0
     * @param size 每页大小，默认20
     * @param source 来源平台筛选
     * @return 今日热点话题列表（分页）
     */
    @GetMapping("/today")
    public Result<?> getTodayHotTopics(
            @RequestParam(required = false, defaultValue = "0") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer size,
            @RequestParam(required = false) String source) {

        // 创建分页对象，注意page+1是因为前端从0开始，后端从1开始
        Page<HotTopic> pageRequest = new Page<>(page + 1, size);
        Page<HotTopic> pageResult = hotTopicService.getTodayHotTopicPage(pageRequest, source);

        // 为每个热点话题添加标签信息
        List<HotTopicWithTagsResponse> contentWithTags = pageResult.getRecords().stream()
                .map(hotTopic -> {
                    // 获取标签
                    List<Tag> tags = contentTagService.getContentTags(hotTopic.getTopicId(), "topic");
                    return HotTopicWithTagsResponse.fromEntityWithTags(hotTopic, tags);
                })
                .collect(Collectors.toList());

        // 构建符合接口文档的返回结构
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("content", contentWithTags);
        resultMap.put("totalElements", pageResult.getTotal());
        resultMap.put("totalPages", pageResult.getPages());
        resultMap.put("size", pageResult.getSize());
        resultMap.put("number", page);

        return Result.success(resultMap);
    }
    
    /**
     * 获取昨日热点话题列表
     *
     * @param page 页码，默认0
     * @param size 每页大小，默认20
     * @param source 来源平台筛选
     * @return 昨日热点话题列表（分页）
     */
    @GetMapping("/yesterday")
    public Result<?> getYesterdayHotTopics(
            @RequestParam(required = false, defaultValue = "0") Integer page,
            @RequestParam(required = false, defaultValue = "20") Integer size,
            @RequestParam(required = false) String source) {

        // 创建分页对象，注意page+1是因为前端从0开始，后端从1开始
        Page<HotTopic> pageRequest = new Page<>(page + 1, size);
        Page<HotTopic> pageResult = hotTopicService.getYesterdayHotTopicPage(pageRequest, source);

        // 为每个热点话题添加标签信息
        List<HotTopicWithTagsResponse> contentWithTags = pageResult.getRecords().stream()
                .map(hotTopic -> {
                    // 获取标签
                    List<Tag> tags = contentTagService.getContentTags(hotTopic.getTopicId(), "topic");
                    return HotTopicWithTagsResponse.fromEntityWithTags(hotTopic, tags);
                })
                .collect(Collectors.toList());

        // 构建符合接口文档的返回结构
        Map<String, Object> resultMap = new HashMap<>();
        resultMap.put("content", contentWithTags);
        resultMap.put("totalElements", pageResult.getTotal());
        resultMap.put("totalPages", pageResult.getPages());
        resultMap.put("size", pageResult.getSize());
        resultMap.put("number", page);

        return Result.success(resultMap);
    }
    
    /**
     * 获取热点话题详情
     *
     * @param topicId 话题ID
     * @return 热点话题详情
     */
    @GetMapping("/{topicId}")
    public Result<HotTopicWithTagsResponse> getHotTopicDetail(@PathVariable String topicId) {
        HotTopic hotTopic = hotTopicService.getById(topicId);
        if (hotTopic == null) {
            return Result.error(404, "话题不存在");
        }

        // 获取标签信息
        List<Tag> tags = contentTagService.getContentTags(topicId, "topic");

        // 获取用户收藏状态
        String userId = UserContext.getUserId();
        Boolean isFavorited = false;
        if (userId != null) {
            isFavorited = favoriteService.isFavorited(topicId, "topic", userId);
        }

        HotTopicWithTagsResponse response = HotTopicWithTagsResponse.fromEntityWithTagsAndFavorite(hotTopic, tags, isFavorited);

        return Result.success(response);
    }
    
    /**
     * 更新热点话题的浏览量
     *
     * @param topicId 话题ID
     * @return 结果
     */
    @PostMapping("/{topicId}/view")
    public Result<Void> updateViewCount(@PathVariable String topicId) {
        // 更新浏览量
        hotTopicService.updateViewCount(topicId);
        return Result.success();
    }
    
    /**
     * 记录热点话题的搜索行为
     *
     * @param topicId 话题ID
     * @return 结果
     */
    @PostMapping("/{topicId}/search")
    public Result<Void> recordSearch(@PathVariable String topicId) {
        // 更新搜索量
        hotTopicService.updateSearchCount(topicId);
        return Result.success();
    }
    
    /**
     * 手动触发热点话题更新
     *
     * @return 结果
     */
    @PostMapping("/refresh")
    public Result<Void> refreshHotTopics() {
        hotTopicService.fetchAndUpdateHotTopics();
        return Result.success();
    }
    
    /**
     * 手动触发昨日热点话题更新
     *
     * @return 结果
     */
    @PostMapping("/refresh-yesterday")
    public Result<Void> refreshYesterdayHotTopics() {
        hotTopicService.fetchAndUpdateYesterdayHotTopics();
        return Result.success();
    }
    
    /**
     * 手动刷新热点话题排名
     * 根据热度值重新计算排名
     *
     * @return 结果
     */
    @PostMapping("/refresh-ranks")
    public Result<Void> refreshHotTopicRanks() {
        hotTopicService.refreshHotTopicRanks();
        return Result.success();
    }
    
    /**
     * 手动刷新热点话题趋势
     * 根据历史热度值重新计算趋势
     *
     * @return 结果
     */
    @PostMapping("/refresh-trends")
    public Result<Void> refreshHotTopicTrends() {
        // 调用更新热度值历史记录的方法，同时会更新趋势
        hotTopicService.updateHotValueHistory();
        return Result.success();
    }
    
    /**
     * 手动刷新昨日热点话题趋势
     * 根据历史热度值重新计算趋势
     *
     * @return 结果
     */
    @PostMapping("/refresh-yesterday-trends")
    public Result<Void> refreshYesterdayHotTopicTrends() {
        // 调用更新昨日热度值历史记录的方法，同时会更新趋势
        hotTopicService.updateYesterdayHotValueHistory();
        return Result.success();
    }
    
    /**
     * 获取热点话题标签
     *
     * @param topicId 话题ID
     * @return 标签列表
     */
    @GetMapping("/{topicId}/tags")
    public Result<List<Tag>> getHotTopicTags(@PathVariable String topicId) {
        // 验证话题是否存在
        HotTopic hotTopic = hotTopicService.getById(topicId);
        if (hotTopic == null) {
            return Result.error(404, "话题不存在");
        }
        
        // 获取话题标签
        List<Tag> tags = contentTagService.getContentTags(topicId, "topic");
        return Result.success(tags);
    }
} 