<template>
	<view class="page">
		<!-- 渐变背景顶部 -->
		<view class="gradient-header">
			<view class="header-content">
				<view class="back-btn" @click="goBack">
					<uni-icons type="back" size="18" color="#FFFFFF"></uni-icons>
					<text>返回</text>
				</view>
				<view class="title">热点详情</view>
				<view class="placeholder"></view>
			</view>
		</view>
		
		<view class="content-container">
			<view v-if="loading" class="loading">
				<view class="loading-spinner"></view>
				<text>加载中...</text>
			</view>
			
			<block v-else-if="detailData">
				<!-- 内容卡片 -->
				<view class="card main-card">
					<view class="detail-title">{{detailData.title}}</view>
					
					<view class="detail-info">
						<view class="info-tag">
							<text>{{detailData.source}}</text>
						</view>
						<!-- 热点标签 -->
						<view class="hot-tags" v-if="detailData.tags && detailData.tags.length > 0">
							<view class="tag-item" v-for="(tag, index) in detailData.tags" :key="index">
								<text>{{tag}}</text>
							</view>
						</view>
						<view class="hot-badge">
							<uni-icons type="fire" size="16" color="#FFFFFF"></uni-icons>
							<text>热度 {{detailData.hotValue}}</text>
						</view>
					</view>
					
					<view class="content-wrapper">
						<view class="detail-time">
							<uni-icons type="calendar" size="16" color="#999999"></uni-icons>
							<text>{{detailData.time}}</text>
						</view>

						<view class="divider"></view>
						
						<view class="stats-panel">
							<view class="stat-item">
								<uni-icons type="eye" size="20" color="#722ED1"></uni-icons>
								<text class="stat-value">{{detailData.viewCount || 0}}</text>
								<text class="stat-label">阅读</text>
							</view>
							<view class="stat-item">
								<uni-icons type="search" size="20" color="#722ED1"></uni-icons>
								<text class="stat-value">{{detailData.searchCount || 0}}</text>
								<text class="stat-label">搜索</text>
							</view>
							<view class="source-link-wrapper" v-if="detailData.sourceUrl">
								<text class="link-label">原文访问: </text>
								<text class="link-url" @click="openSourceUrl">点这里</text>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 操作图标 -->
				<view class="action-icons">
					<view class="icon-item" @click="toggleFavorite">
						<view class="icon-bubble" :class="{'active': isFavorite}">
							<uni-icons :type="isFavorite ? 'star-filled' : 'star'" size="20" :color="isFavorite ? '#FFFFFF' : '#722ED1'"></uni-icons>
						</view>
						<text class="icon-text" :class="{'active': isFavorite}">{{isFavorite ? '已收藏' : '收藏'}}</text>
					</view>
				</view>
				
				<!-- 评论区域 -->
				<view id="comments-section">
					<CommentSection
						:content-id="id"
						content-type="hot-topic"
						:comments="comments"
						:comment-count="(detailData && detailData.commentCount) || 0"
						:loading="commentsLoading"
						:error="commentsError"
						:has-more-comments="hasMoreComments"
						@submit-comment="handleSubmitComment"
						@toggle-comment-like="handleToggleCommentLike"
						@toggle-reply-like="handleToggleReplyLike"
						@delete-comment="handleDeleteComment"
						@delete-reply="handleDeleteReply"
						@refresh="getComments"
						@load-more="loadMoreComments"
						@comment-focus="onCommentFocus"
						@comment-blur="onCommentBlur"
					/>
				</view>
			</block>
			
			<view v-else class="no-data">
				<text>暂无数据</text>
			</view>
		</view>

		<!-- AI小助手 -->
		<AIAssistant
			:pageType="'hotDetail'"
			:contentData="detailData"
			:visible="!!detailData"
		/>
	</view>
</template>

<script>
import api from '@/common/api.js';
import CommentSection from '../../components/CommentSection/CommentSection.vue';
import AIAssistant from '../../components/AIAssistant.vue';

export default {
	components: {
		CommentSection,
		AIAssistant
	},
	data() {
		return {
			id: '',
			loading: true,
			detailData: null,
			isFavorite: false,
			fromDiscoverPage: false, // 标记是否从发现页跳转而来
			// 评论相关数据
			comments: [],
			commentsLoading: false,
			commentsError: null,
			currentPage: 0,
			pageSize: 20,
			hasMoreComments: true
		}
	},
	onLoad(option) {
		//console.log('热点详情页加载，参数：', JSON.stringify(option));
		
		// 获取页面栈信息，帮助判断是否为刷新
		const pages = getCurrentPages();
		console.log(`当前页面栈长度: ${pages.length}, 当前页面路径: ${pages[pages.length - 1] ? pages[pages.length - 1].route : '未知'}`);
		
		// 检查是否是新的热点ID，如果是则清除可能存在的旧状态
		const lastViewedId = uni.getStorageSync('lastViewedHotTopicId');
		if (option && option.id && option.id !== lastViewedId) {
			console.log(`检测到新热点ID: ${option.id}，清除旧状态`);
			uni.removeStorageSync('hotDetailPageInfo');
			uni.removeStorageSync('visitingExternalUrl');
			uni.removeStorageSync('hotDetailReturnUrl');
			
			// 保存当前查看的热点ID
			uni.setStorageSync('lastViewedHotTopicId', option.id);
		}
		
		if (option && option.id) {
			this.id = option.id;
			
			// 关键判断：只有直接从发现页跳转并且有时间戳参数时才增加浏览量
			// 刷新页面时参数会保留但没有页面栈
			if (option.from === 'discover' && option.t && pages.length > 1) {
				console.log('从发现页真实跳转而来，将更新浏览量');
				this.fromDiscoverPage = true;
				
				// 存储来源信息，用于处理返回逻辑
				uni.setStorageSync('currentPageFrom', 'discover');
				
				// 更新浏览量
				this.updateViewCount();
			} else if (option.from === 'discover') {
				// 这可能是刷新页面的情况，不更新浏览量
				console.log('检测到可能是页面刷新，不更新浏览量但保留返回逻辑');
				this.fromDiscoverPage = true;
				uni.setStorageSync('currentPageFrom', 'discover');
			} else {
				console.log('非来自发现页的跳转，不更新浏览量');
			}
			
			this.loadDetailData();
		} else {
			this.loading = false;
			console.error('缺少热点ID参数');
		}
		
		// #ifdef APP-PLUS
		// 添加应用从后台恢复到前台的事件监听
		plus.globalEvent.addEventListener('resume', this.handleAppResume);
		// #endif
	},
	onUnload() {
		// #ifdef APP-PLUS
		// 页面卸载时移除事件监听
		plus.globalEvent.removeEventListener('resume', this.handleAppResume);
		// #endif
	},
	onShow() {
		// 每次页面显示时输出状态信息
		console.log('热点详情页显示，当前状态:');
		console.log(`- ID: ${this.id}`);
		console.log(`- 来自发现页: ${this.fromDiscoverPage}`);
		console.log(`- 本地存储页面来源: ${uni.getStorageSync('currentPageFrom')}`);
		
		// 获取页面栈信息
		const pages = getCurrentPages();
		console.log(`- 当前页面栈长度: ${pages.length}`);
		
		// 检查是否是从外部链接返回
		const visitingExternalUrl = uni.getStorageSync('visitingExternalUrl');
		const hotDetailPageInfo = uni.getStorageSync('hotDetailPageInfo');
		
		if (visitingExternalUrl && hotDetailPageInfo) {
			console.log('检测到从外部链接返回到热点详情页');
			uni.removeStorageSync('visitingExternalUrl');
			
			// 如果页面ID与存储的不一致，则重新加载正确的详情
			if (this.id !== hotDetailPageInfo.id) {
				console.log('页面ID不一致，重新加载正确的详情');
				this.id = hotDetailPageInfo.id;
				this.fromDiscoverPage = hotDetailPageInfo.fromDiscoverPage;
				this.loadDetailData();
			}
			
			// 清除存储的页面信息，防止影响后续操作
			uni.removeStorageSync('hotDetailPageInfo');
		}
		
		// 如果是H5环境，检查URL是否与存储的一致
		// #ifdef H5
		const returnUrl = uni.getStorageSync('hotDetailReturnUrl');
		if (returnUrl && window.location.href !== returnUrl) {
			console.log('H5环境检测到URL变化，可能是从外部链接返回');
			uni.removeStorageSync('hotDetailReturnUrl');
			
			// 检查是否需要重新加载数据
			if (hotDetailPageInfo && this.id !== hotDetailPageInfo.id) {
				this.id = hotDetailPageInfo.id;
				this.fromDiscoverPage = hotDetailPageInfo.fromDiscoverPage;
				this.loadDetailData();
			}
			
			// 清除存储的页面信息
			uni.removeStorageSync('hotDetailPageInfo');
		}
		// #endif
	},
	methods: {
		// 处理APP从后台恢复到前台的事件
		handleAppResume() {
			console.log('APP从后台恢复到前台');
			
			// 检查是否是从外部浏览器返回
			const visitingExternalUrl = uni.getStorageSync('visitingExternalUrl');
			if (visitingExternalUrl) {
				console.log('检测到从外部浏览器返回');
				uni.removeStorageSync('visitingExternalUrl');
				
				// 检查是否需要重新加载数据
				const hotDetailPageInfo = uni.getStorageSync('hotDetailPageInfo');
				if (hotDetailPageInfo) {
					console.log('恢复热点详情页状态');
					if (this.id !== hotDetailPageInfo.id) {
						this.id = hotDetailPageInfo.id;
						this.fromDiscoverPage = hotDetailPageInfo.fromDiscoverPage;
						this.loadDetailData();
					}
					
					// 清除存储的页面信息
					uni.removeStorageSync('hotDetailPageInfo');
				}
			}
		},
		goBack() {
			// 如果是从发现页来的，返回到发现页
			if (this.fromDiscoverPage) {
				// 获取页面栈
				const pages = getCurrentPages();
				if (pages.length > 1) {
					// 正常返回上一页
					uni.navigateBack({
						delta: 1
					});
				} else {
					// 如果是刷新后的页面，使用switchTab而不是redirectTo
					uni.switchTab({
						url: '/pages/discover/discover'
					});
				}
			} else {
				// 其他情况普通返回
				uni.navigateBack({
					delta: 1
				});
			}
		},
		async loadDetailData() {
			this.loading = true;
			
			// 输出调试信息，确认ID值
			console.log('正在获取热点详情，ID:', this.id);
			
			try {
				// 调用后端API获取热点详情
				const res = await api.request({
					url: `/v1/hot-topics/${this.id}`,
					method: 'GET'
				});

				if (res.data && res.data.code === 0 && res.data.data) {
					const topic = res.data.data;

					// 将API返回的数据转换为页面所需格式
					this.detailData = {
						title: topic.title,
						time: this.formatTime(topic.collectTime || topic.updateTime),
						source: topic.source || '智索科技',
						sourceUrl: topic.sourceUrl,
						hotValue: topic.hotValue,
						viewCount: topic.viewCount || 0,
						searchCount: topic.searchCount || 0,
						commentCount: topic.commentCount || 0,
						tags: topic.tags || [], // 添加标签信息
						content: `<div style="font-size: 28rpx; line-height: 1.8;">
							<p>${topic.description || topic.title}</p>
							<p>热点来源: ${topic.source || '智索科技'}</p>
							<p>当前热度: ${topic.hotValue}（阅读量: ${topic.viewCount || 0}, 搜索量: ${topic.searchCount || 0}）</p>
							<p>收集时间: ${this.formatTime(topic.collectTime)}</p>
						</div>`
					};

					// 设置收藏状态
					this.isFavorite = topic.isFavorited || false;

					//console.log('获取热点详情成功:', this.detailData);

					// 获取评论列表
					await this.getComments();

					// 更新当前页面的热点ID存储
					uni.setStorageSync('lastViewedHotTopicId', this.id);
				} else {
					console.error('获取热点详情失败:', res.data);
					uni.showToast({
						title: '获取热点详情失败',
						icon: 'none'
					});

					// 失败后显示占位内容
					this.detailData = {
						title: '无法获取热点详情',
						time: this.formatTime(new Date()),
						source: '系统提示',
						commentCount: 0,
						content: '<p>获取热点详情失败，请稍后再试</p>'
					};
				}
			} catch (err) {
				console.error('请求热点详情失败:', err);
				uni.showToast({
					title: '网络请求失败',
					icon: 'none'
				});

				// 失败后显示占位内容
				this.detailData = {
					title: '网络错误',
					time: this.formatTime(new Date()),
					source: '系统提示',
					commentCount: 0,
					content: '<p>网络连接异常，请检查网络后重试</p>'
				};
			} finally {
				this.loading = false;
			}
		},
		
		// 格式化时间
		formatTime(timestamp) {
			if (!timestamp) return '';
			
			const date = new Date(timestamp);
			const year = date.getFullYear();
			const month = (date.getMonth() + 1).toString().padStart(2, '0');
			const day = date.getDate().toString().padStart(2, '0');
			const hours = date.getHours().toString().padStart(2, '0');
			const minutes = date.getMinutes().toString().padStart(2, '0');
			
			return `${year}-${month}-${day} ${hours}:${minutes}`;
		},
		
		// 打开源链接
		openSourceUrl() {
			if (this.detailData && this.detailData.sourceUrl) {
				// 保存当前页面状态和参数
				const currentPageInfo = {
					id: this.id,
					fromDiscoverPage: this.fromDiscoverPage
				};
				uni.setStorageSync('hotDetailPageInfo', currentPageInfo);
				
				// 检查是否是常见的有CSP限制的网站
				const url = this.detailData.sourceUrl;
				const restrictedDomains = [
					'zhihu.com',
					'www.zhihu.com',
					'weibo.com',
					'www.weibo.com',
					'weibo.cn',
					'www.baidu.com',
					'baidu.com',
					'qq.com',
					'bilibili.com',
					'www.bilibili.com'
				];
				
				let isRestrictedSite = false;
				try {
					const urlObj = new URL(url);
					const hostname = urlObj.hostname;
					isRestrictedSite = restrictedDomains.some(domain => 
						hostname === domain || hostname.endsWith('.' + domain)
					);
				} catch (e) {
					console.error('URL解析错误:', e);
				}
				
				if (isRestrictedSite) {
					// 对于有CSP限制的网站，直接使用系统浏览器打开
					uni.showToast({
						title: '在浏览器中打开',
						icon: 'none'
					});
					
					// 记录访问外部链接
					uni.setStorageSync('visitingExternalUrl', true);
					
					// 直接调用浏览器打开方法
					this.openInBrowser(url);
				} else {
					// 对于其他网站，提供选择
					uni.showActionSheet({
						itemList: ['应用内打开', '浏览器打开'],
						success: (res) => {
							if (res.tapIndex === 0) {
								// 应用内打开
								this.openInWebView(this.detailData.sourceUrl);
							} else if (res.tapIndex === 1) {
								// 浏览器打开
								this.openInBrowser(this.detailData.sourceUrl);
							}
						}
					});
				}
			}
		},
		
		// 在应用内WebView中打开链接
		openInWebView(url) {
			// 记录访问外部链接
			uni.setStorageSync('visitingExternalUrl', true);
			
			// 使用uni-app的navigateTo打开WebView页面
			uni.navigateTo({
				url: `/pages/common/webview?url=${encodeURIComponent(url)}&title=${encodeURIComponent(this.detailData.source || '原文')}`
			});
		},
		
		// 在系统浏览器中打开链接
		openInBrowser(url) {
			// 记录访问外部链接
			uni.setStorageSync('visitingExternalUrl', true);
			
			// 在不同平台使用不同的打开方式
			// #ifdef H5
			// 保存当前URL，以便返回
			const currentUrl = window.location.href;
			uni.setStorageSync('hotDetailReturnUrl', currentUrl);
			
			// 使用新窗口打开，这样关闭后可以回到当前页面
			window.open(url || this.detailData.sourceUrl, '_blank');
			// #endif
			
			// #ifdef APP-PLUS
			// 使用系统浏览器打开链接
			plus.runtime.openURL(url || this.detailData.sourceUrl, (error) => {
				if (error) {
					uni.showToast({
						title: '打开链接失败',
						icon: 'none'
					});
					// 如果打开失败，清除访问标记
					uni.removeStorageSync('visitingExternalUrl');
				}
			});
			
			// 设置一个备用清理机制，防止APP恢复时没有触发handleAppResume
			setTimeout(() => {
				console.log('检查是否需要清理访问标记');
				const hotDetailPageInfo = uni.getStorageSync('hotDetailPageInfo');
				if (hotDetailPageInfo && hotDetailPageInfo.id === this.id) {
					console.log('延时清理机制：保持当前热点ID');
				} else {
					console.log('延时清理机制：检测到ID不匹配，可能需要更新');
				}
			}, 5000);
			// #endif
			
			// #ifdef MP
			uni.setClipboardData({
				data: url || this.detailData.sourceUrl,
				success: () => {
					uni.showToast({
						title: '链接已复制，请在浏览器中打开',
						icon: 'none'
					});
				}
			});
			// #endif
		},
		
		// 收藏/取消收藏
		async toggleFavorite() {
			try {
				const response = await api.request({
					url: '/v1/favorites',
					method: 'POST',
					data: {
						contentId: this.id,
						contentType: 'topic'
					}
				});

				if (response.data && response.data.code === 0) {
					// 从响应中获取收藏状态
					this.isFavorite = response.data.data.isFavorited;

					uni.showToast({
						title: this.isFavorite ? '收藏成功' : '取消收藏成功',
						icon: 'success'
					});
				} else {
					throw new Error(response.data?.message || '操作失败');
				}
			} catch (error) {
				console.error('收藏操作失败:', error);
				uni.showToast({
					title: error.message || '操作失败',
					icon: 'error'
				});
			}
		},
		
		// ========== 评论相关方法 ==========

		// 获取评论列表
		async getComments() {
			this.commentsLoading = true;
			this.commentsError = null;

			try {
				const response = await api.request({
					url: '/v1/comments',
					method: 'GET',
					data: {
						contentId: this.id,
						contentType: 'hot-topic',
						page: this.currentPage,
						size: this.pageSize
					}
				});

				if (response.data && response.data.code === 0 && response.data.data) {
					const newComments = response.data.data.content || [];

					if (this.currentPage === 0) {
						this.comments = newComments;
					} else {
						this.comments = [...this.comments, ...newComments];
					}

					this.hasMoreComments = newComments.length === this.pageSize;

					// 更新热点评论数
					if (this.detailData && response.data.data.totalElements !== undefined) {
						this.detailData.commentCount = response.data.data.totalElements;
					}

					console.log('获取评论成功:', this.comments);
				} else {
					throw new Error(response.data?.message || '获取评论失败');
				}
			} catch (error) {
				console.error('获取评论失败:', error);
				this.commentsError = error.message || '网络错误，请稍后重试';
				// 不再使用默认数据，直接显示空状态
				if (this.currentPage === 0) {
					this.comments = [];
				}
			} finally {
				this.commentsLoading = false;
			}
		},

		// 加载更多评论
		async loadMoreComments() {
			if (this.commentsLoading || !this.hasMoreComments) return;

			this.currentPage += 1;
			await this.getComments();
		},

		// 处理评论组件的发表评论事件
		async handleSubmitComment(commentData) {
			try {
				// 构建请求数据 - 直接使用组件处理后的纯净内容
				const requestData = {
					contentId: this.id,
					contentType: 'hot-topic',
					content: commentData.content, // 使用组件已经处理过的纯净内容
					parentId: commentData.parentId
				};

				const response = await api.request({
					url: '/v1/comments',
					method: 'POST',
					data: requestData
				});

				if (response.data && response.data.code === 0) {
					// 评论成功后重新获取评论列表，确保数据准确
					this.currentPage = 0;
					await this.getComments();

					uni.showToast({
						title: '评论成功',
						icon: 'success'
					});
				} else {
					throw new Error(response.data?.message || '评论失败');
				}
			} catch (error) {
				console.error('发表评论失败:', error);
				uni.showToast({
					title: error.message || '评论失败',
					icon: 'error'
				});
				throw error; // 重新抛出错误，让组件知道操作失败
			}
		},

		// 处理评论点赞事件
		async handleToggleCommentLike({ comment }) {
			const originalLiked = comment.isLiked;
			const originalCount = comment.likeCount || 0;

			try {
				const response = await api.request({
					url: '/v1/likes',
					method: 'POST',
					data: {
						contentId: comment.commentId,
						contentType: 'comment'
					}
				});

				if (response.data && response.data.code === 0) {
					// 从响应中获取点赞状态
					comment.isLiked = response.data.data.isLiked;
					if (comment.isLiked) {
						comment.likeCount = originalCount + 1;
					} else {
						comment.likeCount = Math.max(originalCount - 1, 0);
					}
				} else {
					throw new Error(response.data?.message || '操作失败');
				}
			} catch (error) {
				console.error('评论点赞失败:', error);
				// 恢复原始状态
				comment.isLiked = originalLiked;
				comment.likeCount = originalCount;
				uni.showToast({
					title: error.message || '操作失败',
					icon: 'error'
				});
			}
		},

		// 处理回复点赞事件
		async handleToggleReplyLike({ reply }) {
			const originalLiked = reply.isLiked;
			const originalCount = reply.likeCount || 0;

			try {
				const response = await api.request({
					url: '/v1/likes',
					method: 'POST',
					data: {
						contentId: reply.commentId,
						contentType: 'comment'
					}
				});

				if (response.data && response.data.code === 0) {
					// 从响应中获取点赞状态
					reply.isLiked = response.data.data.isLiked;
					if (reply.isLiked) {
						reply.likeCount = originalCount + 1;
					} else {
						reply.likeCount = Math.max(originalCount - 1, 0);
					}
				} else {
					throw new Error(response.data?.message || '操作失败');
				}
			} catch (error) {
				console.error('回复点赞失败:', error);
				// 恢复原始状态
				reply.isLiked = originalLiked;
				reply.likeCount = originalCount;
				uni.showToast({
					title: error.message || '操作失败',
					icon: 'error'
				});
			}
		},

		// 评论输入框获得焦点
		onCommentFocus() {
			// 可以在这里添加一些UI反馈
		},

		// 评论输入框失去焦点
		onCommentBlur() {
			// 可以在这里添加一些UI反馈
		},

		// 处理删除评论事件
		async handleDeleteComment({ comment, index }) {
			try {
				const response = await api.request({
					url: `/v1/comments/${comment.commentId}`,
					method: 'DELETE'
				});

				if (response.data && response.data.code === 0) {
					// 从本地评论列表中移除
					this.comments.splice(index, 1);

					// 更新评论数量
					if (this.detailData) {
						// 计算删除的总数量（主评论 + 子评论）
						const deletedCount = 1 + (comment.replies ? comment.replies.length : 0);
						this.detailData.commentCount = Math.max(0, this.detailData.commentCount - deletedCount);
					}

					uni.showToast({
						title: '删除成功',
						icon: 'success'
					});
				} else {
					throw new Error(response.data?.message || '删除失败');
				}
			} catch (error) {
				console.error('删除评论失败:', error);
				uni.showToast({
					title: error.message || '删除失败',
					icon: 'error'
				});
			}
		},

		// 处理删除回复事件
		async handleDeleteReply({ reply, parentComment, replyIndex }) {
			try {
				const response = await api.request({
					url: `/v1/comments/${reply.commentId}`,
					method: 'DELETE'
				});

				if (response.data && response.data.code === 0) {
					// 从本地回复列表中移除
					if (parentComment.replies && parentComment.replies.length > replyIndex) {
						parentComment.replies.splice(replyIndex, 1);
					}

					// 更新评论数量
					if (this.detailData) {
						this.detailData.commentCount = Math.max(0, this.detailData.commentCount - 1);
					}

					uni.showToast({
						title: '删除成功',
						icon: 'success'
					});
				} else {
					throw new Error(response.data?.message || '删除失败');
				}
			} catch (error) {
				console.error('删除回复失败:', error);
				uni.showToast({
					title: error.message || '删除失败',
					icon: 'error'
				});
			}
		},

		// 滚动到评论区域
		scrollToComments() {
			uni.pageScrollTo({
				selector: '#comments-section',
				duration: 300
			});
		},
		
		// 更新浏览量
		updateViewCount() {
			// 调用API更新浏览量
			if (this.id) {
				console.log(`正在更新热点ID=${this.id}的浏览量`);
				
				api.request({
					url: `/v1/hot-topics/${this.id}/view`,
					method: 'POST'
				}).then(res => {
					console.log(`热点ID=${this.id}浏览量更新成功:`, res.data);
					// 本地浏览量+1，不等待后端更新
					if (this.detailData) {
						this.detailData.viewCount++;
					}
				}).catch(err => {
					console.error(`热点ID=${this.id}更新浏览量失败:`, err);
				});
			}
		}
	}
}
</script>

<style>
.page {
	min-height: 100vh;
	background-color: #F6F7FB;
	padding-bottom: 30rpx;
}

.gradient-header {
	background: linear-gradient(135deg, #722ED1, #A353E3);
	height: 220rpx;
	position: relative;
	border-bottom-left-radius: 30rpx;
	border-bottom-right-radius: 30rpx;
	box-shadow: 0 4rpx 20rpx rgba(114, 46, 209, 0.15);
}

.header-content {
	display: flex;
	justify-content: space-between;
	align-items: center;
	height: 90rpx;
	padding: 0 30rpx;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
}

.back-btn {
	font-size: 28rpx;
	color: #FFFFFF;
	display: flex;
	align-items: center;
}

.title {
	font-size: 34rpx;
	font-weight: bold;
	color: #FFFFFF;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

.placeholder {
	width: 60rpx;
}

.content-container {
	padding: 0 24rpx;
	margin-top: -80rpx;
	position: relative;
	z-index: 10;
}

.card {
	background-color: #FFFFFF;
	border-radius: 16rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
	padding: 30rpx;
	margin-bottom: 24rpx;
}

.main-card {
	padding-bottom: 20rpx;
}

.detail-title {
	font-size: 38rpx;
	font-weight: bold;
	margin-bottom: 24rpx;
	color: #333333;
	line-height: 1.4;
}

.detail-info {
	display: flex;
	align-items: center;
	gap: 8rpx;
	margin-bottom: 30rpx;
}

.detail-info .hot-badge {
	margin-left: auto;
}

.info-tag {
	background-color: #F0E7FB;
	padding: 6rpx 16rpx;
	border-radius: 8rpx;
	font-size: 24rpx;
	color: #722ED1;
}

.hot-badge {
	background: linear-gradient(to right, #FF6B6B, #FF9B6B);
	padding: 6rpx 16rpx;
	border-radius: 30rpx;
	font-size: 24rpx;
	color: #FFFFFF;
	display: flex;
	align-items: center;
}

.hot-badge uni-icons {
	margin-right: 6rpx;
}

.content-wrapper {
	background-color: #FAFAFA;
	border-radius: 12rpx;
	padding: 20rpx;
}

.detail-time {
	display: flex;
	align-items: center;
	font-size: 24rpx;
	color: #999999;
	margin-bottom: 16rpx;
}

.detail-time uni-icons {
	margin-right: 8rpx;
}

.divider {
	height: 1rpx;
	background-color: #EEEEEE;
	margin: 16rpx 0;
}

.stats-panel {
	display: flex;
	flex-wrap: wrap;
	align-items: center;
}

.stat-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-right: 40rpx;
}

.stat-value {
	font-weight: bold;
	color: #333333;
	margin: 6rpx 0;
	font-size: 26rpx;
}

.stat-label {
	font-size: 22rpx;
	color: #999999;
}

.source-link-wrapper {
	margin-left: auto;
}

.link-label {
	margin-right: 10rpx;
	color: #999999;
	font-size: 24rpx;
}

.link-url {
	color: #722ED1;
	text-decoration: underline;
	font-weight: bold;
	font-size: 24rpx;
}

.action-icons {
	display: flex;
	justify-content: flex-end;
	margin-bottom: 24rpx;
	padding: 0 10rpx;
}

.icon-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	margin-left: 40rpx;
}

.icon-bubble {
	width: 70rpx;
	height: 70rpx;
	border-radius: 50%;
	background-color: #F0E7FB;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-bottom: 8rpx;
	transition: all 0.3s;
}

.icon-bubble.active {
	background-color: #722ED1;
}

.icon-text {
	font-size: 24rpx;
	color: #999;
}

.icon-text.active {
	color: #722ED1;
	font-weight: bold;
}

.loading, .no-data {
	text-align: center;
	padding: 100rpx 0;
	color: #999999;
	background: #FFFFFF;
	border-radius: 16rpx;
	margin-top: 80rpx;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #722ED1;
	border-radius: 50%;
	margin: 0 auto 20rpx;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* 热点标签样式 */
.detail-info .hot-tags {
	display: flex;
	flex-wrap: wrap;
	align-items: center;

	.tag-item {
		background-color: #E8D5F7;
		padding: 6rpx 12rpx;
		border-radius: 8rpx;
		font-size: 22rpx;
		color: #722ED1;
		margin-right: 6rpx;
		font-weight: 500;
	}
}

</style> 