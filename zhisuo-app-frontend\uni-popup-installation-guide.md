# uni-popup组件安装指南

## 问题描述
在profile.vue文件中使用了uni-popup和uni-popup-dialog组件，但项目中未安装这些组件，导致控制台报错：
```
Failed to resolve component: uni-popup-dialog
```

## 已完成的解决步骤

1. 已通过npm安装了uni-ui组件库：
```bash
npm install @dcloudio/uni-ui
```

2. 已在pages.json中添加了easycom配置：
```json
"easycom": {
  "autoscan": true,
  "custom": {
    "^uni-(.*)": "@dcloudio/uni-ui/lib/uni-$1/uni-$1.vue"
  }
}
```

## 后续操作

### 方法一：重启项目并测试（推荐）

1. 重启项目
2. 访问个人主页，查看是否仍有报错

### 方法二：如果问题仍然存在，通过HBuilderX插件市场安装

1. 打开HBuilderX
2. 点击顶部菜单栏的"插件"
3. 选择"插件市场"
4. 搜索"uni-popup"
5. 点击"安装到项目"
6. 选择当前项目"zhisuo-app-frontend"
7. 点击"确定"安装

### 方法三：手动下载并导入

如果上述方法都不起作用，可以尝试手动下载并导入：

1. 访问 [uni-popup组件](https://ext.dcloud.net.cn/plugin?id=329)
2. 下载组件
3. 解压后将组件文件夹复制到项目的uni_modules目录下

## 注意事项

- uni-popup组件依赖于uni-transition组件，确保该组件也已安装
- 如果使用HBuilderX开发，建议使用插件市场安装，这样更容易管理组件版本
- 如果使用VS Code或其他编辑器开发，npm安装和手动导入是更好的选择 