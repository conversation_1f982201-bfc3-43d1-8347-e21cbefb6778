<template>
	<view class="container">
		<!-- 导航栏 -->
		<view class="navbar">
			<view class="nav-left" @click="goBack">
				<uni-icons type="left" size="20" color="#333"></uni-icons>
			</view>
			<view class="nav-title">文章列表</view>
			<view class="nav-right"></view>
		</view>

		<!-- 搜索框 -->
		<view class="search-container">
			<view class="search-box">
				<uni-icons type="search" size="18" color="#999"></uni-icons>
				<input
					class="search-input"
					type="text"
					placeholder="搜索文章标题..."
					v-model="searchKeyword"
					@input="onSearchInput"
					@confirm="onSearchConfirm"
					confirm-type="search"
				/>
				<view v-if="searchKeyword" class="clear-btn" @click="clearSearch">
					<uni-icons type="clear" size="16" color="#999"></uni-icons>
				</view>
			</view>
		</view>

		<!-- 文章列表 -->
		<view class="article-list">
			<!-- 加载状态 -->
			<view v-if="articlesLoading" class="loading">
				<LoadingAnimation type="spinner" text="正在加载文章..." />
			</view>

			<!-- 错误状态 -->
			<view v-else-if="articlesError" class="error-container">
				<view class="error-text">{{ articlesError }}</view>
				<view class="retry-btn" @click="getArticleList">重试</view>
			</view>

			<!-- 文章列表 -->
			<view v-else>
				<!-- 文章项 -->
				<view
					v-for="(article, index) in articles"
					:key="article.articleId || index"
					class="article-item"
					@click="onArticleClick(article)"
				>
					<view class="article-image">
						<image
							:src="article.coverImage || '/static/logo.png'"
							mode="aspectFill"
							:lazy-load="true"
						></image>
					</view>
					<view class="article-info">
						<view class="article-title">{{ article.title || '无标题' }}</view>
						<view class="article-desc">{{ article.description || '暂无描述' }}</view>
						<!-- 文章标签 -->
						<view class="article-tags">
							<view class="tag-item news-tag">
								<text>新闻</text>
							</view>
						</view>
						<view class="article-meta">
							<view class="stats-group">
								<view class="views">
									<uni-icons type="eye" size="14" color="#999"></uni-icons>
									<text>{{ formatViewCount(article.viewCount) }}</text>
								</view>
								<view class="comments">
									<uni-icons type="chat" size="14" color="#999"></uni-icons>
									<text>{{ article.commentCount || 0 }}</text>
								</view>
							</view>
							<view class="time-icon-group">
								<view v-if="article.iconUrl" class="article-icon">
									<image :src="article.iconUrl" mode="aspectFit"></image>
								</view>
								<view class="time">
									<uni-icons type="clock" size="14" color="#999"></uni-icons>
									<text>{{ formatTimeAgo(article.publishTime) }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>

				<!-- 搜索模式下的状态 -->
				<view v-if="isSearchMode">
					<!-- 搜索结果为空 -->
					<view v-if="articles.length === 0" class="empty">
						<text>未找到相关文章</text>
					</view>
					<!-- 搜索结果底部提示 -->
					<view v-else class="search-result-tip">
						<text>共找到 {{ articles.length }} 篇相关文章</text>
					</view>
				</view>

				<!-- 普通模式下的状态 -->
				<view v-else>
					<!-- 加载更多状态 -->
					<view v-if="loadMoreStatus === 'loading'" class="load-more">
						<!-- 使用加载动画组件 -->
						<LoadingAnimation
							:type="loadingAnimationType"
							text="正在加载更多..."
							color="#722ED1"
						/>
					</view>

					<!-- 没有更多数据 -->
					<view v-else-if="articles.length > 0" class="no-more">
						<text>没有更多文章了</text>
					</view>

					<!-- 空状态 -->
					<view v-else class="empty">
						<text>暂无文章</text>
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import Api from '../../common/api.js';
	import LoadingAnimation from '../../components/LoadingAnimation.vue';

	export default {
		components: {
			LoadingAnimation
		},
		data() {
			return {
				articles: [], // 文章列表
				articlesLoading: false, // 文章加载状态
				articlesError: null, // 文章加载错误信息
				currentPage: 0, // 当前页码
				pageSize: 5, // 每页数量
				hasMore: true, // 是否还有更多数据
				loadMoreStatus: 'more', // 加载更多状态
				loadingAnimationType: 'spinner', // 当前加载动画类型
				searchKeyword: '', // 搜索关键词
				searchTimer: null, // 搜索防抖定时器
				isSearchMode: false // 是否处于搜索模式
			};
		},
		onLoad() {
			// 页面加载时初始化
		},
		onReady() {
			// DOM准备完成后再加载数据
			this.$nextTick(() => {
				this.getArticleList();
			});
		},
		onReachBottom() {
			// 触底自动加载更多
			this.loadMore();
		},
		methods: {
			// 返回首页
			goBack() {
				uni.switchTab({
					url: '/pages/index/index'
				});
			},

			// 获取文章列表
			async getArticleList(isLoadMore = false) {
				if (!isLoadMore) {
					this.articlesLoading = true;
					this.articlesError = null;
					this.currentPage = 0;
					this.articles = [];
				} else {
					this.loadMoreStatus = 'loading';
				}

				// 确保UI有时间更新显示加载动画
				await this.$nextTick();

				// 添加最小延迟确保用户能看到加载动画
				if (isLoadMore) {
					await new Promise(resolve => setTimeout(resolve, 300));
				}

				try {
					const response = await Api.request({
						url: '/v1/articles',
						method: 'GET',
						data: {
							page: this.currentPage,
							size: this.pageSize,
							sortBy: 'viewCount',
							sortOrder: 'desc'
						}
					});

					if (response.data && response.data.code === 0 && response.data.data) {
						const newArticles = response.data.data.content || [];

						if (isLoadMore) {
							this.articles = [...this.articles, ...newArticles];
						} else {
							this.articles = newArticles;
						}

						// 判断是否还有更多数据
						this.hasMore = newArticles.length === this.pageSize;
						this.currentPage++;
					} else {
						console.error('API响应格式错误:', response.data);
						throw new Error(response.data?.message || '获取文章失败');
					}
				} catch (error) {
					console.error('获取文章列表失败:', error);
					if (!isLoadMore) {
						this.articlesError = error.message || '网络错误，请稍后重试';
					} else {
						uni.showToast({
							title: '加载失败',
							icon: 'none'
						});
					}
				} finally {
					if (!isLoadMore) {
						this.articlesLoading = false;
					} else {
						this.loadMoreStatus = this.hasMore ? 'more' : 'noMore';
					}
				}
			},

			// 加载更多
			loadMore() {
				// 搜索模式下不支持加载更多
				if (this.isSearchMode) {
					return;
				}

				if (this.hasMore && this.loadMoreStatus !== 'loading') {
					this.getArticleList(true);
				}
			},

			// 点击文章项
			onArticleClick(article) {
				if (!article || !article.articleId) {
					return;
				}

				// 跳转到文章详情页
				uni.navigateTo({
					url: `/pages/article/detail?id=${article.articleId}`
				});
			},

			// 格式化浏览量
			formatViewCount(count) {
				if (!count || count === 0) return '0';
				if (count < 1000) return count.toString();
				if (count < 10000) return (count / 1000).toFixed(1) + 'k';
				return (count / 10000).toFixed(1) + 'w';
			},

			// 格式化时间
			formatTimeAgo(timestamp) {
				if (!timestamp) return '刚刚';

				const now = Date.now();
				const time = new Date(timestamp).getTime();
				const diff = now - time;

				const minute = 60 * 1000;
				const hour = 60 * minute;
				const day = 24 * hour;

				if (diff < minute) return '刚刚';
				if (diff < hour) return Math.floor(diff / minute) + '分钟前';
				if (diff < day) return Math.floor(diff / hour) + '小时前';
				if (diff < 7 * day) return Math.floor(diff / day) + '天前';

				// 超过7天显示具体日期
				const date = new Date(timestamp);
				return `${date.getMonth() + 1}-${date.getDate()}`;
			},

			// 搜索输入处理（防抖）
			onSearchInput() {
				// 清除之前的定时器
				if (this.searchTimer) {
					clearTimeout(this.searchTimer);
				}

				// 设置新的定时器，500ms后执行搜索
				this.searchTimer = setTimeout(() => {
					this.performSearch();
				}, 500);
			},

			// 搜索确认处理
			onSearchConfirm() {
				// 清除定时器，立即执行搜索
				if (this.searchTimer) {
					clearTimeout(this.searchTimer);
				}
				this.performSearch();
			},

			// 执行搜索
			async performSearch() {
				const keyword = this.searchKeyword.trim();

				if (!keyword) {
					// 如果搜索关键词为空，退出搜索模式，重新加载普通文章列表
					this.isSearchMode = false;
					this.getArticleList();
					return;
				}

				// 进入搜索模式
				this.isSearchMode = true;
				this.articlesLoading = true;
				this.articlesError = null;

				try {
					const response = await Api.request({
						url: '/v1/articles/search',
						method: 'GET',
						data: {
							keyword: keyword,
							page: 0,
							size: 20, // 搜索结果一次性加载更多
							sortBy: 'viewCount',
							sortOrder: 'desc'
						}
					});

					if (response.data && response.data.code === 0 && response.data.data) {
						this.articles = response.data.data.content || [];
						// 搜索模式下不支持加载更多
						this.hasMore = false;
					} else {
						console.error('搜索API响应格式错误:', response.data);
						throw new Error(response.data?.message || '搜索失败');
					}
				} catch (error) {
					console.error('搜索文章失败:', error);
					this.articlesError = error.message || '搜索失败，请稍后重试';
					this.articles = [];
				} finally {
					this.articlesLoading = false;
				}
			},

			// 清除搜索
			clearSearch() {
				this.searchKeyword = '';
				this.isSearchMode = false;

				// 清除定时器
				if (this.searchTimer) {
					clearTimeout(this.searchTimer);
				}

				// 重新加载普通文章列表
				this.getArticleList();
			}
		}
	};
</script>

<style lang="scss" scoped>
	.container {
		min-height: 100vh;
		background-color: #f8f9fa;
	}

	.navbar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 88rpx;
		padding: 0 30rpx;
		background-color: #fff;
		border-bottom: 1rpx solid #eee;
		position: sticky;
		top: 0;
		z-index: 100;

		.nav-left,
		.nav-right {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;

			&:active {
				opacity: 0.6;
				transform: scale(0.95);
			}
		}

		.nav-title {
			font-size: 32rpx;
			font-weight: 600;
			color: #333;
		}
	}

	.search-container {
		background-color: #fff;
		padding: 20rpx 30rpx;
		border-bottom: 1rpx solid #eee;
		position: sticky;
		top: 88rpx;
		z-index: 99;

		.search-box {
			display: flex;
			align-items: center;
			background-color: #f5f5f5;
			border-radius: 24rpx;
			padding: 16rpx 20rpx;
			transition: all 0.3s ease;

			&:focus-within {
				background-color: #fff;
				box-shadow: 0 0 0 2rpx #722ED1;
			}

			.search-input {
				flex: 1;
				font-size: 28rpx;
				color: #333;
				margin: 0 16rpx;
				background: transparent;
				border: none;
				outline: none;

				&::placeholder {
					color: #999;
				}
			}

			.clear-btn {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 32rpx;
				height: 32rpx;
				border-radius: 50%;
				background-color: #ddd;
				transition: all 0.3s ease;

				&:active {
					opacity: 0.7;
					transform: scale(0.9);
				}
			}
		}
	}

	.article-list {
		padding: 30rpx;
	}

	.loading,
	.error-container,
	.empty {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 60rpx 30rpx;
		text-align: center;
		color: #999;
		font-size: 28rpx;
	}

	.error-text {
		font-size: 28rpx;
		color: #999;
		margin-bottom: 20rpx;
	}

	.retry-btn {
		background-color: #722ED1;
		color: #fff;
		padding: 16rpx 32rpx;
		border-radius: 20rpx;
		font-size: 26rpx;
		transition: all 0.3s ease;

		&:active {
			opacity: 0.8;
		}
	}

	// 复用首页的文章项样式
	.article-item {
		display: flex;
		align-items: center;
		background-color: #fff;
		border-radius: 16rpx;
		margin-bottom: 24rpx;
		padding: 24rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
		transition: all 0.3s ease;
		min-height: 180rpx;

		&:active {
			transform: translateY(1rpx);
			box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.04);
		}

		.article-image {
			width: 200rpx;
			height: 140rpx;
			border-radius: 12rpx;
			overflow: hidden;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
			flex-shrink: 0;
			margin-right: 24rpx;

			image {
				width: 100%;
				height: 100%;
				transition: all 0.3s ease;
			}
		}

		.article-info {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: center;
			min-height: 90rpx;
			overflow: hidden;

			.article-title {
				font-size: 30rpx;
				font-weight: 600;
				color: #333;
				line-height: 1.3;
				margin-bottom: 12rpx;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.article-desc {
				font-size: 26rpx;
				color: #666;
				line-height: 1.4;
				margin-bottom: 12rpx;
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				line-clamp: 2;
				overflow: hidden;
				text-overflow: ellipsis;
				flex: 1;
			}

			.article-meta {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 24rpx;
				color: #999;

				.stats-group {
					display: flex;
					align-items: center;
					gap: 16rpx;

					.views, .comments {
						display: flex;
						align-items: center;

						text {
							margin-left: 6rpx;
							font-size: 24rpx;
						}
					}
				}

				.time-icon-group {
					display: flex;
					align-items: center;
					gap: 8rpx;

					.article-icon {
						width: 28rpx;
						height: 28rpx;
						border-radius: 4rpx;
						overflow: hidden;
						flex-shrink: 0;

						image {
							width: 100%;
							height: 100%;
						}
					}

					.time {
						display: flex;
						align-items: center;

						text {
							margin-left: 6rpx;
							font-size: 24rpx;
						}
					}
				}
			}
		}
	}

	.load-more {
		padding: 30rpx;
		text-align: center;
	}



	.no-more {
		padding: 30rpx;
		text-align: center;
		color: #999;
		font-size: 26rpx;
	}

	.search-result-tip {
		padding: 30rpx;
		text-align: center;
		color: #666;
		font-size: 26rpx;
		background-color: #f8f9fa;
		border-radius: 12rpx;
		margin: 20rpx 0;
	}

	.article-tags {
		display: flex;
		flex-wrap: wrap;
		margin: 8rpx 0;

		.tag-item {
			display: flex;
			align-items: center;
			padding: 4rpx 8rpx;
			border-radius: 8rpx;
			font-size: 20rpx;
			margin-right: 8rpx;
			margin-bottom: 4rpx;

			text {
				font-weight: 500;
			}

			&.news-tag {
				background: linear-gradient(135deg, #722ED1, #A353E3);
				color: #FFFFFF;
			}
		}
	}
</style>
