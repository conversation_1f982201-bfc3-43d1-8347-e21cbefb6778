<template>
	<view class="page-container">
		<view class="page">
			<!-- 导航栏 -->
			<view class="navbar">
				<view class="nav-left" @click="goBack">
					<uni-icons type="left" size="20" color="#333"></uni-icons>
				</view>
				<view class="nav-title">文章详情</view>
				<view class="nav-right">
					<!-- 占位，保持布局平衡 -->
				</view>
			</view>

			<!-- 加载状态 -->
			<view v-if="loading" class="loading-container">
				<view class="loading-text">正在加载文章...</view>
			</view>

			<!-- 错误状态 -->
			<view v-else-if="error" class="error-container">
				<view class="error-text">{{ error }}</view>
				<view class="retry-btn" @click="getArticleDetail">重试</view>
			</view>

		<!-- 文章内容 -->
		<view v-else-if="article" class="article-container">
			<!-- 文章头部 -->
			<view class="article-header">
				<view class="article-title">{{ article.title }}</view>
				<view class="article-meta">
					<view class="meta-item">
						<uni-icons type="calendar" size="14" color="#999"></uni-icons>
						<text>{{ formatTime(article.publishTime) }}</text>
					</view>
					<view class="meta-item">
						<uni-icons type="eye" size="14" color="#999"></uni-icons>
						<text>{{ formatViewCount(article.viewCount) }}</text>
					</view>
					<view class="meta-item">
						<uni-icons type="hand-up" size="14" color="#999"></uni-icons>
						<text>{{ formatViewCount(article.likeCount) }}</text>
					</view>
				</view>
				<!-- 文章标签 -->
				<view class="article-tags">
					<view class="tag-item news-tag">
						<text>新闻</text>
					</view>
				</view>

				<view v-if="article.source" class="article-source" @click="openOriginalArticle">
					<uni-icons type="paperplane" size="14" color="#666"></uni-icons>
					<text class="source-label">来源：</text>
					<view v-if="article.iconUrl" class="source-icon">
						<image :src="article.iconUrl" mode="aspectFit"></image>
					</view>
					<text class="source-link">{{ article.source }}</text>
				</view>
			</view>

			<!-- 文章封面 -->
			<view v-if="article.coverImage" class="article-cover">
				<image :src="article.coverImage" mode="aspectFill"></image>
			</view>

			<!-- 文章描述 -->
			<view v-if="article.description" class="article-description">
				{{ article.description }}
			</view>

			<!-- 文章内容 -->
			<view class="article-content">
				<rich-text :nodes="formatContent(article.content)"></rich-text>
			</view>

			<!-- 文章底部操作 -->
			<view class="article-actions">
				<view class="action-btn" @click="toggleLike">
					<uni-icons :type="isLiked ? 'hand-up-filled' : 'hand-up'" size="22" :color="isLiked ? '#ff4757' : '#999'"></uni-icons>
					<text :class="{ liked: isLiked }">{{ isLiked ? '已点赞' : '点赞' }}</text>
					<text class="action-count">{{ formatViewCount(article.likeCount) }}</text>
				</view>
				<view class="action-btn" @click="toggleFavorite">
					<uni-icons :type="isFavorited ? 'star-filled' : 'star'" size="22" :color="isFavorited ? '#ffa502' : '#999'"></uni-icons>
					<text :class="{ favorited: isFavorited }">{{ isFavorited ? '已收藏' : '收藏' }}</text>
				</view>
				<view class="action-btn" @click="scrollToComments">
					<uni-icons type="chat" size="22" color="#999"></uni-icons>
					<text>评论</text>
					<text class="action-count">{{ formatViewCount(article.commentCount) }}</text>
				</view>
				<view class="action-btn" @click="shareArticle">
					<uni-icons type="redo" size="22" color="#999"></uni-icons>
					<text>分享</text>
				</view>
			</view>

			<!-- 评论区域 -->
			<view id="comments-section">
				<CommentSection
					:content-id="articleId"
					content-type="article"
					:comments="comments"
					:comment-count="article.commentCount"
					:loading="commentsLoading"
					:error="commentsError"
					:has-more-comments="hasMoreComments"
					@submit-comment="handleSubmitComment"
					@toggle-comment-like="handleToggleCommentLike"
					@toggle-reply-like="handleToggleReplyLike"
					@delete-comment="handleDeleteComment"
					@delete-reply="handleDeleteReply"
					@refresh="getComments"
					@load-more="loadMoreComments"
					@comment-focus="onCommentFocus"
					@comment-blur="onCommentBlur"
				/>
			</view>
		</view>

			<!-- 空状态 -->
			<view v-else class="empty-container">
				<view class="empty-text">文章不存在</view>
			</view>
		</view>

		<!-- AI小助手 -->
		<AIAssistant
			:pageType="'article'"
			:contentData="article"
			:articleId="articleId"
			:visible="!!article"
		/>
	</view>
</template>

<script>
	import Api from '../../common/api.js';
	import CommentSection from '../../components/CommentSection/CommentSection.vue';
	import AIAssistant from '../../components/AIAssistant.vue';

	export default {
		components: {
			CommentSection,
			AIAssistant
		},
		data() {
			return {
				articleId: '',
				article: null,
				loading: false,
				error: null,
				isLiked: false,
				isFavorited: false,
				likeLoading: false, // 点赞操作loading状态
				favoriteLoading: false, // 收藏操作loading状态
				// 评论相关数据
				comments: [],
				commentsLoading: false,
				commentsError: null,
				currentPage: 0,
				pageSize: 20,
				hasMoreComments: true
			}
		},
		onLoad(options) {
			if (options.id) {
				this.articleId = options.id;
				this.getArticleDetail();
			} else {
				this.error = '文章ID不存在';
			}
		},
		methods: {
			// 返回首页
			goBack() {
				uni.reLaunch({
					url: '/pages/index/index'
				});
			},

			// 获取文章详情
			async getArticleDetail() {
				this.loading = true;
				this.error = null;

				try {
					const response = await Api.request({
						url: `/v1/articles/${this.articleId}`,
						method: 'GET'
					});

					if (response.data && response.data.code === 0 && response.data.data) {
						this.article = response.data.data;

						// 获取用户对文章的状态（点赞、收藏）
						await this.getUserArticleStatus();

						// 获取评论列表
						await this.getComments();

						// 更新浏览量和标记为已读
						this.updateViewCount();
						this.markAsRead();
					} else {
						throw new Error(response.data?.message || '获取文章详情失败');
					}
				} catch (error) {
					console.error('获取文章详情失败:', error);
					this.error = error.message || '网络错误，请稍后重试';
				} finally {
					this.loading = false;
				}
			},

			// 获取用户对文章的状态
			async getUserArticleStatus() {
				try {
					// 检查点赞状态
					const likeResponse = await Api.request({
						url: '/v1/likes/check',
						method: 'GET',
						data: {
							contentId: this.articleId,
							contentType: 'article'
						}
					});

					if (likeResponse.data && likeResponse.data.code === 0) {
						this.isLiked = likeResponse.data.data.isLiked;
					}

					// 检查收藏状态
					const favoriteResponse = await Api.request({
						url: '/v1/favorites/check',
						method: 'GET',
						data: {
							contentId: this.articleId,
							contentType: 'article'
						}
					});

					if (favoriteResponse.data && favoriteResponse.data.code === 0) {
						this.isFavorited = favoriteResponse.data.data.isFavorited;
					}
				} catch (error) {
					console.error('获取用户状态失败:', error);
					// 如果获取状态失败，默认为未点赞、未收藏
					this.isLiked = false;
					this.isFavorited = false;
				}
			},

			// 更新浏览量
			async updateViewCount() {
				try {
					await Api.request({
						url: `/v1/articles/${this.articleId}/view`,
						method: 'POST'
					});

					// 更新前端显示的阅读量
					if (this.article && this.article.viewCount !== undefined) {
						this.article.viewCount = (this.article.viewCount || 0) + 1;
					}
				} catch (error) {
					console.error('更新浏览量失败:', error);
				}
			},

			// 标记为已读（已移除后端接口，保留方法以避免调用错误）
			async markAsRead() {
				// 注意：已读状态现在通过独立的user_read_records表管理
				// 这里可以根据需要实现客户端本地记录或调用新的已读记录接口
				console.log('文章已查看:', this.articleId);
			},

			// 格式化时间
			formatTime(timeStr) {
				if (!timeStr) return '';
				const date = new Date(timeStr);
				return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
			},

			// 格式化数量显示
			formatViewCount(count) {
				if (!count || count === 0) return '0';
				
				if (count >= 10000) {
					return (count / 10000).toFixed(1) + 'w';
				} else if (count >= 1000) {
					return (count / 1000).toFixed(1) + 'k';
				} else {
					return count.toString();
				}
			},

			// 格式化文章内容
			formatContent(content) {
				if (!content) return '';
				
				// 简单的文本格式化，将换行符转换为段落
				return content.split('\n').map(paragraph => {
					if (paragraph.trim()) {
						return `<p style="margin-bottom: 16px; line-height: 1.6;">${paragraph.trim()}</p>`;
					}
					return '';
				}).join('');
			},

			// 切换点赞状态
			async toggleLike() {
				// 防止重复点击
				if (this.likeLoading) return;
				this.likeLoading = true;

				const originalLiked = this.isLiked;
				const originalCount = this.article.likeCount || 0;

				try {
					// 使用文章点赞接口
					const response = await Api.request({
						url: `/v1/articles/${this.articleId}/like`,
						method: 'POST'
					});

					if (response.data && response.data.code === 0) {
						// 从响应中获取点赞状态
						this.isLiked = response.data.data.isLiked;

						// 更新点赞数
						if (this.isLiked) {
							this.article.likeCount = originalCount + 1;
							uni.showToast({ title: '点赞成功', icon: 'success' });
						} else {
							this.article.likeCount = Math.max(originalCount - 1, 0);
							uni.showToast({ title: '取消点赞', icon: 'success' });
						}
					} else {
						throw new Error(response.data?.message || '操作失败');
					}
				} catch (error) {
					console.error('点赞操作失败:', error);
					// 恢复原始状态
					this.isLiked = originalLiked;
					this.article.likeCount = originalCount;
					uni.showToast({
						title: error.message || '操作失败，请稍后重试',
						icon: 'error'
					});
				} finally {
					this.likeLoading = false;
				}
			},

			// 切换收藏状态
			async toggleFavorite() {
				// 防止重复点击
				if (this.favoriteLoading) return;
				this.favoriteLoading = true;

				const originalFavorited = this.isFavorited;

				try {
					const response = await Api.request({
						url: '/v1/favorites',
						method: 'POST',
						data: {
							contentId: this.articleId,
							contentType: 'article'
						}
					});

					if (response.data && response.data.code === 0) {
						// 从响应中获取收藏状态
						this.isFavorited = response.data.data.isFavorited;
						uni.showToast({
							title: this.isFavorited ? '收藏成功' : '取消收藏',
							icon: 'success'
						});
					} else {
						throw new Error(response.data?.message || '操作失败');
					}
				} catch (error) {
					console.error('收藏操作失败:', error);
					// 恢复原始状态
					this.isFavorited = originalFavorited;
					uni.showToast({
						title: error.message || '操作失败，请稍后重试',
						icon: 'error'
					});
				} finally {
					this.favoriteLoading = false;
				}
			},

			// 分享文章
			shareArticle() {
				uni.showActionSheet({
					itemList: ['分享到微信', '分享到朋友圈', '复制链接'],
					success: (res) => {
						switch (res.tapIndex) {
							case 0:
								// 分享到微信
								uni.showToast({ title: '分享到微信', icon: 'success' });
								break;
							case 1:
								// 分享到朋友圈
								uni.showToast({ title: '分享到朋友圈', icon: 'success' });
								break;
							case 2:
								// 复制链接
								uni.setClipboardData({
									data: this.article.sourceUrl || `文章：${this.article.title}`,
									success: () => {
										uni.showToast({ title: '链接已复制', icon: 'success' });
									}
								});
								break;
						}
					}
				});
			},

			// 打开原文链接
			openOriginalArticle() {
				if (!this.article.sourceUrl) {
					uni.showToast({
						title: '暂无原文链接',
						icon: 'none'
					});
					return;
				}

				// 直接在系统浏览器中打开原文链接
				// #ifdef APP-PLUS
				plus.runtime.openURL(this.article.sourceUrl);
				// #endif

				// #ifdef H5
				window.open(this.article.sourceUrl, '_blank');
				// #endif

				// #ifdef MP-WEIXIN
				uni.showModal({
					title: '提示',
					content: '即将跳转到外部链接，是否继续？',
					success: (res) => {
						if (res.confirm) {
							uni.setClipboardData({
								data: this.article.sourceUrl,
								success: () => {
									uni.showToast({
										title: '链接已复制，请在浏览器中打开',
										icon: 'none',
										duration: 3000
									});
								}
							});
						}
					}
				});
				// #endif
			},

			// 获取默认文章详情数据（作为备用）
			getDefaultArticleDetail() {
				// 根据articleId返回对应的默认数据
				const defaultArticles = {
					'default-1': {
						articleId: 'default-1',
						title: '2024年AI技术发展趋势分析',
						description: '深度解析AI技术在各行业的应用前景，探讨人工智能未来发展方向',
						content: `
							<h2>人工智能技术发展现状</h2>
							<p>2024年，人工智能技术正在经历前所未有的发展阶段。从机器学习到深度学习，从自然语言处理到计算机视觉，AI技术在各个领域都展现出了巨大的潜力。</p>

							<h2>主要应用领域</h2>
							<p><strong>1. 医疗健康</strong></p>
							<p>AI在医疗诊断、药物研发、个性化治疗等方面发挥着越来越重要的作用。</p>

							<p><strong>2. 金融服务</strong></p>
							<p>智能风控、算法交易、客户服务等领域的AI应用日趋成熟。</p>

							<p><strong>3. 自动驾驶</strong></p>
							<p>自动驾驶技术正在从实验室走向实际应用，预计将在未来几年内实现商业化。</p>

							<h2>未来发展趋势</h2>
							<p>随着技术的不断进步，AI将在更多领域发挥重要作用，同时也需要关注AI伦理和安全问题。</p>
						`,
						coverImage: '/static/logo.png',
						source: '智索科技',
						sourceUrl: 'https://www.baidu.com',
						author: '智索编辑部',
						publishTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
						viewCount: 2300,
						likeCount: 156,
						favoriteCount: 89,
						commentCount: 23,
						isLiked: 0,
						isFavorite: 0
					},
					'default-2': {
						articleId: 'default-2',
						title: '数字化转型：企业如何应对',
						description: '企业数字化转型的策略与实践',
						content: `
							<h2>数字化转型的重要性</h2>
							<p>在数字经济时代，企业数字化转型已经不是选择题，而是必答题。</p>

							<h2>转型策略</h2>
							<p>企业需要从技术、组织、文化等多个维度进行全面转型。</p>

							<h2>实施路径</h2>
							<p>建议企业采用渐进式转型策略，逐步推进数字化进程。</p>
						`,
						coverImage: '/static/logo.png',
						source: '科技日报',
						sourceUrl: 'https://www.google.com',
						author: '科技日报记者',
						publishTime: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(),
						viewCount: 1800,
						likeCount: 98,
						favoriteCount: 67,
						commentCount: 15,
						isLiked: 0,
						isFavorite: 0
					},
					'default-3': {
						articleId: 'default-3',
						title: '智能算法在日常生活中的应用',
						description: 'AI如何改变我们的生活方式',
						content: `
							<h2>智能推荐系统</h2>
							<p>从购物到娱乐，智能推荐算法正在改变我们的消费习惯。</p>

							<h2>智能家居</h2>
							<p>AI技术让家居设备更加智能化，提升生活品质。</p>

							<h2>出行服务</h2>
							<p>智能导航、共享出行等服务让出行更加便捷。</p>
						`,
						coverImage: '/static/logo.png',
						source: 'TechCrunch',
						sourceUrl: 'https://techcrunch.com',
						author: 'TechCrunch编辑',
						publishTime: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
						viewCount: 1500,
						likeCount: 78,
						favoriteCount: 45,
						commentCount: 8,
						isLiked: 0,
						isFavorite: 0
					}
				};

				return defaultArticles[this.articleId] || defaultArticles['default-1'];
			},

			// ========== 评论相关方法 ==========

			// 获取评论列表
			async getComments() {
				this.commentsLoading = true;
				this.commentsError = null;

				try {
					const response = await Api.request({
						url: '/v1/comments',
						method: 'GET',
						data: {
							contentId: this.articleId,
							contentType: 'article',
							page: this.currentPage,
							size: this.pageSize
						}
					});

					if (response.data && response.data.code === 0 && response.data.data) {
						const newComments = response.data.data.content || [];

						if (this.currentPage === 0) {
							this.comments = newComments;
						} else {
							this.comments = [...this.comments, ...newComments];
						}

						this.hasMoreComments = newComments.length === this.pageSize;

						// 更新文章评论数
						if (this.article && response.data.data.totalElements !== undefined) {
							this.article.commentCount = response.data.data.totalElements;
						}

						console.log('获取评论成功:', this.comments);
					} else {
						throw new Error(response.data?.message || '获取评论失败');
					}
				} catch (error) {
					console.error('获取评论失败:', error);
					this.commentsError = error.message || '网络错误，请稍后重试';
					// 不再使用默认数据，直接显示空状态
					if (this.currentPage === 0) {
						this.comments = [];
					}
				} finally {
					this.commentsLoading = false;
				}
			},

			// 加载更多评论
			async loadMoreComments() {
				if (this.commentsLoading || !this.hasMoreComments) return;

				this.currentPage += 1;
				await this.getComments();
			},

			// 处理评论组件的发表评论事件
			async handleSubmitComment(commentData) {
				try {
					// 构建请求数据 - 直接使用组件处理后的纯净内容
					const requestData = {
						contentId: this.articleId,
						contentType: 'article',
						content: commentData.content, // 使用组件已经处理过的纯净内容
						parentId: commentData.parentId
					};

					const response = await Api.request({
						url: '/v1/comments',
						method: 'POST',
						data: requestData
					});

					if (response.data && response.data.code === 0) {
						// 评论成功后重新获取评论列表，确保数据准确
						this.currentPage = 0;
						await this.getComments();

						uni.showToast({
							title: '评论成功',
							icon: 'success'
						});
					} else {
						throw new Error(response.data?.message || '评论失败');
					}
				} catch (error) {
					console.error('发表评论失败:', error);
					uni.showToast({
						title: error.message || '评论失败',
						icon: 'error'
					});
					throw error; // 重新抛出错误，让组件知道操作失败
				}
			},

			// 处理评论点赞事件
			async handleToggleCommentLike({ comment }) {
				const originalLiked = comment.isLiked;
				const originalCount = comment.likeCount || 0;

				try {
					const response = await Api.request({
						url: '/v1/likes',
						method: 'POST',
						data: {
							contentId: comment.commentId,
							contentType: 'comment'
						}
					});

					if (response.data && response.data.code === 0) {
						// 从响应中获取点赞状态
						comment.isLiked = response.data.data.isLiked;
						if (comment.isLiked) {
							comment.likeCount = originalCount + 1;
						} else {
							comment.likeCount = Math.max(originalCount - 1, 0);
						}
					} else {
						throw new Error(response.data?.message || '操作失败');
					}
				} catch (error) {
					console.error('评论点赞失败:', error);
					// 恢复原始状态
					comment.isLiked = originalLiked;
					comment.likeCount = originalCount;
					uni.showToast({
						title: error.message || '操作失败',
						icon: 'error'
					});
				}
			},

			// 处理回复点赞事件
			async handleToggleReplyLike({ reply }) {
				const originalLiked = reply.isLiked;
				const originalCount = reply.likeCount || 0;

				try {
					const response = await Api.request({
						url: '/v1/likes',
						method: 'POST',
						data: {
							contentId: reply.commentId,
							contentType: 'comment'
						}
					});

					if (response.data && response.data.code === 0) {
						// 从响应中获取点赞状态
						reply.isLiked = response.data.data.isLiked;
						if (reply.isLiked) {
							reply.likeCount = originalCount + 1;
						} else {
							reply.likeCount = Math.max(originalCount - 1, 0);
						}
					} else {
						throw new Error(response.data?.message || '操作失败');
					}
				} catch (error) {
					console.error('回复点赞失败:', error);
					// 恢复原始状态
					reply.isLiked = originalLiked;
					reply.likeCount = originalCount;
					uni.showToast({
						title: error.message || '操作失败',
						icon: 'error'
					});
				}
			},



			// 滚动到评论区域
			scrollToComments() {
				uni.pageScrollTo({
					selector: '#comments-section',
					duration: 300
				});
			},

			// 评论输入框获得焦点
			onCommentFocus() {
				// 可以在这里添加一些UI反馈
			},

			// 评论输入框失去焦点
			onCommentBlur() {
				// 可以在这里添加一些UI反馈
			},

			// 处理删除评论事件
			async handleDeleteComment({ comment, index }) {
				try {
					const response = await Api.request({
						url: `/v1/comments/${comment.commentId}`,
						method: 'DELETE'
					});

					if (response.data && response.data.code === 0) {
						// 从本地评论列表中移除
						this.comments.splice(index, 1);

						// 更新评论数量
						if (this.article) {
							// 计算删除的总数量（主评论 + 子评论）
							const deletedCount = 1 + (comment.replies ? comment.replies.length : 0);
							this.article.commentCount = Math.max(0, this.article.commentCount - deletedCount);
						}

						uni.showToast({
							title: '删除成功',
							icon: 'success'
						});
					} else {
						throw new Error(response.data?.message || '删除失败');
					}
				} catch (error) {
					console.error('删除评论失败:', error);
					uni.showToast({
						title: error.message || '删除失败',
						icon: 'error'
					});
				}
			},

			// 处理删除回复事件
			async handleDeleteReply({ reply, parentComment, replyIndex }) {
				try {
					const response = await Api.request({
						url: `/v1/comments/${reply.commentId}`,
						method: 'DELETE'
					});

					if (response.data && response.data.code === 0) {
						// 从本地回复列表中移除
						if (parentComment.replies && parentComment.replies.length > replyIndex) {
							parentComment.replies.splice(replyIndex, 1);
						}

						// 更新评论数量
						if (this.article) {
							this.article.commentCount = Math.max(0, this.article.commentCount - 1);
						}

						uni.showToast({
							title: '删除成功',
							icon: 'success'
						});
					} else {
						throw new Error(response.data?.message || '删除失败');
					}
				} catch (error) {
					console.error('删除回复失败:', error);
					uni.showToast({
						title: error.message || '删除失败',
						icon: 'error'
					});
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page-container {
		position: relative;
		min-height: 100vh;
	}

	.page {
		background-color: #f8f9fc;
		min-height: 100vh;
	}

	.navbar {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #fff;
		height: 100rpx;
		position: sticky;
		top: 0;
		z-index: 100;
		box-shadow: 0 2rpx 15rpx rgba(0, 0, 0, 0.05);

		.nav-left, .nav-right {
			width: 60rpx;
			height: 60rpx;
			display: flex;
			align-items: center;
			justify-content: center;
		}

		.nav-title {
			font-size: 32rpx;
			font-weight: bold;
			color: #333;
		}
	}

	.loading-container, .error-container, .empty-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 100rpx 30rpx;
		text-align: center;
	}

	.loading-text, .error-text, .empty-text {
		font-size: 28rpx;
		color: #999;
		margin-bottom: 20rpx;
	}

	.retry-btn {
		background-color: #722ED1;
		color: #fff;
		padding: 16rpx 32rpx;
		border-radius: 20rpx;
		font-size: 26rpx;
		transition: all 0.3s ease;

		&:active {
			opacity: 0.8;
			transform: scale(0.98);
		}
	}

	.article-container {
		padding: 30rpx;
	}

	.article-header {
		margin-bottom: 30rpx;

		.article-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			line-height: 1.4;
			margin-bottom: 20rpx;
		}

		.article-meta {
			display: flex;
			flex-wrap: wrap;
			margin-bottom: 15rpx;

			.meta-item {
				display: flex;
				align-items: center;
				margin-right: 30rpx;
				margin-bottom: 10rpx;
				font-size: 24rpx;
				color: #999;

				text {
					margin-left: 6rpx;
				}
			}
		}

		.article-source {
			display: flex;
			align-items: center;
			font-size: 24rpx;
			padding: 12rpx 0;
			cursor: pointer;
			transition: all 0.3s ease;

			&:active {
				opacity: 0.7;
			}

			.source-label {
				color: #666;
				margin-left: 8rpx;
				margin-right: 8rpx;
			}

			.source-icon {
				width: 32rpx;
				height: 32rpx;
				border-radius: 6rpx;
				overflow: hidden;
				margin-right: 8rpx;
				flex-shrink: 0;

				image {
					width: 100%;
					height: 100%;
				}
			}

			.source-link {
				color: #722ED1;
				flex: 1;
				margin-right: 8rpx;
			}
		}
	}

	.article-cover {
		margin-bottom: 30rpx;
		border-radius: 16rpx;
		overflow: hidden;

		image {
			width: 100%;
			height: 400rpx;
		}
	}

	.article-description {
		font-size: 28rpx;
		color: #666;
		line-height: 1.6;
		margin-bottom: 30rpx;
		padding: 20rpx;
		background-color: #f5f5f5;
		border-radius: 12rpx;
	}

	.article-content {
		font-size: 30rpx;
		color: #333;
		line-height: 1.8;
		margin-bottom: 50rpx;
	}

	.article-actions {
		display: flex;
		justify-content: space-around;
		padding: 30rpx 0;
		border-top: 1px solid #f0f0f0;

		.action-btn {
			display: flex;
			flex-direction: column;
			align-items: center;
			transition: all 0.3s ease;

			&:active {
				transform: scale(0.95);
			}

			text {
				font-size: 24rpx;
				color: #999;
				margin-top: 8rpx;

				&.liked {
					color: #ff4757;
				}

				&.favorited {
					color: #ffa502;
				}
			}

			.action-count {
				font-size: 20rpx;
				color: #999;
				margin-top: 4rpx;
			}
		}
	}

	.article-tags {
		display: flex;
		flex-wrap: wrap;
		margin-bottom: 15rpx;

		.tag-item {
			display: flex;
			align-items: center;
			padding: 6rpx 12rpx;
			border-radius: 8rpx;
			font-size: 22rpx;
			margin-right: 12rpx;
			margin-bottom: 8rpx;

			text {
				font-weight: 500;
			}

			&.news-tag {
				background-color: #F0E7FB;
				color: #722ED1;
			}
		}
	}

</style>
