package com.zhisuo.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 文章实体类
 */
@Data
@TableName("articles")
public class Article {
    
    @TableId
    private String articleId;      // 文章ID
    
    private String title;          // 文章标题
    
    private String description;    // 文章描述(内容第一段)
    
    private String content;        // 文章内容
    
    private String coverImage;     // 封面图URL
    
    private String source;         // 内容来源(cctv.com)
    
    private String sourceUrl;      // 来源URL
    
    private String iconUrl;        // 图标URL(https://news.cctv.com/favicon.ico)
    
    private Integer viewCount;     // 阅读量
    
    private Integer commentCount;  // 评论量
    
    private Integer likeCount;     // 点赞量

    private Date collectTime;      // 收集时间(热点内容)
    
    private Date publishTime;      // 发布时间
    
    private Date createTime;       // 创建时间
    
    private Date updateTime;       // 更新时间
    
    private Integer status;        // 状态(1:已发布,0:草稿,-1:已删除)
} 