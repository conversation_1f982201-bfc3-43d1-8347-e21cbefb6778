package com.zhisuo.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhisuo.app.entity.Tag;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 标签分类Mapper接口
 */
@Mapper
public interface TagMapper extends BaseMapper<Tag> {

    /**
     * 查询内容的标签名称列表
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 标签名称列表
     */
    @Select("SELECT t.tag_name FROM tags t " +
            "INNER JOIN content_tags ct ON t.tag_id = ct.tag_id " +
            "WHERE ct.content_id = #{contentId} AND ct.content_type = #{contentType} AND t.status = 1")
    List<String> selectContentTagNames(@Param("contentId") String contentId, @Param("contentType") String contentType);
}