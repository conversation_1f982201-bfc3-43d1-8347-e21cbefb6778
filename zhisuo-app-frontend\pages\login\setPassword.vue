<template>
	<view class="page">
		<view class="title">
			<view>设置密码<br>提高账号安全</view>
		</view>

		<view class="form">
			<view class="inputs_button">
				<form class="inputs">
					<view class="password">
						<input :type="pwdShow ? 'text' : 'password'" v-model="password" :adjust-position="false"
							placeholder="请输入密码" />
						<uni-icons :type="pwdShow ? 'eye-filled' : 'eye-slash-filled'" size="24" color="#cccccc"
							@click="pwdShow = !pwdShow"></uni-icons>
					</view>
					<view class="password">
						<input :type="confirmPwdShow ? 'text' : 'password'" v-model="confirmPassword"
							:adjust-position="false" placeholder="请确认密码" />
						<uni-icons :type="confirmPwdShow ? 'eye-filled' : 'eye-slash-filled'" size="24" color="#cccccc"
							@click="confirmPwdShow = !confirmPwdShow"></uni-icons>
					</view>
					<view class="tips">
						<text>密码长度为6-20位，建议使用字母、数字和符号组合</text>
					</view>
				</form>
				<view class="button">
					<view @click="savePassword">
						保存
					</view>
				</view>
				<view class="button skip-button">
					<view @click="showSkipConfirm">
						跳过设置
					</view>
				</view>
			</view>
		</view>

		<view class="pact">
			<view>查看<text @click="openAgreement('user')">《用户协议》</text>和<text
					@click="openAgreement('privacy')">《隐私协议》</text></view>
		</view>

		<!-- 协议弹窗 -->
		<agreement-popup :show="showAgreement" :title="agreementTitle" :content="agreementContent"
			:confirm-text="'我知道了'" :require-scroll="false" width="90%" height="75%" @confirm="agreementCancel"
			@cancel="agreementCancel"></agreement-popup>
	</view>
</template>

<script>
	import agreement from '../../common/agreement.js';
	import AgreementPopup from '../../components/AgreementPopup.vue';
	import Api from '../../common/api.js';

	export default {
		components: {
			AgreementPopup
		},
		data() {
			return {
				password: "",
				confirmPassword: "",
				pwdShow: false,
				confirmPwdShow: false,
				userId: "", // 用户ID
				deviceId: "", // 设备ID
				deviceType: "", // 设备类型

				// 协议弹窗相关
				showAgreement: false,
				agreementTitle: '',
				agreementContent: '',
				currentAgreementType: ''
			}
		},
		onLoad(options) {
			// 从参数中获取用户ID
			if (options && options.userId) {
				this.userId = options.userId;
			} else {
				// 若没有userId参数，尝试从用户信息中获取
				const userInfo = uni.getStorageSync('userInfo');
				if (userInfo && userInfo.userId) {
					this.userId = userInfo.userId;
				} else {
					// 没有用户ID，无法设置密码，返回登录页
					uni.showToast({
						title: '用户信息异常，请重新登录',
						icon: 'none',
						duration: 2000
					});
					setTimeout(() => {
						uni.navigateBack();
					}, 2000);
				}
			}
			
			// 初始化设备信息
			this.deviceId = uni.getSystemInfoSync().deviceId || `device_${Date.now()}`;
			this.deviceType = uni.getSystemInfoSync().platform || 'unknown';
			
			// 开发环境提示
			if (process.env.NODE_ENV === 'development') {
				console.log('当前API地址:', Api.BASE_URL);
			}
		},
		methods: {
			savePassword() {
				if (!this.password) {
					uni.showToast({
						title: '请输入密码',
						icon: 'none'
					});
					return;
				}

				if (this.password.length < 6 || this.password.length > 20) {
					uni.showToast({
						title: '密码长度应为6-20位',
						icon: 'none'
					});
					return;
				}

				if (this.password !== this.confirmPassword) {
					uni.showToast({
						title: '两次输入的密码不一致',
						icon: 'none'
					});
					return;
				}

				uni.showLoading({
					title: '保存中',
					mask: true
				});

				// 调用API设置密码
				Api.request({
					url: '/v1/auth/password/set',
					method: 'POST',
					data: {
						userId: this.userId,
						password: this.password,
						deviceId: this.deviceId,
						deviceType: this.deviceType
					}
				}).then(res => {
					uni.hideLoading();
					if (res.data && res.data.code === 0) {
						// 更新token等信息
						const data = res.data.data;
						uni.setStorageSync('token', data.token);
						uni.setStorageSync('refreshToken', data.refreshToken);
						
						uni.showToast({
							title: '密码设置成功',
							icon: 'success'
						});
						
						// 跳转到首页
						setTimeout(() => {
							uni.switchTab({
								url: '/pages/index/index'
							});
						}, 1500);
					} else {
						uni.showToast({
							title: res.data?.message || '密码设置失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('密码设置失败:', err);
					uni.showToast({
						title: '网络异常，请稍后再试',
						icon: 'none'
					});
				});
			},
			showSkipConfirm() {
				uni.showModal({
					title: '提示',
					content: '跳过设置密码后，系统将为您设置默认密码。\n\n您可以稍后在个人中心修改密码，是否确认跳过？',
					confirmText: '确认跳过',
					cancelText: '继续设置',
					success: (res) => {
						if (res.confirm) {
							this.skipSetPassword();
						}
					}
				});
			},
			skipSetPassword() {
				uni.showLoading({
					title: '处理中',
					mask: true
				});
				
				// 调用API跳过密码设置
				Api.request({
					url: `/v1/auth/password/skip`,
					method: 'POST',
					data: {
						userId: this.userId,
						deviceId: this.deviceId,
						deviceType: this.deviceType
					}
				}).then(res => {
					uni.hideLoading();
					if (res.data && res.data.code === 0) {
						// 更新token等信息
						const data = res.data.data;
						uni.setStorageSync('token', data.token);
						uni.setStorageSync('refreshToken', data.refreshToken);
						
						uni.showToast({
							title: '您的默认密码为：123456，可在个人中心修改',
							icon: 'none',
							duration: 3000
						});
						
						// 跳转到首页
						setTimeout(() => {
							uni.switchTab({
								url: '/pages/index/index'
							});
						}, 2000);
					} else {
						uni.showToast({
							title: res.data?.message || '操作失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					uni.hideLoading();
					console.error('跳过密码设置失败:', err);
					uni.showToast({
						title: '网络异常，请稍后再试',
						icon: 'none'
					});
				});
			},
			openAgreement(type) {
				this.currentAgreementType = type;
				this.agreementTitle = type === 'user' ? '用户协议' : '隐私协议';
				this.agreementContent = type === 'user' ? agreement.userAgreement : agreement.privacyAgreement;
				this.showAgreement = true;
			},
			agreementCancel() {
				this.showAgreement = false;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		min-height: 100vh;
		background: url("../../static/images/pageBg.png") no-repeat top center;
		background-size: 100%;
		display: flex;
		flex-direction: column;
		justify-content: space-between;

		.title {
			padding-top: 220rpx;
			padding-bottom: 70rpx;

			view {
				padding-left: 64rpx;
				font-size: 48rpx;
				font-weight: 700;
				color: #383838;
			}
		}

		.form {
			flex: 1;
			margin: 0 32rpx;
			border-radius: 40rpx;
			background-color: #fff;
			overflow: hidden;

			.inputs_button {
				background-color: #fff;

				.inputs {
					padding: 150rpx 32rpx 0;
					margin-bottom: 80rpx;

					.password {
						height: 96rpx;
						border-radius: 20rpx;
						padding: 0 48rpx;
						display: flex;
						align-items: center;
						background-color: #f7fafc;
						margin-bottom: 48rpx;

						input {
							flex: 1;
						}
					}

					.tips {
						padding: 0 48rpx;

						text {
							font-size: 24rpx;
							color: #999;
						}
					}
				}

				.button {
					padding: 0 32rpx;
					margin-bottom: 30rpx;

					view {
						line-height: 96rpx;
						border-radius: 20rpx;
						text-align: center;
						font-size: 32rpx;
						background-color: #3264ed;
						color: #fff;
					}

					&.skip-button {
						view {
							background-color: #f7fafc;
							color: #666;
							border: 1rpx solid #eee;
						}
					}
				}
			}
		}

		.pact {
			display: flex;
			align-items: center;
			justify-content: center;
			padding-bottom: 120rpx;
			font-size: 24rpx;

			text {
				color: #3264ed;
			}
		}
	}
</style>