package com.zhisuo.app.controller;

import com.zhisuo.app.common.ErrorCode;
import com.zhisuo.app.common.Result;
import com.zhisuo.app.common.context.UserContext;
import com.zhisuo.app.common.exception.BusinessException;
import com.zhisuo.app.dto.request.UpdateUserInfoRequest;
import com.zhisuo.app.dto.request.UpdatePasswordRequest;
import com.zhisuo.app.entity.User;
import com.zhisuo.app.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.Map;

@RestController
@RequestMapping("/v1/user")
public class UserController {

    @Autowired
    private UserService userService;

    /**
     * 获取当前登录用户信息
     * @return 用户信息DTO
     */
    @GetMapping("/info")
    public Result<com.zhisuo.app.dto.response.UserInfoResponse> getUserInfo() {
        try {
            com.zhisuo.app.dto.response.UserInfoResponse userInfo = userService.getCurrentUserInfo();
            return Result.success(userInfo);
        } catch (BusinessException e) {
            return Result.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(5000, "获取用户信息失败");
        }
    }

    /**
     * 更新用户信息
     * @param request 用户信息更新请求
     * @return 更新结果
     */
    @PutMapping("/info")
    public Result<com.zhisuo.app.dto.response.UserInfoResponse> updateUserInfo(@RequestBody @Valid UpdateUserInfoRequest request) {
        try {
            String userId = UserContext.getUserId();
            if (!StringUtils.hasText(userId)) {
                return Result.error(ErrorCode.UNAUTHORIZED);
            }
            
            User updatedUser = userService.updateUserInfo(userId, request);
            // 转换为DTO返回
            com.zhisuo.app.dto.response.UserInfoResponse userInfoResponse = com.zhisuo.app.dto.response.UserInfoResponse.fromUser(updatedUser);
            return Result.success(userInfoResponse);
        } catch (BusinessException e) {
            return Result.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(5000, "更新用户信息失败");
        }
    }
    
    /**
     * 修改用户密码
     * @param request 修改密码请求
     * @return 修改结果
     */
    @PutMapping("/password")
    public Result<Boolean> updatePassword(@RequestBody @Valid UpdatePasswordRequest request) {
        try {
            String userId = UserContext.getUserId();
            if (!StringUtils.hasText(userId)) {
                return Result.error(ErrorCode.UNAUTHORIZED);
            }

            boolean success = userService.updatePassword(userId, request);
            if (success) {
                return Result.success(true);
            } else {
                return Result.error(5001, "修改密码失败");
            }
        } catch (BusinessException e) {
            return Result.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(5000, "修改密码失败");
        }
    }

    /**
     * 获取用户统计数据
     * @return 用户统计数据
     */
    @GetMapping("/stats")
    public Result<Map<String, Object>> getUserStats() {
        try {
            String userId = UserContext.getUserId();
            if (!StringUtils.hasText(userId)) {
                return Result.error(ErrorCode.UNAUTHORIZED);
            }

            Map<String, Object> stats = userService.getUserStats(userId);
            return Result.success(stats);
        } catch (BusinessException e) {
            return Result.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(5000, "获取用户统计数据失败");
        }
    }

    /**
     * 获取用户兴趣分布
     * @return 用户兴趣分布数据
     */
    @GetMapping("/interests")
    public Result<Map<String, Object>> getUserInterests() {
        try {
            String userId = UserContext.getUserId();
            if (!StringUtils.hasText(userId)) {
                return Result.error(ErrorCode.UNAUTHORIZED);
            }

            Map<String, Object> interests = userService.getUserInterests(userId);
            return Result.success(interests);
        } catch (BusinessException e) {
            return Result.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(5000, "获取用户兴趣分布失败");
        }
    }

    /**
     * 获取用户最近分析
     * @param page 页码
     * @param size 每页大小
     * @return 用户最近分析列表
     */
    @GetMapping("/analysis")
    public Result<Map<String, Object>> getUserAnalysis(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "10") Integer size) {
        try {
            String userId = UserContext.getUserId();
            if (!StringUtils.hasText(userId)) {
                return Result.error(ErrorCode.UNAUTHORIZED);
            }

            Map<String, Object> analysis = userService.getUserAnalysis(userId, page, size);
            return Result.success(analysis);
        } catch (BusinessException e) {
            return Result.error(e.getCode(), e.getMessage());
        } catch (Exception e) {
            e.printStackTrace();
            return Result.error(5000, "获取用户分析失败");
        }
    }
}