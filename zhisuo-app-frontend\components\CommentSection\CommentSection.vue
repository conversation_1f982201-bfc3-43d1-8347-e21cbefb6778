<template>
	<!-- 评论区卡片 -->
	<view class="card comment-card">
		<view class="section-title">
			<uni-icons type="chatbubble" size="18" color="#722ED1"></uni-icons>
			<text>评论 ({{ formatViewCount(commentCount || 0) }})</text>
		</view>

		<!-- 评论输入框 -->
		<view class="comment-input-box">
			<input type="text"
				v-model="commentText"
				class="comment-input"
				:placeholder="replyingTo ? `回复 @${replyingTo.username}:` : '发表你的看法...'"
				@confirm="submitComment" />
			<view class="send-btn" @click="submitComment" :class="{ disabled: !commentText.trim() || submitting }">
				<uni-icons type="paperplane" size="16" color="#FFFFFF"></uni-icons>
			</view>
		</view>

		<!-- 评论列表 -->
		<view class="comment-list">
			<!-- 加载状态 -->
			<view v-if="loading" class="loading-container">
				<view class="loading-text">正在加载评论...</view>
			</view>

			<!-- 错误状态 -->
			<view v-else-if="error" class="error-container">
				<view class="error-text">{{ error }}</view>
				<view class="retry-btn" @click="$emit('refresh')">重试</view>
			</view>

			<!-- 空状态 -->
			<view v-else-if="comments.length === 0" class="no-comments">
				<text>暂无评论，快来发表你的看法吧</text>
			</view>
			<!-- 评论项 -->
			<view v-else v-for="(comment, index) in comments" :key="comment.commentId || index" class="comment-item">
				<view class="comment-avatar">
					<image :src="comment.userAvatar || '/static/logo.png'" mode="aspectFill"></image>
				</view>
				<view class="comment-content">
					<view class="comment-header">
						<view class="comment-user">{{ comment.username || '智索用户' }}</view>
						<view v-if="isCurrentUserComment(comment)" class="comment-menu" @click="showCommentOptions(comment, index)">
							<text class="menu-dots">⋯</text>
						</view>
					</view>
					<view class="comment-text">{{ comment.content }}</view>
					<view class="comment-time">{{ formatTimeAgo(comment.createTime) }}</view>
					<view class="comment-actions">
						<view class="action-like" @click="toggleCommentLike(comment, index)">
							<uni-icons :type="comment.isLiked ? 'heart-filled' : 'heart'" size="16" :color="comment.isLiked ? '#722ED1' : '#999999'"></uni-icons>
							<text>{{ comment.likeCount || 0 }}</text>
						</view>
						<view class="action-reply" @click="replyComment(comment, index)">
							<uni-icons type="chatbubble" size="16" color="#999999"></uni-icons>
							<text>回复</text>
						</view>
						<!-- 回复展开/收起按钮 -->
						<view v-if="comment.replies && comment.replies.length > 0" class="action-toggle-replies" @click="toggleReplies(comment, index)">
							<uni-icons :type="comment.showReplies ? 'arrowup' : 'arrowdown'" size="16" color="#999999"></uni-icons>
							<text>{{ comment.showReplies ? '收起' : '展开' }}回复({{ comment.replies.length }})</text>
						</view>
					</view>

					<!-- 回复列表 -->
					<view v-if="comment.replies && comment.replies.length > 0 && comment.showReplies" class="replies-list">
						<view
							v-for="(reply, replyIndex) in comment.replies"
							:key="reply.commentId || replyIndex"
							class="reply-item"
						>
							<view class="reply-avatar">
								<image :src="reply.userAvatar || '/static/logo.png'" mode="aspectFill"></image>
							</view>
							<view class="reply-content">
								<view class="reply-header">
									<view class="reply-user">{{ reply.username || '智索用户' }}</view>
									<view v-if="isCurrentUserComment(reply)" class="reply-menu" @click="showReplyOptions(reply, comment, index, replyIndex)">
										<text class="menu-dots">⋯</text>
									</view>
								</view>
								<view class="reply-text">
									<text v-if="reply.replyToUsername" class="reply-to">@{{ reply.replyToUsername }} </text>
									{{ reply.content }}
								</view>
								<view class="reply-time">{{ formatTimeAgo(reply.createTime) }}</view>
								<view class="reply-actions">
									<view class="action-like" @click="toggleReplyLike(reply, index, replyIndex)">
										<uni-icons :type="reply.isLiked ? 'heart-filled' : 'heart'" size="14" :color="reply.isLiked ? '#722ED1' : '#999999'"></uni-icons>
										<text>{{ reply.likeCount || 0 }}</text>
									</view>
									<view class="action-reply" @click="replyToReply(reply, comment, index)">
										<uni-icons type="chatbubble" size="14" color="#999999"></uni-icons>
										<text>回复</text>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 加载更多 -->
			<view v-if="hasMoreComments && !loading" class="load-more" @click="$emit('load-more')">
				<text>加载更多评论</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'CommentSection',
		props: {
			// 内容ID
			contentId: {
				type: String,
				required: true
			},
			// 内容类型
			contentType: {
				type: String,
				default: 'article'
			},
			// 评论列表
			comments: {
				type: Array,
				default: () => []
			},
			// 评论总数
			commentCount: {
				type: Number,
				default: 0
			},
			// 加载状态
			loading: {
				type: Boolean,
				default: false
			},
			// 错误信息
			error: {
				type: String,
				default: ''
			},
			// 是否有更多评论
			hasMoreComments: {
				type: Boolean,
				default: false
			}
		},
		data() {
			return {
				commentText: '',
				replyingTo: null,
				submitting: false
			}
		},
		watch: {
			// 监听评论数据变化，初始化回复显示状态
			comments: {
				handler(newComments) {
					if (newComments && newComments.length > 0) {
						newComments.forEach(comment => {
							if (comment.replies && comment.replies.length > 0) {
								// 如果没有设置 showReplies 属性，默认为 false（收起状态）
								if (comment.showReplies === undefined) {
									this.$set(comment, 'showReplies', false);
								}
							}
						});
					}
				},
				immediate: true,
				deep: true
			}
		},
		methods: {
			// 格式化时间显示 - 直接显示年月日时分
			formatTimeAgo(timeStr) {
				if (!timeStr) return '';

				const time = new Date(timeStr);

				// 检查时间是否有效
				if (isNaN(time.getTime())) return '';

				const year = time.getFullYear();
				const month = (time.getMonth() + 1).toString().padStart(2, '0');
				const day = time.getDate().toString().padStart(2, '0');
				const hours = time.getHours().toString().padStart(2, '0');
				const minutes = time.getMinutes().toString().padStart(2, '0');

				return `${year}-${month}-${day} ${hours}:${minutes}`;
			},

			// 格式化数量显示
			formatViewCount(count) {
				if (!count || count === 0) return '0';
				
				if (count >= 10000) {
					return (count / 10000).toFixed(1) + 'w';
				} else if (count >= 1000) {
					return (count / 1000).toFixed(1) + 'k';
				} else {
					return count.toString();
				}
			},



			// 发表评论
			async submitComment() {
				if (!this.commentText.trim() || this.submitting) {
					return;
				}

				this.submitting = true;

				try {
					// 处理评论内容，去掉所有@用户名部分，只保留用户实际输入的内容
					let content = this.commentText.trim();

					// 如果是回复，去掉所有@用户名部分
					if (this.replyingTo) {
						const username = this.replyingTo.username;

						// 转义用户名中的特殊字符
						const escapedUsername = username.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');

						// 使用正则表达式去掉开头所有的@用户名（包括重复的和空格）
						const regex = new RegExp(`^(@${escapedUsername}\\s*)+`, 'g');
						content = content.replace(regex, '').trim();
					}

					// 简单的回复逻辑：parent_id 就是被回复评论的 comment_id
					let parentId = null;
					let replyTo = this.replyingTo;

					if (this.replyingTo) {
						// 直接使用被回复评论的ID作为parent_id
						parentId = this.replyingTo.commentId;
					}

					this.$emit('submit-comment', {
						content: content, // 纯净的用户输入内容，不包含@用户名
						parentId: parentId,
						replyTo: replyTo
					});

					this.commentText = '';
					this.replyingTo = null;
				} catch (error) {
					console.error('发表评论失败:', error);
				} finally {
					this.submitting = false;
				}
			},

			// 回复评论
			replyComment(comment, index) {
				this.replyingTo = comment;
				this.commentText = `@${comment.username} `;
			},

			// 回复回复
			replyToReply(reply, parentComment, parentIndex) {
				// 简化逻辑：直接回复该评论
				this.replyingTo = reply;
				this.commentText = `@${reply.username} `;
			},

			// 切换回复显示/隐藏
			toggleReplies(comment, index) {
				// 使用 Vue.set 确保响应式更新
				this.$set(comment, 'showReplies', !comment.showReplies);
			},

			// 切换评论点赞状态
			async toggleCommentLike(comment, index) {
				this.$emit('toggle-comment-like', { comment, index });
			},

			// 切换回复点赞状态
			async toggleReplyLike(reply, commentIndex, replyIndex) {
				this.$emit('toggle-reply-like', { reply, commentIndex, replyIndex });
			},

			// 评论输入框获得焦点
			onCommentFocus() {
				this.$emit('comment-focus');
			},

			// 评论输入框失去焦点
			onCommentBlur() {
				this.$emit('comment-blur');
			},

			// 判断是否是当前用户的评论
			isCurrentUserComment(comment) {
				const userInfo = uni.getStorageSync('userInfo');
				const currentUserId = userInfo ? userInfo.userId : null;
				return currentUserId && comment.userId === currentUserId;
			},

			// 显示评论操作选项
			showCommentOptions(comment, index) {
				const hasReplies = comment.replies && comment.replies.length > 0;
				const replyText = hasReplies ? `（将同时删除${comment.replies.length}条回复）` : '';

				uni.showActionSheet({
					itemList: [`删除评论${replyText}`],
					itemColor: '#ff4757',
					success: (res) => {
						if (res.tapIndex === 0) {
							this.confirmDeleteComment(comment, index);
						}
					}
				});
			},

			// 显示回复操作选项
			showReplyOptions(reply, parentComment, commentIndex, replyIndex) {
				uni.showActionSheet({
					itemList: ['删除回复'],
					itemColor: '#ff4757',
					success: (res) => {
						if (res.tapIndex === 0) {
							this.confirmDeleteReply(reply, parentComment, commentIndex, replyIndex);
						}
					}
				});
			},

			// 确认删除评论
			confirmDeleteComment(comment, index) {
				const hasReplies = comment.replies && comment.replies.length > 0;
				const message = hasReplies
					? `确定要删除这条评论吗？\n将同时删除${comment.replies.length}条回复，此操作不可恢复。`
					: '确定要删除这条评论吗？此操作不可恢复。';

				uni.showModal({
					title: '删除确认',
					content: message,
					confirmColor: '#ff4757',
					success: (res) => {
						if (res.confirm) {
							this.deleteComment(comment, index);
						}
					}
				});
			},

			// 确认删除回复
			confirmDeleteReply(reply, parentComment, commentIndex, replyIndex) {
				uni.showModal({
					title: '删除确认',
					content: '确定要删除这条回复吗？此操作不可恢复。',
					confirmColor: '#ff4757',
					success: (res) => {
						if (res.confirm) {
							this.deleteReply(reply, parentComment, commentIndex, replyIndex);
						}
					}
				});
			},

			// 删除评论
			async deleteComment(comment, index) {
				try {
					this.$emit('delete-comment', { comment, index });
				} catch (error) {
					console.error('删除评论失败:', error);
					uni.showToast({
						title: '删除失败',
						icon: 'error'
					});
				}
			},

			// 删除回复
			async deleteReply(reply, parentComment, commentIndex, replyIndex) {
				try {
					this.$emit('delete-reply', { reply, parentComment, commentIndex, replyIndex });
				} catch (error) {
					console.error('删除回复失败:', error);
					uni.showToast({
						title: '删除失败',
						icon: 'error'
					});
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.comment-card {
		background: #FFFFFF;
		border-radius: 20rpx;
		padding: 30rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);

		.section-title {
			display: flex;
			align-items: center;
			margin-bottom: 30rpx;
			font-size: 32rpx;
			font-weight: 600;
			color: #333333;

			text {
				margin-left: 10rpx;
			}
		}

		.comment-input-box {
			display: flex;
			align-items: center;
			background: #F8F9FA;
			border-radius: 50rpx;
			padding: 20rpx 30rpx;
			margin-bottom: 30rpx;

			.comment-input {
				flex: 1;
				background: transparent;
				border: none;
				font-size: 28rpx;
				color: #333333;

				&::placeholder {
					color: #999999;
				}
			}

			.send-btn {
				width: 60rpx;
				height: 60rpx;
				background: linear-gradient(135deg, #722ED1, #9254DE);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-left: 20rpx;
				transition: all 0.3s ease;

				&:active {
					transform: scale(0.95);
				}

				&.disabled {
					background: #E5E7EB;
					opacity: 0.6;
				}
			}
		}

		.comment-list {
			.loading-container, .error-container {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				padding: 60rpx 30rpx;
				text-align: center;

				.loading-text, .error-text {
					font-size: 28rpx;
					color: #999999;
					margin-bottom: 20rpx;
				}

				.retry-btn {
					background-color: #722ED1;
					color: #FFFFFF;
					padding: 16rpx 32rpx;
					border-radius: 20rpx;
					font-size: 26rpx;
					transition: all 0.3s ease;

					&:active {
						opacity: 0.8;
						transform: scale(0.98);
					}
				}
			}

			.no-comments {
				text-align: center;
				padding: 60rpx 30rpx;
				color: #999999;
				font-size: 28rpx;
			}

			.comment-item {
				display: flex;
				padding: 30rpx 0;
				border-bottom: 1rpx solid #F0F0F0;

				&:last-child {
					border-bottom: none;
				}

				.comment-avatar {
					width: 80rpx;
					height: 80rpx;
					border-radius: 50%;
					overflow: hidden;
					margin-right: 20rpx;
					flex-shrink: 0;

					image {
						width: 100%;
						height: 100%;
					}
				}

				.comment-content {
					flex: 1;

					.comment-header {
						display: flex;
						justify-content: space-between;
						align-items: center;
						margin-bottom: 10rpx;

						.comment-user {
							font-size: 28rpx;
							font-weight: 600;
							color: #333333;
						}

						.comment-menu {
							padding: 10rpx;
							margin: -10rpx;

							.menu-dots {
								font-size: 32rpx;
								color: #999999;
								font-weight: bold;
								line-height: 1;
							}
						}
					}

					.comment-text {
						font-size: 28rpx;
						color: #333333;
						line-height: 1.6;
						margin-bottom: 15rpx;
					}

					.comment-time {
						font-size: 24rpx;
						color: #999999;
						margin-bottom: 15rpx;
					}

					.comment-actions {
						display: flex;
						align-items: center;

						.action-like, .action-reply, .action-toggle-replies {
							display: flex;
							align-items: center;
							margin-right: 40rpx;
							transition: all 0.3s ease;

							&:active {
								opacity: 0.7;
							}

							text {
								font-size: 24rpx;
								color: #999999;
								margin-left: 8rpx;
							}
						}

						.action-like text {
							color: #999;
						}

						.action-toggle-replies {
							margin-left: auto; // 推到右侧
							margin-right: 0;

							text {
								color: #999;
								font-weight: 500;
							}
						}
					}

					.replies-list {
						margin-top: 20rpx;
						margin-left: 0; // 回复对齐到主评论内容区域的左侧

						.reply-item {
							display: flex;
							padding: 20rpx 0;
							border-bottom: 1rpx solid #F0F0F0;

							&:last-child {
								border-bottom: none;
							}

							.reply-avatar {
								width: 60rpx;
								height: 60rpx;
								border-radius: 50%;
								overflow: hidden;
								margin-right: 15rpx;
								flex-shrink: 0;

								image {
									width: 100%;
									height: 100%;
								}
							}

							.reply-content {
								flex: 1;

								.reply-header {
									display: flex;
									justify-content: space-between;
									align-items: center;
									margin-bottom: 8rpx;

									.reply-user {
										font-size: 26rpx;
										font-weight: 600;
										color: #333333;
									}

									.reply-menu {
										padding: 8rpx;
										margin: -8rpx;

										.menu-dots {
											font-size: 28rpx;
											color: #999999;
											font-weight: bold;
											line-height: 1;
										}
									}
								}

								.reply-text {
									font-size: 26rpx;
									color: #333333;
									line-height: 1.6;
									margin-bottom: 10rpx;

									.reply-to {
										color: #722ED1;
										font-weight: 600;
									}
								}

								.reply-time {
									font-size: 22rpx;
									color: #999999;
									margin-bottom: 10rpx;
								}

								.reply-actions {
									display: flex;
									align-items: center;

									.action-like, .action-reply {
										display: flex;
										align-items: center;
										margin-right: 30rpx;
										transition: all 0.3s ease;

										&:active {
											opacity: 0.7;
										}

										text {
											font-size: 22rpx;
											color: #999999;
											margin-left: 6rpx;
										}
									}

									.action-like text {
										color: #999;
									}
								}
							}
						}
					}
				}
			}
		}

		.load-more {
			text-align: center;
			padding: 30rpx;
			color: #722ED1;
			font-size: 28rpx;
			transition: all 0.3s ease;

			&:active {
				opacity: 0.7;
			}
		}
	}
</style>
