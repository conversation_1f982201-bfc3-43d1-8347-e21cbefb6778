package com.zhisuo.app.config;

import com.zhisuo.app.service.ArticleService;
import com.zhisuo.app.service.HotTopicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 定时任务配置
 */
@Slf4j
@Configuration
@EnableScheduling
public class ScheduleConfig {

    @Autowired
    private HotTopicService hotTopicService;

    @Autowired
    private ArticleService articleService;
    
    /**
     * 每30分钟更新一次热点话题
     * cron表达式：秒 分 时 日 月 周
     * 0 0/30 * * * * 表示每30分钟执行一次（在每小时的0分和30分执行）
     */
    @Scheduled(cron = "0 0/30 * * * *")
    public void updateHotTopics() {
        log.info("定时任务开始执行：更新热点话题");
        try {
            hotTopicService.fetchAndUpdateHotTopics();
        } catch (Exception e) {
            log.error("更新热点话题定时任务执行异常", e);
        }
        log.info("定时任务执行完成：更新热点话题");
    }

    /**
     * 每天上午9点和下午3点更新央视新闻文章
     * 参考Python脚本的配置：["09:00", "15:00"]
     * cron表达式：秒 分 时 日 月 周
     * 0 0 9,15 * * * 表示每天9点和15点执行
     */
    @Scheduled(cron = "0 0 9,15 * * *")
    public void updateCCTVNews() {
        log.info("定时任务开始执行：更新央视新闻文章");
        try {
            articleService.fetchAndUpdateCCTVNews();
        } catch (Exception e) {
            log.error("更新央视新闻文章定时任务执行异常", e);
        }
        log.info("定时任务执行完成：更新央视新闻文章");
    }
}