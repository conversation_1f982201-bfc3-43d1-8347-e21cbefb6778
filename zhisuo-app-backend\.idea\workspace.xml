<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="a33ca965-5dc2-4194-b605-2c65dbe7a1f1" name="更改" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="localRepository" value="D:\devlop-tool\java\LocalRepository" />
        <option name="mavenHome" value="D:/devlop-tool/java/apache-maven-3.9.4" />
        <option name="userSettingsFile" value="D:\devlop-tool\java\apache-maven-3.9.4\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2zLz4k5iFwMfnyi4QFwqGKdFLYv" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/Desktop/ruoyi&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;项目&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.29770115&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;preferences.pluginManager&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  }
}</component>
  <component name="RunManager" selected="Spring Boot.ZhisuoAppApplication">
    <configuration name="CCTVNewsServiceTest.testFetchAndUpdateCCTVNews" type="JUnit" factoryName="JUnit" temporary="true" nameIsGenerated="true">
      <module name="zhisuo-app" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.zhisuo.app.service.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <option name="PACKAGE_NAME" value="com.zhisuo.app.service" />
      <option name="MAIN_CLASS_NAME" value="com.zhisuo.app.service.CCTVNewsServiceTest" />
      <option name="METHOD_NAME" value="testFetchAndUpdateCCTVNews" />
      <option name="TEST_OBJECT" value="method" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="ZhisuoAppApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="zhisuo-app" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.zhisuo.app.ZhisuoAppApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="JUnit.CCTVNewsServiceTest.testFetchAndUpdateCCTVNews" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="应用程序级" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="默认任务">
      <changelist id="a33ca965-5dc2-4194-b605-2c65dbe7a1f1" name="更改" comment="" />
      <created>1699846319148</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1699846319148</updated>
      <workItem from="1751524953620" duration="4466000" />
      <workItem from="1751544747797" duration="2679000" />
      <workItem from="1751552915527" duration="5002000" />
      <workItem from="1751593698320" duration="1912000" />
      <workItem from="1751607045302" duration="9074000" />
      <workItem from="1751679900443" duration="6411000" />
      <workItem from="1751808489258" duration="2169000" />
      <workItem from="1751871558492" duration="626000" />
      <workItem from="1751891907516" duration="3241000" />
      <workItem from="1751976523977" duration="5303000" />
      <workItem from="1752063615171" duration="6164000" />
      <workItem from="1752158328961" duration="2839000" />
      <workItem from="1752239622726" duration="3026000" />
      <workItem from="1752324370436" duration="7723000" />
      <workItem from="1752383360685" duration="3278000" />
      <workItem from="1752411160175" duration="3358000" />
      <workItem from="1752507335427" duration="6000" />
      <workItem from="1752507423956" duration="773000" />
      <workItem from="1752587723712" duration="7023000" />
      <workItem from="1752673941467" duration="2883000" />
      <workItem from="1752734975207" duration="3725000" />
      <workItem from="1752758783606" duration="3767000" />
      <workItem from="1752827271401" duration="127000" />
      <workItem from="1752905931399" duration="3553000" />
      <workItem from="1752928667303" duration="761000" />
      <workItem from="1753017673889" duration="1919000" />
      <workItem from="1753089146873" duration="1824000" />
      <workItem from="1753101008338" duration="2003000" />
      <workItem from="1753165043016" duration="6795000" />
      <workItem from="1753185847181" duration="5739000" />
      <workItem from="1753273646979" duration="5444000" />
      <workItem from="1753359732755" duration="7879000" />
      <workItem from="1753428780450" duration="2396000" />
      <workItem from="1753445523762" duration="1264000" />
      <workItem from="1753521055437" duration="605000" />
      <workItem from="1753533942532" duration="729000" />
      <workItem from="1753688028665" duration="8350000" />
      <workItem from="1753704856865" duration="2824000" />
      <workItem from="1753768334298" duration="5754000" />
      <workItem from="1753791195855" duration="5481000" />
      <workItem from="1754018825219" duration="6247000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
</project>