package com.zhisuo.app.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhisuo.app.entity.Tag;
import com.zhisuo.app.mapper.TagMapper;
import com.zhisuo.app.service.TagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 标签分类Service实现类
 */
@Slf4j
@Service
public class TagServiceImpl extends ServiceImpl<TagMapper, Tag> implements TagService {
    
    @Override
    public Tag getByTagName(String tagName) {
        if (tagName == null || tagName.trim().isEmpty()) {
            return null;
        }
        
        LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Tag::getTagName, tagName)
                .eq(Tag::getStatus, 1);
        
        return getOne(queryWrapper);
    }
    
    @Override
    public List<Tag> getTagsByCategory(String category) {
        LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Tag::getStatus, 1)
                .orderByAsc(Tag::getCreateTime);
        
        return list(queryWrapper);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Tag createTagIfNotExists(String tagName, String category) {
        if (tagName == null || tagName.trim().isEmpty()) {
            return null;
        }
        
        // 先查询是否已存在
        Tag existingTag = getByTagName(tagName);
        if (existingTag != null) {
            return existingTag;
        }
        
        // 创建新标签
        Tag newTag = new Tag();
        newTag.setTagId(IdUtil.fastSimpleUUID());
        newTag.setTagName(tagName);
        // 不再设置category字段
        newTag.setCreateTime(new Date());
        newTag.setStatus(1);
        
        save(newTag);
        log.info("创建新标签: {}", tagName);
        
        return newTag;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<Tag> batchCreateTagsIfNotExist(List<String> tagNames, String category) {
        if (tagNames == null || tagNames.isEmpty()) {
            return new ArrayList<>();
        }
        
        // 去重
        List<String> distinctTagNames = tagNames.stream()
                .filter(name -> name != null && !name.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());
        
        List<Tag> result = new ArrayList<>();
        
        // 查询已存在的标签
        LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.in(Tag::getTagName, distinctTagNames)
                .eq(Tag::getStatus, 1);
        
        List<Tag> existingTags = list(queryWrapper);
        
        // 记录已存在的标签名
        List<String> existingTagNames = existingTags.stream()
                .map(Tag::getTagName)
                .collect(Collectors.toList());
        
        // 添加已存在的标签到结果集
        result.addAll(existingTags);
        
        // 创建不存在的标签
        List<Tag> newTags = new ArrayList<>();
        for (String tagName : distinctTagNames) {
            if (!existingTagNames.contains(tagName)) {
                Tag newTag = new Tag();
                newTag.setTagId(IdUtil.fastSimpleUUID());
                newTag.setTagName(tagName);
                // 不再设置category字段
                newTag.setCreateTime(new Date());
                newTag.setStatus(1);
                
                newTags.add(newTag);
            }
        }
        
        // 批量保存新标签
        if (!newTags.isEmpty()) {
            saveBatch(newTags);
            log.info("批量创建新标签, 数量: {}", newTags.size());
            result.addAll(newTags);
        }
        
        return result;
    }

    @Override
    public List<String> getContentTagNames(String contentId, String contentType) {
        List<String> tagNames = new ArrayList<>();

        try {
            // 通过原生SQL查询内容标签
            tagNames = baseMapper.selectContentTagNames(contentId, contentType);
            log.info("获取内容{}({})的标签成功，共{}个标签", contentId, contentType, tagNames.size());

        } catch (Exception e) {
            log.error("获取内容{}({})的标签失败: {}", contentId, contentType, e.getMessage(), e);
        }

        return tagNames;
    }
}