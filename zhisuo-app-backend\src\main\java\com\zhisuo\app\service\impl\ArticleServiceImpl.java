package com.zhisuo.app.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhisuo.app.entity.Article;
import com.zhisuo.app.mapper.ArticleMapper;
import com.zhisuo.app.service.ArticleService;
import com.zhisuo.app.service.ContentTagService;
import lombok.extern.slf4j.Slf4j;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.PostConstruct;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 文章Service实现类
 */
@Slf4j
@Service
public class ArticleServiceImpl extends ServiceImpl<ArticleMapper, Article> implements ArticleService {

    @Autowired
    private ContentTagService contentTagService;

    @Value("${cctv.news.url:https://news.cctv.com/2019/07/gaiban/cmsdatainterface/page/news_1.jsonp}")
    private String cctvNewsUrl;

    @Value("${cctv.news.max-news:20}")
    private Integer maxNews;

    @Value("${cctv.news.timeout:10000}")
    private Integer timeout;

    @Value("${cctv.news.delay:1000}")
    private Integer delay;

    private static final String USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/130.0.0.0 Safari/537.36";
    private static final String CCTV_SOURCE = "cctv.com";
    private static final String CCTV_ICON_URL = "https://news.cctv.com/favicon.ico";

    /**
     * 应用启动时立即执行一次文章数据获取
     */
    @PostConstruct
    public void init() {
        log.info("应用启动，开始初始化文章数据...");
        try {
            fetchAndUpdateCCTVNews();
            log.info("文章数据初始化完成");
        } catch (Exception e) {
            log.error("文章数据初始化失败: {}", e.getMessage(), e);
        }
    }

    @Override
    public List<Article> getArticleList(Integer limit) {
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Article::getStatus, 1)
                .orderByDesc(Article::getPublishTime);
        
        if (limit != null && limit > 0) {
            queryWrapper.last("LIMIT " + limit);
        }
        
        return list(queryWrapper);
    }
    
    @Override
    public Page<Article> getArticlePage(Page<Article> page, String source) {
        return getArticlePage(page, source, "publishTime", "desc");
    }

    @Override
    public Page<Article> getArticlePage(Page<Article> page, String source, String sortBy, String sortOrder) {
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Article::getStatus, 1);

        if (source != null && !source.isEmpty()) {
            queryWrapper.eq(Article::getSource, source);
        }

        // 动态排序
        if ("viewCount".equals(sortBy)) {
            if ("desc".equals(sortOrder)) {
                queryWrapper.orderByDesc(Article::getViewCount);
            } else {
                queryWrapper.orderByAsc(Article::getViewCount);
            }
        } else if ("commentCount".equals(sortBy)) {
            if ("desc".equals(sortOrder)) {
                queryWrapper.orderByDesc(Article::getCommentCount);
            } else {
                queryWrapper.orderByAsc(Article::getCommentCount);
            }
        } else if ("likeCount".equals(sortBy)) {
            if ("desc".equals(sortOrder)) {
                queryWrapper.orderByDesc(Article::getLikeCount);
            } else {
                queryWrapper.orderByAsc(Article::getLikeCount);
            }
        } else {
            // 默认按发布时间排序
            if ("desc".equals(sortOrder)) {
                queryWrapper.orderByDesc(Article::getPublishTime);
            } else {
                queryWrapper.orderByAsc(Article::getPublishTime);
            }
        }

        return page(page, queryWrapper);
    }
    
    @Override
    public Page<Article> getTodayArticlePage(Page<Article> page, String source) {
        // 获取今天的开始和结束时间
        LocalDate today = LocalDate.now();
        LocalDateTime todayStart = LocalDateTime.of(today, LocalTime.MIN);
        LocalDateTime todayEnd = LocalDateTime.of(today, LocalTime.MAX);
        
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Article::getStatus, 1)
                .ge(Article::getCollectTime, todayStart)
                .le(Article::getCollectTime, todayEnd);
        
        if (source != null && !source.isEmpty()) {
            queryWrapper.eq(Article::getSource, source);
        }
        
        queryWrapper.orderByDesc(Article::getPublishTime);
        
        return page(page, queryWrapper);
    }
    
    @Override
    public void updateViewCount(String articleId) {
        Article article = getById(articleId);
        if (article != null) {
            article.setViewCount(article.getViewCount() == null ? 1 : article.getViewCount() + 1);
            article.setUpdateTime(new Date());
            updateById(article);
            log.info("更新文章浏览量: articleId={}, viewCount={}", articleId, article.getViewCount());
        }
    }
    
    @Override
    public void updateLikeCount(String articleId) {
        updateLikeCount(articleId, 1);
    }

    @Override
    public void updateLikeCount(String articleId, int increment) {
        Article article = getById(articleId);
        if (article != null) {
            int newCount = Math.max((article.getLikeCount() != null ? article.getLikeCount() : 0) + increment, 0);
            article.setLikeCount(newCount);
            article.setUpdateTime(new Date());
            updateById(article);
            log.info("更新文章点赞量: articleId={}, increment={}, newCount={}", articleId, increment, newCount);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fetchAndUpdateCCTVNews() {
        try {
            log.info("开始获取央视新闻数据...");
            List<Article> newArticles = fetchCCTVNews();

            if (newArticles.isEmpty()) {
                log.info("未获取到新的央视新闻数据");
                return;
            }

            log.info("获取到 {} 条央视新闻", newArticles.size());

            // 批量保存新文章
            for (Article article : newArticles) {
                // 检查是否已存在相同的文章（根据来源URL判断）
                LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
                queryWrapper.eq(Article::getSourceUrl, article.getSourceUrl());
                Article existingArticle = getOne(queryWrapper);

                if (existingArticle == null) {
                    // 设置文章ID和时间戳
                    article.setArticleId(IdUtil.simpleUUID());
                    article.setCreateTime(new Date());
                    article.setUpdateTime(new Date());
                    article.setStatus(1); // 已发布状态

                    save(article);
                    log.info("保存新文章: {}", article.getTitle());

                    // 为新文章添加"新闻"标签
                    addArticleNewsTag(article.getArticleId());
                } else {
                    log.debug("文章已存在，跳过: {}", article.getTitle());
                }
            }

            log.info("央视新闻数据更新完成");
        } catch (Exception e) {
            log.error("获取或更新央视新闻数据时出错: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    @Override
    public List<Article> getArticlesBySource(String source, Integer limit) {
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Article::getStatus, 1)
                .eq(Article::getSource, source)
                .orderByDesc(Article::getPublishTime);
        
        if (limit != null && limit > 0) {
            queryWrapper.last("LIMIT " + limit);
        }
        
        return list(queryWrapper);
    }
    


    @Override
    public void updateCommentCount(String articleId, int increment) {
        Article article = getById(articleId);
        if (article != null) {
            int newCount = Math.max((article.getCommentCount() != null ? article.getCommentCount() : 0) + increment, 0);
            article.setCommentCount(newCount);
            article.setUpdateTime(new Date());
            updateById(article);
            log.info("更新文章评论数: articleId={}, increment={}, newCount={}", articleId, increment, newCount);
        }
    }

    /**
     * 从央视网获取热点新闻数据
     *
     * @return 文章列表
     */
    private List<Article> fetchCCTVNews() {
        List<Article> articles = new ArrayList<>();

        try {
            log.info("开始获取央视网热点新闻...");

            // 发送HTTP请求获取新闻数据
            HttpResponse response = HttpRequest.get(cctvNewsUrl)
                    .header("User-Agent", USER_AGENT)
                    .timeout(timeout)
                    .execute();

            if (!response.isOk()) {
                log.error("获取央视新闻数据失败，HTTP状态码: {}", response.getStatus());
                return articles;
            }

            String responseBody = response.body();

            // 处理JSONP格式，去掉前后的函数调用
            String jsonData = responseBody.trim();
            if (jsonData.startsWith("news(") && jsonData.endsWith(")")) {
                jsonData = jsonData.substring(5, jsonData.length() - 1);
            }

            JSONObject jsonObject = JSONUtil.parseObj(jsonData);
            JSONObject data = jsonObject.getJSONObject("data");
            if (data == null) {
                log.warn("央视新闻数据格式异常，未找到data字段");
                return articles;
            }

            JSONArray newsList = data.getJSONArray("list");
            if (newsList == null || newsList.isEmpty()) {
                log.warn("央视新闻列表为空");
                return articles;
            }

            // 处理新闻列表，限制数量
            int processCount = Math.min(newsList.size(), maxNews);
            for (int i = 0; i < processCount; i++) {
                JSONObject newsItem = newsList.getJSONObject(i);

                try {
                    Article article = convertToArticle(newsItem);
                    if (article != null) {
                        articles.add(article);
                        log.debug("成功转换新闻: {}", article.getTitle());
                    }

                    // 避免频繁请求
                    if (i < processCount - 1) {
                        Thread.sleep(delay);
                    }
                } catch (Exception e) {
                    log.error("处理新闻项时出错: {}", e.getMessage(), e);
                }
            }

            log.info("成功获取{}条央视新闻", articles.size());

        } catch (Exception e) {
            log.error("获取央视新闻数据时出错: {}", e.getMessage(), e);
        }

        return articles;
    }

    /**
     * 获取新闻详情内容
     *
     * @param url 新闻URL
     * @return 新闻内容
     */
    private String fetchNewsContent(String url) {
        try {
            log.debug("获取新闻详情: {}", url);

            HttpResponse response = HttpRequest.get(url)
                    .header("User-Agent", USER_AGENT)
                    .timeout(timeout)
                    .execute();

            if (!response.isOk()) {
                log.warn("获取新闻详情失败，HTTP状态码: {}, URL: {}", response.getStatus(), url);
                return "无法获取内容";
            }

            String html = response.body();
            Document doc = Jsoup.parse(html);

            // 央视网新闻内容通常在class为"content_area"的div中
            Element contentDiv = doc.selectFirst("div.content_area");
            if (contentDiv == null) {
                // 尝试其他可能的内容区域
                contentDiv = doc.selectFirst("div.text_area");
                if (contentDiv == null) {
                    contentDiv = doc.selectFirst("div.cnt_bd");
                }
                if (contentDiv == null) {
                    contentDiv = doc.selectFirst("div.content");
                }
            }

            if (contentDiv != null) {
                // 获取所有段落文本
                Elements paragraphs = contentDiv.select("p");
                if (!paragraphs.isEmpty()) {
                    StringBuilder content = new StringBuilder();
                    for (Element p : paragraphs) {
                        String text = p.text().trim();
                        if (StrUtil.isNotBlank(text)) {
                            content.append(text).append("\n");
                        }
                    }
                    return content.toString().trim();
                } else {
                    // 如果没有找到段落，直接获取内容区域的文本
                    return contentDiv.text().trim();
                }
            }

            return "无法获取内容";

        } catch (Exception e) {
            log.error("获取新闻内容失败: {}, URL: {}", e.getMessage(), url);
            return "获取内容失败";
        }
    }

    /**
     * 将JSON新闻数据转换为Article实体
     */
    private Article convertToArticle(JSONObject newsItem) {
        try {
            Article article = new Article();

            // 映射字段：'新闻ID'对应'文章ID'
            String newsId = newsItem.getStr("id");
            if (StrUtil.isBlank(newsId)) {
                log.warn("新闻ID为空，跳过该条新闻");
                return null;
            }

            // '文章标题'对应'标题'
            String title = newsItem.getStr("title");
            if (StrUtil.isBlank(title)) {
                log.warn("新闻标题为空，跳过该条新闻");
                return null;
            }

            // '封面图片'对应'封面URL'
            String coverImage = newsItem.getStr("image");

            // '链接'对应'来源URL'
            String sourceUrl = newsItem.getStr("url");
            if (StrUtil.isBlank(sourceUrl)) {
                log.warn("新闻链接为空，跳过该条新闻");
                return null;
            }

            // '发布时间'对应'发布时间'
            String publishTimeStr = newsItem.getStr("focus_date");
            Date publishTime = parsePublishTime(publishTimeStr);

            // 获取新闻详情内容
            String content = fetchNewsContent(sourceUrl);

            // 内容第一段对应'文章描述'
            String description = extractFirstParagraph(content);

            // 设置Article字段
            article.setTitle(title);
            article.setDescription(description);
            article.setContent(content);
            article.setCoverImage(coverImage);
            article.setSource(CCTV_SOURCE);
            article.setSourceUrl(sourceUrl);
            article.setIconUrl(CCTV_ICON_URL);
            article.setPublishTime(publishTime);
            article.setCollectTime(new Date()); // '抓取时间'对应'收集时间'

            // 初始化计数器
            article.setViewCount(0);
            article.setCommentCount(0);
            article.setLikeCount(0);

            return article;

        } catch (Exception e) {
            log.error("转换新闻数据时出错: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解析发布时间
     */
    private Date parsePublishTime(String publishTimeStr) {
        if (StrUtil.isBlank(publishTimeStr)) {
            return new Date();
        }

        try {
            // 尝试多种时间格式
            SimpleDateFormat[] formats = {
                new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"),
                new SimpleDateFormat("yyyy-MM-dd HH:mm"),
                new SimpleDateFormat("yyyy-MM-dd"),
                new SimpleDateFormat("yyyy/MM/dd HH:mm:ss"),
                new SimpleDateFormat("yyyy/MM/dd HH:mm"),
                new SimpleDateFormat("yyyy/MM/dd")
            };

            for (SimpleDateFormat format : formats) {
                try {
                    return format.parse(publishTimeStr);
                } catch (ParseException ignored) {
                    // 继续尝试下一种格式
                }
            }

            log.warn("无法解析发布时间: {}", publishTimeStr);
            return new Date();

        } catch (Exception e) {
            log.error("解析发布时间出错: {}", e.getMessage());
            return new Date();
        }
    }

    /**
     * 提取内容的第一段作为描述
     */
    private String extractFirstParagraph(String content) {
        if (StrUtil.isBlank(content)) {
            return "";
        }

        String[] lines = content.split("\n");
        for (String line : lines) {
            String trimmed = line.trim();
            if (StrUtil.isNotBlank(trimmed) && trimmed.length() > 10) {
                // 限制描述长度
                return trimmed.length() > 200 ? trimmed.substring(0, 200) + "..." : trimmed;
            }
        }

        // 如果没有找到合适的段落，返回前200个字符
        String trimmed = content.trim();
        return trimmed.length() > 200 ? trimmed.substring(0, 200) + "..." : trimmed;
    }

    /**
     * 为文章添加"新闻"标签
     *
     * @param articleId 文章ID
     */
    private void addArticleNewsTag(String articleId) {
        try {
            // 为文章添加固定的"新闻"标签
            contentTagService.addContentTagByName(articleId, "article", "新闻", null);
            log.info("为文章ID={}添加新闻标签成功", articleId);
        } catch (Exception e) {
            log.error("为文章ID={}添加新闻标签失败: {}", articleId, e.getMessage(), e);
        }
    }
}
