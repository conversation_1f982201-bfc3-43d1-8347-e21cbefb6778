package com.zhisuo.app.service.impl;

import cn.hutool.core.util.IdUtil;
import cn.hutool.crypto.digest.DigestUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhisuo.app.common.ErrorCode;
import com.zhisuo.app.common.context.UserContext;
import com.zhisuo.app.common.exception.BusinessException;
import com.zhisuo.app.dto.request.PasswordLoginRequest;
import com.zhisuo.app.dto.request.SetPasswordRequest;
import com.zhisuo.app.dto.request.SmsCodeRequest;
import com.zhisuo.app.dto.request.SmsLoginRequest;
import com.zhisuo.app.dto.request.TokenRefreshRequest;
import com.zhisuo.app.dto.request.UpdateUserInfoRequest;
import com.zhisuo.app.dto.request.UpdatePasswordRequest;
import com.zhisuo.app.dto.response.LoginResponse;
import com.zhisuo.app.dto.response.SmsCodeResponse;
import com.zhisuo.app.dto.response.TokenRefreshResponse;
import com.zhisuo.app.entity.User;
import com.zhisuo.app.entity.UserToken;
import com.zhisuo.app.entity.UserFavorite;
import com.zhisuo.app.entity.Tag;
import com.zhisuo.app.mapper.UserMapper;
import com.zhisuo.app.mapper.UserTokenMapper;
import com.zhisuo.app.mapper.UserFavoriteMapper;
import com.zhisuo.app.mapper.UserAnalysisMapper;

import com.zhisuo.app.service.UserService;
import com.zhisuo.app.service.TagService;
import com.zhisuo.app.util.JwtUtil;
import com.zhisuo.app.util.RedisUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.Date;
import java.util.Map;
import java.util.HashMap;
import java.util.List;
import java.util.ArrayList;


/**
 * 用户Service实现类
 */
@Slf4j
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {
    
    @Autowired
    private UserTokenMapper userTokenMapper;

    @Autowired
    private UserFavoriteMapper userFavoriteMapper;

    @Autowired
    private UserAnalysisMapper userAnalysisMapper;

    @Autowired
    private TagService tagService;

    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private JwtUtil jwtUtil;
    
    @Value("${app.sms.expire-time}")
    private Integer smsExpireTime;
    
    @Value("${app.sms.default-code}")
    private String defaultSmsCode;
    
    @Value("${jwt.expiration}")
    private Long jwtExpiration;
    
    /**
     * 短信验证码Redis前缀
     */
    private static final String SMS_CODE_PREFIX = "sms:code:";
    
    /**
     * 用户令牌Redis前缀
     */
    private static final String USER_TOKEN_PREFIX = "user:token:";

    /**
     * 默认密码
     */
    private static final String DEFAULT_PASSWORD = "123456";
    
    @Override
    public SmsCodeResponse sendSmsCode(SmsCodeRequest request) {
        String phone = request.getPhone();
        String type = request.getType();
        
        // 检查发送频率
        String key = SMS_CODE_PREFIX + type + ":" + phone;
        if (Boolean.TRUE.equals(redisUtil.hasKey(key))) {
            throw new BusinessException(ErrorCode.OPERATION_TOO_FREQUENT);
        }
        
        // 生成验证码，固定为6666
        String code = "6666";
        log.info("向手机号[{}]发送验证码[{}]，类型[{}]", phone, code, type);
        
        // 将验证码保存到Redis，设置过期时间
        redisUtil.set(key, code, smsExpireTime);
        
        return SmsCodeResponse.of(smsExpireTime);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginResponse loginBySms(SmsLoginRequest request) {
        String phone = request.getPhone();
        String code = request.getCode();
        String deviceId = request.getDeviceId();
        String deviceType = request.getDeviceType();
        
        // 校验验证码
        String key = SMS_CODE_PREFIX + "login:" + phone;
        Object cachedCode = redisUtil.get(key);
        
        if (cachedCode == null) {
            throw new BusinessException(ErrorCode.VERIFICATION_CODE_EXPIRED);
        }
        
        if (!code.equals(cachedCode.toString())) {
            throw new BusinessException(ErrorCode.INVALID_VERIFICATION_CODE);
        }
        
        // 验证码使用后立即删除
        redisUtil.delete(key);
        
        // 验证码校验通过后，检查用户是否已注册
        User user = getUserByPhone(phone);
        boolean isNewUser = false;
        
        if (user == null) {
            // 用户不存在，进行注册
            user = createUser(phone);
            isNewUser = true;
            log.info("用户不存在，已为手机号[{}]创建新账户", phone);
        } else {
            log.info("用户已存在，手机号[{}]直接登录", phone);
        }
        
        // 更新登录时间
        user.setLastLoginTime(new Date());
        updateById(user);
        
        // 生成token并保存
        return generateLoginResponse(user, deviceId, deviceType, isNewUser, user.getPasswordHash() == null);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginResponse loginByPassword(PasswordLoginRequest request) {
        String phone = request.getPhone();
        String password = request.getPassword();
        String deviceId = request.getDeviceId();
        String deviceType = request.getDeviceType();
        
        // 查询用户是否存在
        User user = getUserByPhone(phone);
        if (user == null) {
            throw new BusinessException(ErrorCode.USER_NOT_FOUND);
        }
        
        // 校验密码
        if (user.getPasswordHash() == null) {
            throw new BusinessException(ErrorCode.PASSWORD_ERROR);
        }
        
        String encryptedPassword = DigestUtil.md5Hex(password + user.getPasswordSalt());
        if (!encryptedPassword.equals(user.getPasswordHash())) {
            throw new BusinessException(ErrorCode.PASSWORD_ERROR);
        }
        
        // 更新登录时间
        user.setLastLoginTime(new Date());
        updateById(user);
        
        // 生成token并保存
        return generateLoginResponse(user, deviceId, deviceType, false, false);
    }
    
    @Override
    public TokenRefreshResponse refreshToken(TokenRefreshRequest request) {
        String refreshToken = request.getRefreshToken();
        
        // 验证刷新令牌
        if (!jwtUtil.validateToken(refreshToken) || !jwtUtil.isRefreshToken(refreshToken)) {
            throw new BusinessException(ErrorCode.INVALID_TOKEN);
        }
        
        // 获取用户ID
        String userId = jwtUtil.getUserIdFromToken(refreshToken);
        User user = getUserById(userId);
        if (user == null) {
            throw new BusinessException(ErrorCode.USER_NOT_FOUND);
        }
        
        // 生成新的token
        String newToken = jwtUtil.generateToken(userId);
        String newRefreshToken = jwtUtil.generateRefreshToken(userId);
        
        TokenRefreshResponse response = new TokenRefreshResponse();
        response.setToken(newToken);
        response.setRefreshToken(newRefreshToken);
        response.setExpiresIn(jwtExpiration);
        
        return response;
    }
    
    @Override
    public void logout(String userId, String token) {
        // 从Redis中删除token
        String key = USER_TOKEN_PREFIX + userId + ":" + token;
        redisUtil.delete(key);
        
        // 从数据库中删除token记录
        LambdaQueryWrapper<UserToken> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(UserToken::getUserId, userId)
                .eq(UserToken::getToken, token);
        userTokenMapper.delete(wrapper);
    }
    
    @Override
    public User getUserById(String userId) {
        return getById(userId);
    }
    
    @Override
    public User getUserByPhone(String phone) {
        LambdaQueryWrapper<User> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(User::getPhone, phone);
        return getOne(wrapper);
    }
    
    /**
     * 创建新用户
     *
     * @param phone 手机号
     * @return 用户对象
     */
    private User createUser(String phone) {
        User user = new User();
        user.setUserId(IdUtil.fastSimpleUUID());
        user.setPhone(phone);
        user.setNickname("用户" + phone.substring(phone.length() - 4));
        user.setAvatar("https://fcg02.oss-cn-guangzhou.aliyuncs.com/image/avatar/default.png");
        user.setMemberLevel(0);
        user.setCreateTime(new Date());
        user.setUpdateTime(new Date());
        user.setStatus(1);
        
        save(user);
        return user;
    }
    
    /**
     * 生成登录响应
     *
     * @param user           用户对象
     * @param deviceId       设备ID
     * @param deviceType     设备类型
     * @param isNewUser      是否新用户
     * @param needSetPassword 是否需要设置密码
     * @return 登录响应
     */
    private LoginResponse generateLoginResponse(User user, String deviceId, String deviceType, boolean isNewUser, boolean needSetPassword) {
        String userId = user.getUserId();
        
        // 生成JWT令牌
        String token = jwtUtil.generateToken(userId);
        String refreshToken = jwtUtil.generateRefreshToken(userId);
        
        // 保存令牌信息到数据库
        UserToken userToken = new UserToken();
        userToken.setTokenId(IdUtil.fastSimpleUUID());
        userToken.setUserId(userId);
        userToken.setToken(token);
        userToken.setDeviceType(deviceType);
        userToken.setExpireTime(new Date(System.currentTimeMillis() + jwtExpiration * 1000));
        userToken.setCreateTime(new Date());
        userToken.setLastActiveTime(new Date());
        userTokenMapper.insert(userToken);
        
        // 构建响应对象
        LoginResponse response = new LoginResponse();
        response.setToken(token);
        response.setRefreshToken(refreshToken);
        response.setExpiresIn(jwtExpiration);
        response.setIsNewUser(isNewUser);
        response.setNeedSetPassword(needSetPassword);
        
        // 使用UserInfoResponse.fromUser方法转换用户信息
        response.setUserInfo(com.zhisuo.app.dto.response.UserInfoResponse.fromUser(user));
        
        return response;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginResponse setPassword(SetPasswordRequest request) {
        String userId = request.getUserId();
        String password = request.getPassword();
        String deviceId = request.getDeviceId();
        String deviceType = request.getDeviceType();
        
        // 查询用户是否存在
        User user = getUserById(userId);
        if (user == null) {
            throw new BusinessException(ErrorCode.USER_NOT_FOUND);
        }
        
        // 设置密码，生成盐值和密码哈希
        String salt = IdUtil.fastSimpleUUID();
        String passwordHash = DigestUtil.md5Hex(password + salt);
        
        // 更新用户信息
        user.setPasswordSalt(salt);
        user.setPasswordHash(passwordHash);
        user.setUpdateTime(new Date());
        updateById(user);
        
        log.info("用户[{}]设置密码成功", userId);
        
        // 生成token并保存
        return generateLoginResponse(user, deviceId, deviceType, false, false);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public LoginResponse skipSetPassword(String userId, String deviceId, String deviceType) {
        // 查询用户是否存在
        User user = getUserById(userId);
        if (user == null) {
            throw new BusinessException(ErrorCode.USER_NOT_FOUND);
        }
        
        // 如果用户已经设置了密码，则不进行处理
        if (user.getPasswordHash() != null) {
            log.info("用户[{}]已设置密码，跳过默认密码设置", userId);
        } else {
            // 设置默认密码
            String salt = IdUtil.fastSimpleUUID();
            String passwordHash = DigestUtil.md5Hex(DEFAULT_PASSWORD + salt);
            
            // 更新用户信息
            user.setPasswordSalt(salt);
            user.setPasswordHash(passwordHash);
            user.setUpdateTime(new Date());
            updateById(user);
            
            log.info("用户[{}]跳过密码设置，已设置默认密码", userId);
        }
        
        // 生成token并保存
        return generateLoginResponse(user, deviceId, deviceType, false, false);
    }

    @Override
    public User updateUserInfo(String userId, UpdateUserInfoRequest request) {
        // 获取用户信息
        User user = getUserById(userId);
        if (user == null) {
            throw new BusinessException(ErrorCode.USER_NOT_FOUND);
        }
        
        // 更新昵称
        if (StringUtils.hasText(request.getNickname())) {
            user.setNickname(request.getNickname());
        }
        
        // 更新头像
        if (StringUtils.hasText(request.getAvatar())) {
            user.setAvatar(request.getAvatar());
        }
        
        // 更新时间
        user.setUpdateTime(new Date());
        
        // 保存更新
        updateById(user);
        
        return user;
    }

    @Override
    public com.zhisuo.app.dto.response.UserInfoResponse getCurrentUserInfo() {
        String userId = UserContext.getUserId();
        if (!StringUtils.hasText(userId)) {
            throw new BusinessException(ErrorCode.UNAUTHORIZED);
        }
        
        // 从数据库获取最新的用户信息，并转换为DTO
        User user = getUserById(userId);
        return com.zhisuo.app.dto.response.UserInfoResponse.fromUser(user);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePassword(String userId, UpdatePasswordRequest request) {
        // 获取用户信息
        User user = getUserById(userId);
        if (user == null) {
            throw new BusinessException(ErrorCode.USER_NOT_FOUND);
        }
        
        // 验证原密码
        if (user.getPasswordHash() == null) {
            throw new BusinessException(ErrorCode.PASSWORD_ERROR.getCode(), "您尚未设置密码，请先设置密码");
        }
        
        String oldPasswordHash = DigestUtil.md5Hex(request.getOldPassword() + user.getPasswordSalt());
        if (!oldPasswordHash.equals(user.getPasswordHash())) {
            throw new BusinessException(ErrorCode.PASSWORD_ERROR.getCode(), "原密码错误");
        }
        
        // 设置新密码
        String salt = IdUtil.fastSimpleUUID();
        String passwordHash = DigestUtil.md5Hex(request.getNewPassword() + salt);
        
        // 更新用户信息
        user.setPasswordSalt(salt);
        user.setPasswordHash(passwordHash);
        user.setUpdateTime(new Date());
        
        // 保存更新
        boolean result = updateById(user);
        
        // 如果更新成功，可以考虑使当前用户的所有token失效，强制重新登录
        // 这里简化处理，不做token失效
        
        return result;
    }

    @Override
    public Map<String, Object> getUserStats(String userId) {
        Map<String, Object> stats = new HashMap<>();

        try {
            // 获取收藏数量
            LambdaQueryWrapper<UserFavorite> favoriteWrapper = new LambdaQueryWrapper<>();
            favoriteWrapper.eq(UserFavorite::getUserId, userId);
            long favoriteCount = userFavoriteMapper.selectCount(favoriteWrapper);

            // 获取分析数量
            long analysisCount = userAnalysisMapper.countByUserId(userId);

            // 获取关注数量（这里暂时使用模拟数据，因为还没有关注功能）
            long followCount = 0; // 暂时设为0，后续可以实现

            stats.put("favoriteCount", favoriteCount);
            stats.put("analysisCount", analysisCount);
            stats.put("followCount", followCount);

            log.info("获取用户{}统计数据成功: 收藏{}, 分析{}, 关注{}", userId, favoriteCount, analysisCount, followCount);

        } catch (Exception e) {
            log.error("获取用户{}统计数据失败: {}", userId, e.getMessage(), e);
            // 返回默认值
            stats.put("favoriteCount", 0);
            stats.put("analysisCount", 0);
            stats.put("followCount", 0);
        }

        return stats;
    }

    @Override
    public Map<String, Object> getUserInterests(String userId) {
        Map<String, Object> interests = new HashMap<>();

        try {
            // 基于用户收藏和点赞的内容标签分析兴趣分布
            List<Map<String, Object>> interestData = calculateUserInterests(userId);

            interests.put("data", interestData);
            interests.put("updateTime", new Date());

            log.info("获取用户{}兴趣分布成功，共{}个兴趣类别", userId, interestData.size());

        } catch (Exception e) {
            log.error("获取用户{}兴趣分布失败: {}", userId, e.getMessage(), e);
            interests.put("data", new ArrayList<>());
            interests.put("updateTime", new Date());
        }

        return interests;
    }

    /**
     * 计算用户兴趣分布
     * 显示所有标签，基于用户收藏和点赞的内容标签进行统计分析
     */
    private List<Map<String, Object>> calculateUserInterests(String userId) {
        List<Map<String, Object>> interestData = new ArrayList<>();

        try {
            // 获取所有标签
            List<Tag> allTags = tagService.getTagsByCategory(null);

            if (allTags.isEmpty()) {
                log.info("系统中暂无标签，返回空兴趣分布");
                return interestData;
            }

            // 获取用户收藏内容的标签统计
            Map<String, Integer> userTagCounts = getUserContentTagCounts(userId);

            // 计算用户标签总数
            int totalUserCount = userTagCounts.values().stream().mapToInt(Integer::intValue).sum();

            // 定义颜色数组
            String[] colors = {"#4a89dc", "#36CFC9", "#73D13D", "#FFC53D", "#FF7875", "#B37FEB", "#40A9FF", "#52C41A"};

            // 为所有标签计算百分比
            for (int i = 0; i < allTags.size(); i++) {
                Tag tag = allTags.get(i);
                String tagName = tag.getTagName();
                int userCount = userTagCounts.getOrDefault(tagName, 0);

                // 计算百分比：如果用户有该标签的收藏，则按实际比例；否则为0
                int percentage = 0;
                if (totalUserCount > 0 && userCount > 0) {
                    percentage = Math.round((float) userCount * 100 / totalUserCount);
                }

                Map<String, Object> interest = new HashMap<>();
                interest.put("name", tagName);
                interest.put("percentage", percentage);
                interest.put("color", colors[i % colors.length]);
                interest.put("count", userCount);
                interestData.add(interest);
            }

            // 按百分比降序排序，百分比相同的按标签名排序
            interestData.sort((a, b) -> {
                int percentageA = (Integer) a.get("percentage");
                int percentageB = (Integer) b.get("percentage");
                if (percentageA != percentageB) {
                    return Integer.compare(percentageB, percentageA); // 降序
                }
                return ((String) a.get("name")).compareTo((String) b.get("name")); // 升序
            });

            log.info("用户{}兴趣分布计算完成，显示{}个标签，用户有{}个标签有数据",
                    userId, allTags.size(), userTagCounts.size());

        } catch (Exception e) {
            log.error("计算用户{}兴趣分布失败: {}", userId, e.getMessage(), e);
        }

        return interestData;
    }

    /**
     * 获取用户内容标签统计
     * 使用联合查询直接统计用户收藏内容的标签分布
     */
    private Map<String, Integer> getUserContentTagCounts(String userId) {
        Map<String, Integer> tagCounts = new HashMap<>();

        try {
            // 使用联合查询直接获取用户收藏内容的标签统计
            List<Map<String, Object>> results = userFavoriteMapper.selectUserTagCounts(userId);

            // 转换结果为Map
            for (Map<String, Object> row : results) {
                String tagName = (String) row.get("tag_name");
                Object countObj = row.get("count");
                Integer count = countObj instanceof Long ? ((Long) countObj).intValue() : (Integer) countObj;
                tagCounts.put(tagName, count);
            }

            log.info("用户{}标签统计完成，共{}个标签有收藏数据", userId, tagCounts.size());

        } catch (Exception e) {
            log.error("获取用户{}内容标签统计失败: {}", userId, e.getMessage(), e);
        }

        return tagCounts;
    }



    @Override
    public Map<String, Object> getUserAnalysis(String userId, Integer page, Integer size) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 使用分页查询用户分析数据
            Page<Map<String, Object>> analysisPage = new Page<>(page + 1, size);
            analysisPage = userAnalysisMapper.selectUserAnalysis(analysisPage, userId);

            result.put("content", analysisPage.getRecords());
            result.put("totalElements", analysisPage.getTotal());
            result.put("totalPages", analysisPage.getPages());
            result.put("size", analysisPage.getSize());
            result.put("number", analysisPage.getCurrent() - 1);

            log.info("获取用户{}分析列表成功，页码: {}, 大小: {}, 总数: {}", userId, page, size, analysisPage.getTotal());

        } catch (Exception e) {
            log.error("获取用户{}分析列表失败: {}", userId, e.getMessage(), e);
            result.put("content", new ArrayList<>());
            result.put("totalElements", 0);
            result.put("totalPages", 0);
            result.put("size", size);
            result.put("number", page);
        }

        return result;
    }
}