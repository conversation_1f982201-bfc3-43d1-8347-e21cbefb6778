package com.zhisuo.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 协议实体类
 */
@Data
@TableName("agreements")
public class Agreement {
    
    @TableId
    private String agreementId;  // 协议ID
    
    private String title;        // 协议标题
    
    private String content;      // 协议内容
    
    private String type;         // 协议类型(user/privacy)
    
    private String version;      // 版本号
    
    private Date publishTime;    // 发布时间
    
    private Integer status;      // 状态(1:生效,0:失效)
} 