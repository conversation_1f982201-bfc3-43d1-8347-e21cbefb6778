package com.zhisuo.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhisuo.app.entity.UserLike;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

/**
 * 用户点赞Mapper接口
 */
@Mapper
public interface UserLikeMapper extends BaseMapper<UserLike> {
    
    /**
     * 检查用户是否已点赞
     *
     * @param userId 用户ID
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 点赞记录
     */
    @Select("SELECT * FROM user_likes WHERE user_id = #{userId} AND content_id = #{contentId} AND content_type = #{contentType}")
    UserLike selectByUserAndContent(@Param("userId") String userId, 
                                   @Param("contentId") String contentId, 
                                   @Param("contentType") String contentType);
    
    /**
     * 统计内容的点赞数量
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 点赞数量
     */
    @Select("SELECT COUNT(*) FROM user_likes WHERE content_id = #{contentId} AND content_type = #{contentType}")
    long countByContent(@Param("contentId") String contentId, @Param("contentType") String contentType);
    
    /**
     * 分页查询用户点赞的内容
     *
     * @param page 分页对象
     * @param userId 用户ID
     * @param contentType 内容类型（可选）
     * @return 点赞内容列表
     */
    @Select("<script>" +
            "SELECT ul.*, " +
            "CASE " +
            "  WHEN ul.content_type = 'article' THEN a.title " +
            "  WHEN ul.content_type = 'topic' THEN ht.title " +
            "  WHEN ul.content_type = 'comment' THEN c.content " +
            "END as contentTitle, " +
            "CASE " +
            "  WHEN ul.content_type = 'article' THEN a.cover_image " +
            "  WHEN ul.content_type = 'topic' THEN NULL " +
            "  ELSE NULL " +
            "END as contentImage " +
            "FROM user_likes ul " +
            "LEFT JOIN articles a ON ul.content_id = a.article_id AND ul.content_type = 'article' " +
            "LEFT JOIN hot_topics ht ON ul.content_id = ht.topic_id AND ul.content_type = 'topic' " +
            "LEFT JOIN comments c ON ul.content_id = c.comment_id AND ul.content_type = 'comment' " +
            "WHERE ul.user_id = #{userId} " +
            "<if test='contentType != null'>" +
            "  AND ul.content_type = #{contentType} " +
            "</if>" +
            "ORDER BY ul.create_time DESC" +
            "</script>")
    Page<Map<String, Object>> selectUserLikes(Page<Map<String, Object>> page, 
                                             @Param("userId") String userId, 
                                             @Param("contentType") String contentType);
}
