package com.zhisuo.app.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

/**
 * 央视新闻服务测试类
 */
@Slf4j
@SpringBootTest
@TestPropertySource(properties = {
    "cctv.news.url=https://news.cctv.com/2019/07/gaiban/cmsdatainterface/page/news_1.jsonp",
    "cctv.news.max-news=5",
    "cctv.news.timeout=10000",
    "cctv.news.delay=1000"
})
public class CCTVNewsServiceTest {

    @Autowired
    private ArticleService articleService;

    @Test
    public void testFetchAndUpdateCCTVNews() {
        try {
            log.info("开始测试央视新闻获取功能...");

            // 测试定时获取功能
            articleService.fetchAndUpdateCCTVNews();

            log.info("央视新闻获取测试完成");

        } catch (Exception e) {
            log.error("测试失败: {}", e.getMessage(), e);
        }
    }
}
