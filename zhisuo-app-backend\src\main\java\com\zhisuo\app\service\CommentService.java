package com.zhisuo.app.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zhisuo.app.dto.request.CommentCreateRequest;
import com.zhisuo.app.dto.response.CommentResponse;
import com.zhisuo.app.entity.Comment;

/**
 * 评论服务接口
 */
public interface CommentService extends IService<Comment> {
    
    /**
     * 分页获取评论列表
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param userId 当前用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 评论分页数据
     */
    Page<CommentResponse> getCommentPage(String contentId, String contentType, String userId, Integer page, Integer size);
    
    /**
     * 创建评论
     *
     * @param request 评论创建请求
     * @param userId 用户ID
     * @return 评论ID
     */
    String createComment(CommentCreateRequest request, String userId);
    
    /**
     * 删除评论
     *
     * @param commentId 评论ID
     * @param userId 用户ID
     */
    void deleteComment(String commentId, String userId);
    
    /**
     * 获取评论详情
     *
     * @param commentId 评论ID
     * @param userId 当前用户ID
     * @return 评论详情
     */
    CommentResponse getCommentDetail(String commentId, String userId);
    
    /**
     * 获取用户的评论列表
     *
     * @param userId 用户ID
     * @param page 页码
     * @param size 每页大小
     * @return 评论分页数据
     */
    Page<CommentResponse> getUserComments(String userId, Integer page, Integer size);
    
    /**
     * 更新评论点赞数
     *
     * @param commentId 评论ID
     * @param increment 增量（1或-1）
     */
    void updateLikeCount(String commentId, int increment);
    
    /**
     * 统计内容的评论数量
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 评论数量
     */
    long getCommentCount(String contentId, String contentType);
}
