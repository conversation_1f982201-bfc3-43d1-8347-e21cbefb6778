package com.zhisuo.app.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhisuo.app.common.Result;
import com.zhisuo.app.common.context.UserContext;
import com.zhisuo.app.dto.request.CommentCreateRequest;
import com.zhisuo.app.dto.response.CommentResponse;
import com.zhisuo.app.service.CommentService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 评论控制器
 */
@RestController
@RequestMapping("/v1/comments")
public class CommentController {
    
    @Autowired
    private CommentService commentService;
    
    /**
     * 获取评论列表
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param page 页码
     * @param size 每页大小
     * @return 评论列表
     */
    @GetMapping
    public Result<Map<String, Object>> getComments(
            @RequestParam String contentId,
            @RequestParam String contentType,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        
        String userId = UserContext.getUserId();
        
        Page<CommentResponse> commentPage = commentService.getCommentPage(
            contentId, contentType, userId, page, size);
        
        Map<String, Object> result = new HashMap<>();
        result.put("content", commentPage.getRecords());
        result.put("totalElements", commentPage.getTotal());
        result.put("totalPages", commentPage.getPages());
        result.put("size", commentPage.getSize());
        result.put("number", commentPage.getCurrent() - 1); // 前端使用0基索引
        
        return Result.success(result);
    }
    
    /**
     * 发表评论
     *
     * @param request 评论请求
     * @return 评论ID
     */
    @PostMapping
    public Result<Map<String, String>> createComment(@RequestBody @Valid CommentCreateRequest request) {
        String userId = UserContext.getUserId();

        String commentId = commentService.createComment(request, userId);
        
        Map<String, String> result = new HashMap<>();
        result.put("commentId", commentId);
        
        return Result.success(result);
    }
    
    /**
     * 删除评论
     *
     * @param commentId 评论ID
     * @return 结果
     */
    @DeleteMapping("/{commentId}")
    public Result<Void> deleteComment(@PathVariable String commentId) {
        String userId = UserContext.getUserId();

        commentService.deleteComment(commentId, userId);
        return Result.success();
    }
    
    /**
     * 获取评论详情
     *
     * @param commentId 评论ID
     * @return 评论详情
     */
    @GetMapping("/{commentId}")
    public Result<CommentResponse> getCommentDetail(@PathVariable String commentId) {
        String userId = UserContext.getUserId();

        CommentResponse comment = commentService.getCommentDetail(commentId, userId);
        if (comment == null) {
            return Result.error(404, "评论不存在");
        }
        
        return Result.success(comment);
    }
    
    /**
     * 获取用户的评论列表
     *
     * @param page 页码
     * @param size 每页大小
     * @return 评论列表
     */
    @GetMapping("/my")
    public Result<Map<String, Object>> getMyComments(
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        
        String userId = UserContext.getUserId();

        Page<CommentResponse> commentPage = commentService.getUserComments(userId, page, size);
        
        Map<String, Object> result = new HashMap<>();
        result.put("content", commentPage.getRecords());
        result.put("totalElements", commentPage.getTotal());
        result.put("totalPages", commentPage.getPages());
        result.put("size", commentPage.getSize());
        result.put("number", commentPage.getCurrent() - 1);
        
        return Result.success(result);
    }
}
