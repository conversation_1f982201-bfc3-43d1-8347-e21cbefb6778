<template>
  <view class="cropper-container" v-if="visible">
    <!-- Debug信息 -->
    <view class="debug-info" v-if="showDebug">
      <text>图片路径: {{src}}</text>
      <text>图片尺寸: {{imageWidth}} x {{imageHeight}}</text>
      <text>缩放比例: {{scale}}</text>
      <text>位置: {{JSON.stringify(imagePosition)}}</text>
    </view>
    
    <view class="cropper-mask"></view>
    <view class="cropper-content">
      <view class="cropper-header">
        <view class="header-btn" @click="cancel">取消</view>
        <view class="header-title">裁剪头像</view>
        <view class="header-btn confirm" @click="confirm">确定</view>
      </view>
      
      <view class="cropper-body">
        <view class="cropper-area">
          <!-- 图片层 -->
          <image 
            class="cropper-image" 
            :src="src" 
            :style="{
              width: imageWidth + 'px', 
              height: imageHeight + 'px',
              transform: `translate(${imagePosition.x}px, ${imagePosition.y}px) scale(${scale})`
            }"
            @load="onImageLoad"
            @touchstart="handleTouchStart"
            @touchmove.prevent="handleTouchMove"
            @touchend="handleTouchEnd"
            mode="aspectFill"
          ></image>
          
          <!-- 裁剪框 -->
          <view class="cropper-box" :style="{width: cropSize + 'px', height: cropSize + 'px'}">
            <view class="cropper-outline">
              <view class="cropper-outline-border"></view>
            </view>
          </view>
        </view>
      </view>
      
      <view class="cropper-footer">
        <view class="cropper-slider">
          <view class="slider-icon" @click="zoomOut">
            <uni-icons type="minus" size="24" color="#fff"></uni-icons>
          </view>
          <slider class="slider" 
            :min="50" 
            :max="300" 
            :value="scale * 100" 
            @change="sliderChange"
            activeColor="#3264ED"
            backgroundColor="#EFEFEF"
            block-color="#3264ED"
            block-size="24"
          ></slider>
          <view class="slider-icon" @click="zoomIn">
            <uni-icons type="plus" size="24" color="#fff"></uni-icons>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 用于裁剪的隐藏Canvas -->
    <canvas canvas-id="cropper-canvas" :style="{width: width + 'px', height: height + 'px', position: 'fixed', left: '-9999px', top: '-9999px'}"></canvas>
  </view>
</template>

<script>
export default {
  name: 'ImageCropper',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    src: {
      type: String,
      default: ''
    },
    width: {
      type: Number,
      default: 300
    },
    height: {
      type: Number,
      default: 300
    },
    maxScale: {
      type: Number,
      default: 3
    },
    minScale: {
      type: Number,
      default: 0.5
    }
  },
  data() {
    return {
      imageWidth: 300,
      imageHeight: 300,
      scale: 1,
      imagePosition: {
        x: 0,
        y: 0
      },
      cropperWidth: 300,
      cropperHeight: 300,
      cropSize: 300, // 裁剪框尺寸
      originImage: null,
      canvasContext: null,
      showDebug: false, // 开启调试信息
      startX: 0,
      startY: 0,
      lastX: 0,
      lastY: 0,
      isTouching: false
    }
  },
  watch: {
    src(newVal) {
      //console.log('src变化:', newVal);
      if (newVal) {
        this.loadImage(newVal);
      }
    },
    visible(newVal) {
      //console.log('visible变化:', newVal);
      if (newVal && this.src) {
        this.loadImage(this.src);
      }
    }
  },
  mounted() {
    // 计算裁剪框尺寸
    const sysInfo = uni.getSystemInfoSync();
    const windowWidth = sysInfo.windowWidth;
    // 裁剪框尺寸（屏幕宽度的60%，但不超过300px）
    this.cropSize = Math.min(windowWidth * 0.6, 300);
    
    // 组件挂载后，如果已有src，则加载图片
    //console.log('组件挂载, visible:', this.visible, 'src:', this.src);
    if (this.visible && this.src) {
      this.loadImage(this.src);
    }
  },
  methods: {
    // 处理触摸开始
    handleTouchStart(e) {
      //console.log('触摸开始');
      const touch = e.changedTouches && e.changedTouches[0];
      if (touch) {
        this.isTouching = true;
        this.startX = touch.clientX || touch.x || 0;
        this.startY = touch.clientY || touch.y || 0;
        this.lastX = this.imagePosition.x;
        this.lastY = this.imagePosition.y;
      }
    },
    
    // 处理触摸移动
    handleTouchMove(e) {
      if (!this.isTouching) return;
      
      const touch = e.changedTouches && e.changedTouches[0];
      if (touch) {
        // 计算移动距离
        const currentX = touch.clientX || touch.x || 0;
        const currentY = touch.clientY || touch.y || 0;
        const moveX = currentX - this.startX;
        const moveY = currentY - this.startY;
        
        // 更新图片位置
        this.imagePosition.x = this.lastX + moveX;
        this.imagePosition.y = this.lastY + moveY;
      }
    },
    
    // 处理触摸结束
    handleTouchEnd(e) {
      //console.log('触摸结束');
      this.isTouching = false;
    },
    
    // 放大
    zoomIn() {
      if (this.scale < this.maxScale) {
        this.scale += 0.1;
        if (this.scale > this.maxScale) this.scale = this.maxScale;
      }
    },
    
    // 缩小
    zoomOut() {
      if (this.scale > this.minScale) {
        this.scale -= 0.1;
        if (this.scale < this.minScale) this.scale = this.minScale;
      }
    },
    
    // 加载图片
    loadImage(src) {
      //console.log('开始加载图片:', src);
      
      // 先设置一个基本尺寸，避免图片加载前没有显示区域
      this.imageWidth = 300;
      this.imageHeight = 300;
      
      // 尝试直接使用图片
      if (src.startsWith('http') || src.startsWith('https')) {
        // 网络图片，使用getImageInfo
        uni.getImageInfo({
          src: src,
          success: (res) => {
            //console.log('获取图片信息成功:', res);
            this.originImage = res;
            this.adjustImageSize();
          },
          fail: (err) => {
            console.error('获取图片信息失败:', err);
            uni.showToast({
              title: '图片加载失败',
              icon: 'none'
            });
          }
        });
      } else {
        // 本地图片，尝试直接使用
        uni.getImageInfo({
          src: src,
          success: (res) => {
            //console.log('获取本地图片信息成功:', res);
            this.originImage = res;
            this.adjustImageSize();
          },
          fail: (err) => {
            console.error('获取本地图片信息失败:', err);
            // 如果获取失败，设置默认尺寸
            this.originImage = {
              width: 300,
              height: 300
            };
            this.adjustImageSize();
          }
        });
      }
    },
    
    // 图片加载完成事件
    onImageLoad(e) {
      //console.log('图片加载完成事件触发');
      if (!this.originImage) {
        // 如果originImage还没有通过getImageInfo获取到，则通过事件获取图片尺寸
        const imgSize = e.detail;
        if (imgSize && imgSize.width && imgSize.height) {
          //console.log('从事件中获取图片尺寸:', imgSize);
          this.originImage = {
            width: imgSize.width,
            height: imgSize.height
          };
          this.adjustImageSize();
        } else {
          //console.log('事件中没有图片尺寸信息，使用默认尺寸');
          this.originImage = {
            width: 300,
            height: 300
          };
          this.adjustImageSize();
        }
      }
    },
    
    // 调整图片尺寸和位置
    adjustImageSize() {
      if (!this.originImage) {
        console.error('没有图片信息，无法调整尺寸');
        return;
      }
      
      //console.log('调整图片尺寸, 原始尺寸:', this.originImage);
      
      const sysInfo = uni.getSystemInfoSync();
      const windowWidth = sysInfo.windowWidth;
      const windowHeight = sysInfo.windowHeight;
      
      // 裁剪框尺寸
      this.cropperWidth = this.cropSize;
      this.cropperHeight = this.cropSize;
      
      // 计算图片初始尺寸
      const imageRatio = this.originImage.width / this.originImage.height || 1; // 防止除以0
      let imageWidth, imageHeight;
      
      if (imageRatio > 1) {
        // 宽图
        imageHeight = this.cropperHeight * 1.2; // 让图片比裁剪框大一些
        imageWidth = imageHeight * imageRatio;
      } else {
        // 长图
        imageWidth = this.cropperWidth * 1.2; // 让图片比裁剪框大一些
        imageHeight = imageWidth / imageRatio;
      }
      
      // 确保图片至少覆盖裁剪区域
      if (imageWidth < this.cropperWidth) {
        imageWidth = this.cropperWidth;
        imageHeight = imageWidth / imageRatio;
      }
      
      if (imageHeight < this.cropperHeight) {
        imageHeight = this.cropperHeight;
        imageWidth = imageHeight * imageRatio;
      }
      
      this.imageWidth = imageWidth;
      this.imageHeight = imageHeight;
      
      // 居中显示 - 修复：与裁剪框位置计算保持一致
      const rpxToPx = windowWidth / 750; // 750rpx = 屏幕宽度
      const headerHeight = 90 * rpxToPx; // 90rpx转px
      const footerHeight = 180 * rpxToPx; // 180rpx转px
      const cropAreaHeight = windowHeight - headerHeight - footerHeight;

      this.imagePosition = {
        x: (windowWidth - imageWidth) / 2,
        y: headerHeight + (cropAreaHeight - imageHeight) / 2
      };
      
      this.scale = 1;
      
      //console.log('图片尺寸调整完成:', {
        //width: this.imageWidth,
        //height: this.imageHeight,
        //position: this.imagePosition,
        //cropSize: this.cropSize
      //});
    },
    
    // 滑块改变
    sliderChange(e) {
      this.scale = e.detail.value / 100;
    },
    
    // 取消裁剪
    cancel() {
      this.$emit('cancel');
    },
    
    // 确认裁剪
    confirm() {
      try {
        //console.log('开始裁剪图片');
        
        // 创建临时canvas并获取上下文
        const canvasId = 'cropper-canvas';
        const ctx = uni.createCanvasContext(canvasId, this);
        
        // 设置画布尺寸为正方形，与裁剪框一致
        const canvasSize = this.width; // 使用props中传入的width值
        
        // 获取系统信息
        const sysInfo = uni.getSystemInfoSync();
        const windowWidth = sysInfo.windowWidth;
        const windowHeight = sysInfo.windowHeight;

        // 计算rpx到px的转换比例
        const rpxToPx = windowWidth / 750; // 750rpx = 屏幕宽度

        // 计算实际的裁剪区域高度（减去头部和底部的高度）
        const headerHeight = 90 * rpxToPx; // 90rpx转px
        const footerHeight = 180 * rpxToPx; // 180rpx转px
        const cropAreaHeight = windowHeight - headerHeight - footerHeight;

        // 裁剪框尺寸和位置（在裁剪区域中央）
        const boxSize = this.cropSize;
        const boxLeft = (windowWidth - boxSize) / 2;
        const boxTop = headerHeight + (cropAreaHeight - boxSize) / 2;
        
        // 图片当前状态
        const imgX = this.imagePosition.x;
        const imgY = this.imagePosition.y;
        const imgScale = this.scale;
        const viewImageWidth = this.imageWidth * imgScale;
        const viewImageHeight = this.imageHeight * imgScale;
        
        // 计算裁剪框相对于图片的位置(相对于图片左上角)
        const relativeBoxLeft = boxLeft - imgX;
        const relativeBoxTop = boxTop - imgY;
        
        // 计算相对位置在原图中的对应坐标
        const originalWidth = this.originImage.width;
        const originalHeight = this.originImage.height;
        
        // 计算缩放比例 - 修复：应该是原图尺寸与显示尺寸的比例
        const scaleRatioX = originalWidth / this.imageWidth;
        const scaleRatioY = originalHeight / this.imageHeight;

        // 计算在原图中的裁剪区域 - 修复：需要考虑图片的缩放
        let sourceX = (relativeBoxLeft / imgScale) * scaleRatioX;
        let sourceY = (relativeBoxTop / imgScale) * scaleRatioY;
        let sourceWidth = (boxSize / imgScale) * scaleRatioX;
        let sourceHeight = (boxSize / imgScale) * scaleRatioY;
        
        // 确保不超出原图范围
        sourceX = Math.max(0, sourceX);
        sourceY = Math.max(0, sourceY);
        sourceWidth = Math.min(sourceWidth, originalWidth - sourceX);
        sourceHeight = Math.min(sourceHeight, originalHeight - sourceY);

        // 确保裁剪区域有效
        if (sourceWidth <= 0 || sourceHeight <= 0) {
          console.error('裁剪区域无效:', { sourceX, sourceY, sourceWidth, sourceHeight });
          uni.showToast({
            title: '裁剪区域无效',
            icon: 'none'
          });
          this.$emit('confirm', '');
          return;
        }
        
        console.log('详细裁剪参数:', {
          originalSize: { width: originalWidth, height: originalHeight },
          displaySize: { width: this.imageWidth, height: this.imageHeight },
          viewSize: { width: viewImageWidth, height: viewImageHeight },
          imagePosition: { x: imgX, y: imgY },
          boxPosition: { left: boxLeft, top: boxTop, size: boxSize },
          relativeBox: { left: relativeBoxLeft, top: relativeBoxTop },
          source: { x: sourceX, y: sourceY, width: sourceWidth, height: sourceHeight },
          scale: imgScale,
          scaleRatio: { x: scaleRatioX, y: scaleRatioY },
          canvasSize: canvasSize
        });
        
        // 清空画布
        ctx.clearRect(0, 0, canvasSize, canvasSize);
        
        // 绘制裁剪后的图片到Canvas
        ctx.drawImage(
          this.src,
          sourceX,
          sourceY,
          sourceWidth,
          sourceHeight,
          0,
          0,
          canvasSize,
          canvasSize
        );
        
        // 完成绘制并导出图片
        ctx.draw(false, () => {
          // 给Canvas足够的时间来完成绘制
          setTimeout(() => {
            uni.canvasToTempFilePath({
              canvasId: canvasId,
              x: 0,
              y: 0,
              width: canvasSize,
              height: canvasSize,
              destWidth: canvasSize,
              destHeight: canvasSize,
              fileType: 'png',
              quality: 1,
              success: (res) => {
                //console.log('裁剪成功:', res.tempFilePath);
                // 导出成功，返回裁剪后的图片路径
                this.$emit('confirm', res.tempFilePath);
              },
              fail: (err) => {
                console.error('裁剪失败:', err);
                uni.showToast({
                  title: '裁剪失败',
                  icon: 'none'
                });
                // 即使失败也要关闭裁剪窗口
                this.$emit('confirm', '');
              }
            }, this);
          }, 800);
        });
      } catch (error) {
        console.error('裁剪过程出错:', error);
        uni.showToast({
          title: '裁剪失败',
          icon: 'none'
        });
        // 出错也要关闭裁剪窗口
        this.$emit('confirm', '');
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.cropper-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  
  .debug-info {
    position: absolute;
    top: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 10px;
    font-size: 12px;
    z-index: 1000;
    
    text {
      display: block;
      margin-bottom: 5px;
    }
  }
  
  .cropper-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.8);
  }
  
  .cropper-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    
    .cropper-header {
      height: 90rpx;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 30rpx;
      
      .header-btn {
        color: #fff;
        font-size: 32rpx;
        
        &.confirm {
          color: #3264ED;
        }
      }
      
      .header-title {
        color: #fff;
        font-size: 32rpx;
        font-weight: bold;
      }
    }
    
    .cropper-body {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      overflow: hidden;
      
      .cropper-area {
        width: 100%;
        height: 100%;
        position: relative;
        
        .cropper-image {
          position: absolute;
          top: 0;
          left: 0;
          will-change: transform;
          transition: transform 0.05s;
        }
        
        .cropper-box {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          pointer-events: none;
          z-index: 1;
          
          .cropper-outline {
            width: 100%;
            height: 100%;
            position: relative;
            
            .cropper-outline-border {
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              border: 2rpx solid #fff;
              border-radius: 50%;
              box-shadow: 0 0 0 2000px rgba(0, 0, 0, 0.5);
            }
          }
        }
      }
    }
    
    .cropper-footer {
      height: 180rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      
      .cropper-slider {
        width: 80%;
        display: flex;
        align-items: center;
        
        .slider {
          flex: 1;
          margin: 0 20rpx;
        }
        
        .slider-icon {
          width: 40rpx;
          height: 40rpx;
          display: flex;
          align-items: center;
          justify-content: center;
        }
      }
    }
  }
}
</style> 