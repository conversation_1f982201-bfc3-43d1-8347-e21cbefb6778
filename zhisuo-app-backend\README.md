# 智索APP后端项目

## 项目介绍
智索APP是一个集用户管理、内容展示、热点分析和个性化推荐于一体的应用平台。本项目为其Java后端接口实现。

## 技术栈
- JDK 1.8
- Spring Boot 2.7.6
- MyBatis-Plus 3.5.3
- MySQL 8.0
- Redis
- JWT

## 项目结构
```
src/main/java/com/zhisuo/app
├── ZhisuoAppApplication.java       # 应用程序启动类
├── common                          # 通用模块
│   ├── ErrorCode.java              # 错误码枚举
│   ├── Result.java                 # 通用响应结果类
│   ├── context                     # 上下文
│   │   └── UserContext.java        # 用户上下文
│   └── exception                   # 异常处理
│       ├── BusinessException.java  # 业务异常类
│       └── GlobalExceptionHandler.java # 全局异常处理
├── config                          # 配置类
│   ├── MybatisPlusConfig.java      # MyBatis-Plus配置
│   ├── RedisConfig.java            # Redis配置
│   └── WebConfig.java              # Web配置
├── controller                      # 控制器
│   └── AuthController.java         # 认证控制器
├── dto                             # 数据传输对象
│   ├── request                     # 请求DTO
│   │   ├── PasswordLoginRequest.java # 密码登录请求
│   │   ├── SmsCodeRequest.java     # 短信验证码请求
│   │   ├── SmsLoginRequest.java    # 短信验证码登录请求
│   │   └── TokenRefreshRequest.java # 刷新令牌请求
│   └── response                    # 响应DTO
│       ├── LoginResponse.java      # 登录响应
│       ├── SmsCodeResponse.java    # 短信验证码响应
│       └── TokenRefreshResponse.java # 刷新令牌响应
├── entity                          # 实体类
│   ├── Article.java                # 文章实体
│   ├── Comment.java                # 评论实体
│   ├── HotTopic.java               # 热点话题实体
│   ├── SystemNotice.java           # 系统通知实体
│   ├── User.java                   # 用户实体
│   ├── UserFavorite.java           # 用户收藏实体
│   └── UserToken.java              # 用户令牌实体
├── interceptor                     # 拦截器
│   └── AuthInterceptor.java        # 认证拦截器
├── mapper                          # 数据库操作接口
│   ├── UserMapper.java             # 用户Mapper
│   └── UserTokenMapper.java        # 用户令牌Mapper
├── service                         # 业务逻辑接口
│   ├── UserService.java            # 用户Service接口
│   └── impl                        # Service实现类
│       └── UserServiceImpl.java    # 用户Service实现类
└── util                            # 工具类
    ├── JwtUtil.java                # JWT工具类
    └── RedisUtil.java              # Redis工具类
```

## 开发环境
- IDE: IntelliJ IDEA
- 构建工具: Maven
- 数据库: MySQL 8.0
- 缓存: Redis

## 快速开始

### 1. 环境准备
- JDK 1.8+
- Maven 3.6+
- MySQL 8.0+
- Redis 5.0+

### 2. 导入数据库
执行`智索APP数据库设计.sql`脚本创建数据库和表结构。

### 3. 修改配置
修改`application.yml`中的数据库和Redis配置：

```yaml
spring:
  datasource:
    url: ***********************************************************************************************************************
    username: root
    password: 123456
  redis:
    host: localhost
    port: 6379
    password:
```

### 4. 运行项目
```bash
mvn spring-boot:run
```

### 5. 接口测试
可通过Postman等工具进行接口测试，默认短信验证码为"6666"。

## API接口说明

### 1. 发送短信验证码
- 接口URL: `/api/v1/auth/sms/send`
- 请求方式: POST
- 请求参数:
  ```json
  {
    "phone": "13800138000",
    "type": "login"
  }
  ```

### 2. 短信验证码登录
- 接口URL: `/api/v1/auth/login/sms`
- 请求方式: POST
- 请求参数:
  ```json
  {
    "phone": "13800138000",
    "code": "6666",
    "deviceId": "device_id_123456",
    "deviceType": "android"
  }
  ```

### 3. 密码登录
- 接口URL: `/api/v1/auth/login/password`
- 请求方式: POST
- 请求参数:
  ```json
  {
    "phone": "13800138000",
    "password": "md5加密后的密码",
    "deviceId": "device_id_123456",
    "deviceType": "android"
  }
  ```

### 4. 刷新令牌
- 接口URL: `/api/v1/auth/token/refresh`
- 请求方式: POST
- 请求参数:
  ```json
  {
    "refreshToken": "刷新令牌"
  }
  ```

### 5. 退出登录
- 接口URL: `/api/v1/auth/logout`
- 请求方式: POST
- 请求头: `Authorization: Bearer {token}`
- 请求参数: 无 