package com.zhisuo.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 内容标签关联实体类
 */
@Data
@TableName("content_tags")
public class ContentTag {
    
    @TableId
    private String id;           // 主键
    
    private String contentId;    // 内容ID(文章或热点)
    
    private String contentType;  // 内容类型(article/topic)
    
    private String tagId;        // 标签ID
    
    private Date createTime;     // 创建时间
} 