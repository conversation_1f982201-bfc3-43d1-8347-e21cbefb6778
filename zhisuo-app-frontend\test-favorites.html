<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>收藏功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .card {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        
        .api-test {
            margin-bottom: 30px;
        }
        
        .api-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #722ED1;
        }
        
        .api-url {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 6px;
            font-family: monospace;
            margin-bottom: 10px;
            word-break: break-all;
        }
        
        .test-btn {
            background: #722ED1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .test-btn:hover {
            background: #5a1ea6;
        }
        
        .result {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-top: 10px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        
        .error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        
        .input-group {
            margin-bottom: 15px;
        }
        
        .input-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }
        
        .input-group input, .input-group select {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        
        .checkbox-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 10px;
        }
        
        .checkbox-item {
            display: flex;
            align-items: center;
            gap: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="card">
            <h1 class="title">收藏功能 API 测试</h1>
            
            <!-- 获取收藏列表 -->
            <div class="api-test">
                <div class="api-title">1. 获取收藏列表</div>
                <div class="api-url">GET /v1/favorites/my</div>
                
                <div class="input-group">
                    <label>内容类型筛选:</label>
                    <select id="contentTypeFilter">
                        <option value="">全部</option>
                        <option value="article">文章</option>
                        <option value="topic">话题</option>
                    </select>
                </div>
                
                <div class="input-group">
                    <label>页码:</label>
                    <input type="number" id="pageNum" value="0" min="0">
                </div>
                
                <div class="input-group">
                    <label>每页大小:</label>
                    <input type="number" id="pageSize" value="10" min="1" max="50">
                </div>
                
                <button class="test-btn" onclick="testGetFavorites()">测试获取收藏列表</button>
                <div id="getFavoritesResult" class="result" style="display: none;"></div>
            </div>
            
            <!-- 添加/取消收藏 -->
            <div class="api-test">
                <div class="api-title">2. 添加/取消收藏</div>
                <div class="api-url">POST /v1/favorites</div>
                
                <div class="input-group">
                    <label>内容ID:</label>
                    <input type="text" id="contentId" placeholder="输入内容ID">
                </div>
                
                <div class="input-group">
                    <label>内容类型:</label>
                    <select id="contentType">
                        <option value="article">文章</option>
                        <option value="topic">话题</option>
                    </select>
                </div>
                
                <button class="test-btn" onclick="testToggleFavorite()">测试切换收藏状态</button>
                <div id="toggleFavoriteResult" class="result" style="display: none;"></div>
            </div>
            
            <!-- 检查收藏状态 -->
            <div class="api-test">
                <div class="api-title">3. 检查收藏状态</div>
                <div class="api-url">GET /v1/favorites/check</div>
                
                <button class="test-btn" onclick="testCheckFavorite()">测试检查收藏状态</button>
                <div id="checkFavoriteResult" class="result" style="display: none;"></div>
            </div>
            
            <!-- 批量删除收藏 -->
            <div class="api-test">
                <div class="api-title">4. 批量删除收藏</div>
                <div class="api-url">DELETE /v1/favorites/batch</div>
                
                <div class="input-group">
                    <label>收藏ID列表 (从上面的收藏列表中选择):</label>
                    <div id="favoriteIdsList" class="checkbox-group">
                        <!-- 动态生成 -->
                    </div>
                </div>
                
                <button class="test-btn" onclick="testBatchDelete()">测试批量删除</button>
                <div id="batchDeleteResult" class="result" style="display: none;"></div>
            </div>
        </div>
    </div>

    <script>
        // 模拟的 JWT token，实际使用时需要从登录接口获取
        const mockToken = 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...';
        const baseUrl = 'http://localhost:8080'; // 后端服务地址
        
        let currentFavorites = [];
        
        // 通用请求函数
        async function apiRequest(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': mockToken
                }
            };
            
            const finalOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };
            
            try {
                const response = await fetch(baseUrl + url, finalOptions);
                const data = await response.json();
                return { success: response.ok, data, status: response.status };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }
        
        // 显示结果
        function showResult(elementId, result, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.style.display = 'block';
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            element.textContent = JSON.stringify(result, null, 2);
        }
        
        // 测试获取收藏列表
        async function testGetFavorites() {
            const contentType = document.getElementById('contentTypeFilter').value;
            const page = document.getElementById('pageNum').value;
            const size = document.getElementById('pageSize').value;
            
            let url = `/v1/favorites/my?page=${page}&size=${size}`;
            if (contentType) {
                url += `&contentType=${contentType}`;
            }
            
            const result = await apiRequest(url);
            showResult('getFavoritesResult', result, result.success);
            
            // 保存收藏列表用于批量删除测试
            if (result.success && result.data.data && result.data.data.content) {
                currentFavorites = result.data.data.content;
                updateFavoriteIdsList();
            }
        }
        
        // 更新收藏ID列表
        function updateFavoriteIdsList() {
            const container = document.getElementById('favoriteIdsList');
            container.innerHTML = '';
            
            currentFavorites.forEach(favorite => {
                const div = document.createElement('div');
                div.className = 'checkbox-item';
                div.innerHTML = `
                    <input type="checkbox" id="fav_${favorite.favoriteId}" value="${favorite.favoriteId}">
                    <label for="fav_${favorite.favoriteId}">${favorite.contentTitle || favorite.favoriteId}</label>
                `;
                container.appendChild(div);
            });
        }
        
        // 测试切换收藏状态
        async function testToggleFavorite() {
            const contentId = document.getElementById('contentId').value;
            const contentType = document.getElementById('contentType').value;
            
            if (!contentId) {
                showResult('toggleFavoriteResult', { error: '请输入内容ID' }, false);
                return;
            }
            
            const result = await apiRequest('/v1/favorites', {
                method: 'POST',
                body: JSON.stringify({
                    contentId,
                    contentType
                })
            });
            
            showResult('toggleFavoriteResult', result, result.success);
        }
        
        // 测试检查收藏状态
        async function testCheckFavorite() {
            const contentId = document.getElementById('contentId').value;
            const contentType = document.getElementById('contentType').value;
            
            if (!contentId) {
                showResult('checkFavoriteResult', { error: '请输入内容ID' }, false);
                return;
            }
            
            const result = await apiRequest(`/v1/favorites/check?contentId=${contentId}&contentType=${contentType}`);
            showResult('checkFavoriteResult', result, result.success);
        }
        
        // 测试批量删除
        async function testBatchDelete() {
            const checkboxes = document.querySelectorAll('#favoriteIdsList input[type="checkbox"]:checked');
            const favoriteIds = Array.from(checkboxes).map(cb => cb.value);
            
            if (favoriteIds.length === 0) {
                showResult('batchDeleteResult', { error: '请选择要删除的收藏项' }, false);
                return;
            }
            
            const result = await apiRequest('/v1/favorites/batch', {
                method: 'DELETE',
                body: JSON.stringify(favoriteIds)
            });
            
            showResult('batchDeleteResult', result, result.success);
            
            // 删除成功后刷新收藏列表
            if (result.success) {
                setTimeout(() => {
                    testGetFavorites();
                }, 1000);
            }
        }
        
        // 页面加载时自动测试获取收藏列表
        window.onload = function() {
            console.log('收藏功能测试页面已加载');
            console.log('请确保后端服务已启动，并且已经登录获取有效的JWT token');
        };
    </script>
</body>
</html>
