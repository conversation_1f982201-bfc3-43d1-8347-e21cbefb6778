package com.zhisuo.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhisuo.app.entity.ContentTag;
import com.zhisuo.app.entity.Tag;

import java.util.List;

/**
 * 内容标签关联Service接口
 */
public interface ContentTagService extends IService<ContentTag> {
    
    /**
     * 为内容添加标签
     * 
     * @param contentId 内容ID
     * @param contentType 内容类型(article/topic)
     * @param tagId 标签ID
     * @return 是否添加成功
     */
    boolean addContentTag(String contentId, String contentType, String tagId);
    
    /**
     * 批量为内容添加标签
     * 
     * @param contentId 内容ID
     * @param contentType 内容类型(article/topic)
     * @param tagIds 标签ID列表
     * @return 是否添加成功
     */
    boolean batchAddContentTags(String contentId, String contentType, List<String> tagIds);
    
    /**
     * 为内容添加标签（通过标签名称）
     * 如果标签不存在，则创建标签
     * 
     * @param contentId 内容ID
     * @param contentType 内容类型(article/topic)
     * @param tagName 标签名称
     * @param category 标签分类
     * @return 是否添加成功
     */
    boolean addContentTagByName(String contentId, String contentType, String tagName, String category);
    
    /**
     * 批量为内容添加标签（通过标签名称）
     * 如果标签不存在，则创建标签
     * 
     * @param contentId 内容ID
     * @param contentType 内容类型(article/topic)
     * @param tagNames 标签名称列表
     * @param category 标签分类
     * @return 是否添加成功
     */
    boolean batchAddContentTagsByName(String contentId, String contentType, List<String> tagNames, String category);
    
    /**
     * 获取内容的标签列表
     * 
     * @param contentId 内容ID
     * @param contentType 内容类型(article/topic)
     * @return 标签列表
     */
    List<Tag> getContentTags(String contentId, String contentType);
    
    /**
     * 删除内容的所有标签
     * 
     * @param contentId 内容ID
     * @param contentType 内容类型(article/topic)
     * @return 是否删除成功
     */
    boolean deleteContentTags(String contentId, String contentType);
    
    /**
     * 更新内容的标签
     * 先删除原有标签，再添加新标签
     * 
     * @param contentId 内容ID
     * @param contentType 内容类型(article/topic)
     * @param tagIds 新的标签ID列表
     * @return 是否更新成功
     */
    boolean updateContentTags(String contentId, String contentType, List<String> tagIds);
    
    /**
     * 更新内容的标签（通过标签名称）
     * 先删除原有标签，再添加新标签
     * 如果标签不存在，则创建标签
     *
     * @param contentId 内容ID
     * @param contentType 内容类型(article/topic)
     * @param tagNames 新的标签名称列表
     * @param category 标签分类
     * @return 是否更新成功
     */
    boolean updateContentTagsByName(String contentId, String contentType, List<String> tagNames, String category);

    /**
     * 根据标签ID获取内容ID列表
     *
     * @param tagId 标签ID
     * @param contentType 内容类型(article/topic)
     * @return 内容ID列表
     */
    List<String> getContentIdsByTag(String tagId, String contentType);

    /**
     * 统计标签的内容数量
     *
     * @param tagId 标签ID
     * @return 内容数量
     */
    long getContentCountByTag(String tagId);
}