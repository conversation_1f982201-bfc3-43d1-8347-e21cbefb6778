package com.zhisuo.app.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhisuo.app.entity.HotTopic;
import com.zhisuo.app.mapper.HotTopicMapper;
import com.zhisuo.app.service.ContentTagService;
import com.zhisuo.app.service.HotTopicService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

import org.json.JSONArray;
import org.json.JSONObject;

import javax.annotation.PostConstruct;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import com.zhisuo.app.util.RedisUtil;
import org.springframework.scheduling.annotation.Scheduled;

/**
 * 热点话题Service实现类
 */
@Slf4j
@Service
public class HotTopicServiceImpl extends ServiceImpl<HotTopicMapper, HotTopic> implements HotTopicService {
    
    @Autowired
    private HotTopicMapper hotTopicMapper;
    
    @Autowired
    private RedisUtil redisUtil;

    @Autowired
    private ContentTagService contentTagService;
    
    // API基础URL
    private static final String API_BASE_URL = "https://newsnow.busiyi.world/api";
    
    // 热门来源列表
    private static final String[] HOT_SOURCES = {
        "toutiao", "wallstreetcn-hot", "cls-hot", "thepaper","hupu"
    };
    
    // 来源分类
    private static final Map<String, String> SOURCE_CATEGORIES = new HashMap<>();
    static {
        SOURCE_CATEGORIES.put("toutiao", "娱乐");
        SOURCE_CATEGORIES.put("wallstreetcn-hot", "金融");
        SOURCE_CATEGORIES.put("cls-hot", "金融");
        SOURCE_CATEGORIES.put("thepaper", "新闻");
        SOURCE_CATEGORIES.put("hupu","娱乐");
    }
    
    // 来源名称映射
    private static final Map<String, String> SOURCE_NAMES = new HashMap<>();
    static {
        SOURCE_NAMES.put("toutiao", "今日头条");
        SOURCE_NAMES.put("wallstreetcn-hot", "华尔街见闻");
        SOURCE_NAMES.put("cls-hot", "财联社");
        SOURCE_NAMES.put("thepaper", "澎湃新闻");
        SOURCE_NAMES.put("hupu","虎扑");
    }
    
    /**
     * 应用启动时立即执行一次热点数据获取
     */
    @PostConstruct
    public void init() {
        log.info("应用启动，开始初始化热点数据...");
        fetchAndUpdateHotTopics();
    }
    
    @Override
    public List<HotTopic> getHotTopicList(Integer limit) {
        LambdaQueryWrapper<HotTopic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HotTopic::getStatus, 1)
                .orderByDesc(HotTopic::getHotValue) // 修改为按热度值降序排序
                .last(limit != null, "LIMIT " + limit);
        return list(queryWrapper);
    }

    @Override
    public Page<HotTopic> getHotTopicPage(Page<HotTopic> page, String source) {
        LambdaQueryWrapper<HotTopic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HotTopic::getStatus, 1)
                .eq(source != null && !source.isEmpty(), HotTopic::getSource, source)
                .orderByDesc(HotTopic::getHotValue); // 修改为按热度值降序排序
        
        return page(page, queryWrapper);
    }

    @Override
    public Page<HotTopic> getTodayHotTopicPage(Page<HotTopic> page, String source) {
        // 获取今天的开始时间（0点0分0秒）
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date todayStart = calendar.getTime();
        
        // 获取今天的结束时间（23点59分59秒）
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Date todayEnd = calendar.getTime();
        
        // 构建查询条件
        LambdaQueryWrapper<HotTopic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HotTopic::getStatus, 1)
                .eq(source != null && !source.isEmpty(), HotTopic::getSource, source)
                .ge(HotTopic::getCollectTime, todayStart)  // 大于等于今天开始时间
                .le(HotTopic::getCollectTime, todayEnd)    // 小于等于今天结束时间
                .orderByDesc(HotTopic::getHotValue);       // 按热度值降序排序
        
        return page(page, queryWrapper);
    }

    @Override
    public Page<HotTopic> getYesterdayHotTopicPage(Page<HotTopic> page, String source) {
        // 获取昨天的开始时间（0点0分0秒）
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -1);  // 减去1天
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        Date yesterdayStart = calendar.getTime();
        
        // 获取昨天的结束时间（23点59分59秒）
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        calendar.set(Calendar.MILLISECOND, 999);
        Date yesterdayEnd = calendar.getTime();
        
        // 构建查询条件
        LambdaQueryWrapper<HotTopic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HotTopic::getStatus, 1)
                .eq(source != null && !source.isEmpty(), HotTopic::getSource, source)
                .ge(HotTopic::getCollectTime, yesterdayStart)  // 大于等于昨天开始时间
                .le(HotTopic::getCollectTime, yesterdayEnd)    // 小于等于昨天结束时间
                .orderByDesc(HotTopic::getHotValue);           // 按热度值降序排序
        
        return page(page, queryWrapper);
    }

    @Override
    public void updateViewCount(String topicId) {
        // 使用Redis增加浏览量，不直接操作数据库
        redisUtil.incrementHotTopicViewCount(topicId);
        log.info("Redis增加热点话题浏览量: {}", topicId);
    }
    
    @Override
    public void updateSearchCount(String topicId) {
        // 使用Redis增加搜索量，不直接操作数据库
        redisUtil.incrementHotTopicSearchCount(topicId);
        log.info("Redis增加热点话题搜索量: {}", topicId);
    }

    /**
     * 获取热点话题昨日结束时的热度值
     *
     * @param topicId 热点话题ID
     * @return 昨日结束时的热度值，如果没有则返回null
     */
    private String getYesterdayEndHotValue(String topicId) {
        try {
            // 获取昨天的日期
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -1);
            Date yesterday = calendar.getTime();

            // 从Redis中获取昨日保存的热度值
            // 这个值是通过每日凌晨的定时任务保存的，代表昨日结束时的实际热度值
            String savedValue = redisUtil.getHotValueByDate(topicId, yesterday);
            if (savedValue != null) {
                return savedValue;
            }

            // 如果Redis中没有，说明这个热点是今天新增的，或者昨天的数据没有被正确保存
            // 返回null，让调用方处理
            return null;
        } catch (Exception e) {
            log.error("获取热点ID={} 昨日结束热度值失败: {}", topicId, e.getMessage());
            return null;
        }
    }

    /**
     * 每日凌晨重置所有热点的趋势为持平状态，并保存昨日热度值
     * 每天凌晨1点执行
     */
    @Scheduled(cron = "0 0 1 * * ?")
    public void resetDailyTrends() {
        try {
            log.info("开始执行每日趋势重置任务...");

            // 获取所有状态为1的热点
            LambdaQueryWrapper<HotTopic> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HotTopic::getStatus, 1);
            List<HotTopic> allTopics = list(queryWrapper);

            if (allTopics.isEmpty()) {
                log.info("没有需要重置趋势的热点");
                return;
            }

            // 获取昨天的日期
            Calendar yesterday = Calendar.getInstance();
            yesterday.add(Calendar.DAY_OF_MONTH, -1);
            Date yesterdayDate = yesterday.getTime();

            // 批量重置趋势为0（持平）并保存昨日热度值
            int resetCount = 0;
            for (HotTopic topic : allTopics) {
                try {
                    // 保存当前数据库中的热度值作为昨日结束时的热度值到Redis
                    // 这个热度值是昨天结束时的实际热度值，设置1天过期时间
                    String currentHotValue = topic.getHotValue();
                    redisUtil.saveHotValueByDate(topic.getTopicId(), yesterdayDate, currentHotValue, 1, java.util.concurrent.TimeUnit.DAYS);

                    // 重置趋势为持平
                    hotTopicMapper.updateTrend(topic.getTopicId(), 0);
                    resetCount++;

                    log.debug("热点ID={} 保存昨日热度值={}, 重置趋势为持平", topic.getTopicId(), currentHotValue);
                } catch (Exception e) {
                    log.error("重置热点ID={} 趋势失败: {}", topic.getTopicId(), e.getMessage());
                }
            }

            log.info("每日趋势重置完成，成功重置 {} 个热点的趋势为持平状态，并保存了昨日热度值", resetCount);
        } catch (Exception e) {
            log.error("每日趋势重置任务执行失败: {}", e.getMessage(), e);
        }
    }



    /**
     * 定时同步Redis中的计数器数据到数据库
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000)
    public void syncCountersToDatabase() {
        log.info("开始同步Redis计数器数据到数据库...");
        
        try {
            // 同步浏览量
            Map<String, Integer> viewCounts = redisUtil.getAllHotTopicViewCounts();
            if (!viewCounts.isEmpty()) {
                log.info("同步热点话题浏览量数据, 数量: {}", viewCounts.size());
                for (Map.Entry<String, Integer> entry : viewCounts.entrySet()) {
                    String topicId = entry.getKey();
                    Integer viewCount = entry.getValue();
                    
                    // 更新数据库中的浏览量
                    hotTopicMapper.updateViewCountFromRedis(topicId, viewCount);
                    
                    // 更新后删除Redis中的计数器，避免重复计算
                    redisUtil.delete(RedisUtil.HOT_TOPIC_VIEW_COUNT_PREFIX + topicId);
                }
            }
            
            // 同步搜索量
            Map<String, Integer> searchCounts = redisUtil.getAllHotTopicSearchCounts();
            if (!searchCounts.isEmpty()) {
                log.info("同步热点话题搜索量数据, 数量: {}", searchCounts.size());
                for (Map.Entry<String, Integer> entry : searchCounts.entrySet()) {
                    String topicId = entry.getKey();
                    Integer searchCount = entry.getValue();
                    
                    // 更新数据库中的搜索量
                    hotTopicMapper.updateSearchCountFromRedis(topicId, searchCount);
                    
                    // 更新后删除Redis中的计数器，避免重复计算
                    redisUtil.delete(RedisUtil.HOT_TOPIC_SEARCH_COUNT_PREFIX + topicId);
                }
            }
            
            // 更新Redis中的热度值
            // 只更新被访问过的热点话题（浏览量或搜索量有变化的）
            Set<String> updatedTopicIds = new HashSet<>();
            updatedTopicIds.addAll(viewCounts.keySet());
            updatedTopicIds.addAll(searchCounts.keySet());
            
            if (!updatedTopicIds.isEmpty()) {
                log.info("更新Redis中的热度值, 数量: {}", updatedTopicIds.size());
                
                // 查询这些热点话题的最新数据
                List<HotTopic> updatedTopics = listByIds(updatedTopicIds);
                
                for (HotTopic topic : updatedTopics) {
                    String topicId = topic.getTopicId();
                    String currentHotValue = topic.getHotValue();
                    
                    // 将当前热度值保存到Redis
                    redisUtil.saveHotTopicHotValue(topicId, currentHotValue, RedisUtil.HOT_VALUE_EXPIRE_TIME);
                    
                    // 保存今天的热度值，设置2天过期时间（今天+明天用于趋势计算）
                    Date today = new Date();
                    redisUtil.saveHotValueByDate(topicId, today, currentHotValue, 2, java.util.concurrent.TimeUnit.DAYS);
                    
                    // 获取昨天的日期
                    Calendar calendar = Calendar.getInstance();
                    calendar.add(Calendar.DAY_OF_MONTH, -1);
                    Date yesterday = calendar.getTime();
                    
                    // 获取昨天的热度值
                    String yesterdayHotValue = redisUtil.getHotValueByDate(topicId, yesterday);
                    
                    // 如果没有昨天的热度值，需要根据热点的收集时间来确定昨日基准值
                    if (yesterdayHotValue == null) {
                        try {
                            // 检查热点的收集时间
                            Calendar collectCalendar = Calendar.getInstance();
                            collectCalendar.setTime(topic.getCollectTime());
                            Calendar yesterdayCalendar = Calendar.getInstance();
                            yesterdayCalendar.setTime(yesterday);

                            boolean isYesterdayTopic = collectCalendar.get(Calendar.YEAR) == yesterdayCalendar.get(Calendar.YEAR) &&
                                                     collectCalendar.get(Calendar.DAY_OF_YEAR) == yesterdayCalendar.get(Calendar.DAY_OF_YEAR);

                            if (isYesterdayTopic) {
                                // 对于昨日收集的热点，尝试获取昨日结束时的热度值
                                yesterdayHotValue = getYesterdayEndHotValue(topicId);
                                if (yesterdayHotValue == null) {
                                    // 如果没有昨日数据，说明是今天第一次访问昨日热点
                                    // 使用0作为昨日基准值（昨日收集时的初始值）
                                    yesterdayHotValue = "0.0";
                                    log.info("热点ID={} 是昨日收集的热点，但没有昨日热度值记录，使用0作为基准", topicId);
                                }
                            } else {
                                // 对于更早的热点，使用当前值的80%作为基准（估算）
                                double value = Double.parseDouble(currentHotValue);
                                yesterdayHotValue = String.format("%.1f", value * 0.8);
                                log.info("热点ID={} 是更早收集的热点，使用当前值的80%作为昨日基准: {}", topicId, yesterdayHotValue);
                            }

                            // 保存这个昨日热度值，避免重复计算，设置1天过期时间
                            redisUtil.saveHotValueByDate(topicId, yesterday, yesterdayHotValue, 1, java.util.concurrent.TimeUnit.DAYS);
                        } catch (NumberFormatException e) {
                            yesterdayHotValue = "0.0";
                        }
                    }
                    
                    log.info("热点ID={} 今日热度值={}, 昨日热度值={}", topicId, currentHotValue, yesterdayHotValue);
                    
                    // 计算趋势（改进版本）
                    try {
                        double yesterdayValue = Double.parseDouble(yesterdayHotValue);
                        double todayValue = Double.parseDouble(currentHotValue);

                        // 应用衰减机制：对于没有用户交互的热点，热度值按规则下降
                        double adjustedTodayValue = applyHotValueDecay(topic, todayValue, yesterdayValue);

                        // 如果应用了衰减，更新数据库中的热度值
                        if (adjustedTodayValue != todayValue) {
                            topic.setHotValue(String.format("%.1f", adjustedTodayValue));
                            hotTopicMapper.updateById(topic);
                            log.info("热点ID={} 应用衰减后热度值: {} -> {}", topicId, todayValue, adjustedTodayValue);
                        }

                        // 计算趋势：基于调整后的热度值
                        int trend = calculateTrend(adjustedTodayValue, yesterdayValue);

                        // 更新趋势
                        hotTopicMapper.updateTrend(topicId, trend);
                        log.info("热点ID={}, 昨日热度={}, 今日热度={}, 趋势={}",
                                topicId, yesterdayValue, adjustedTodayValue, getTrendText(trend));
                    } catch (NumberFormatException e) {
                        log.warn("热点ID={} 热度值转换失败: {}", topicId, e.getMessage());
                    }
                    
                    log.info("热点ID={} 更新当前热度值: {}", topicId, currentHotValue);
                }
                
                // 只刷新有更新的热点话题排名
                refreshHotTopicRanksForTopics(updatedTopicIds);
            }
            
            log.info("Redis计数器数据同步到数据库完成");
        } catch (Exception e) {
            log.error("同步Redis计数器数据到数据库失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 定时更新热点话题的热度值历史记录和趋势
     * 每天凌晨2点执行，应用衰减机制并更新趋势
     */
    @Override
    @Scheduled(cron = "0 0 2 * * ?") // 每天凌晨2点执行
    public void updateHotValueHistory() {
        log.info("开始定时更新热点话题热度值和趋势...");

        try {
            // 先同步Redis数据到数据库（这会触发趋势计算和衰减机制）
            syncCountersToDatabase();

            // 然后刷新排名
            refreshHotTopicRanks();

            log.info("定时更新热点话题热度值和趋势完成");
        } catch (Exception e) {
            log.error("定时更新热点话题热度值和趋势失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 更新昨日热点话题的热度值历史记录和趋势
     * 已废弃，趋势判断现在在热度值变化时进行
     * 保留此方法是为了接口兼容性
     */
    @Override
    public void updateYesterdayHotValueHistory() {
        log.info("此方法已废弃，趋势判断现在在热度值变化时进行");
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fetchAndUpdateHotTopics() {
        try {
            log.info("开始获取热点数据...");
            Map<String, List<NewsItem>> allNews = fetchAllSources();
            
            if (allNews.isEmpty()) {
                log.info("未获取到数据，跳过更新");
                return;
            }
            
            log.info("获取到 {} 个来源的数据", allNews.size());
            updateDatabase(allNews);
            log.info("热点数据更新完成");
        } catch (Exception e) {
            log.error("获取或更新热点数据时出错: {}", e.getMessage(), e);
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void fetchAndUpdateYesterdayHotTopics() {
        try {
            log.info("开始获取昨日热点数据...");
            
            // 获取昨天的日期范围
            Calendar calendar = Calendar.getInstance();
            calendar.add(Calendar.DAY_OF_MONTH, -1);  // 减去1天
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            calendar.set(Calendar.MILLISECOND, 0);
            Date yesterdayStart = calendar.getTime();
            
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            calendar.set(Calendar.MILLISECOND, 999);
            Date yesterdayEnd = calendar.getTime();
            
            // 查询昨天的热点数据
            LambdaQueryWrapper<HotTopic> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HotTopic::getStatus, 1)
                    .ge(HotTopic::getCollectTime, yesterdayStart)
                    .le(HotTopic::getCollectTime, yesterdayEnd)
                    .orderByDesc(HotTopic::getHotValue);
            
            List<HotTopic> yesterdayTopics = list(queryWrapper);
            
            if (yesterdayTopics.isEmpty()) {
                log.info("未找到昨日热点数据，可能需要从其他数据源获取");
            } else {
                log.info("找到 {} 条昨日热点数据", yesterdayTopics.size());
            }
            
            log.info("昨日热点数据更新完成");
        } catch (Exception e) {
            log.error("获取或更新昨日热点数据时出错: {}", e.getMessage(), e);
            throw e;
        }
    }
    
    /**
     * 获取所有来源的数据
     */
    private Map<String, List<NewsItem>> fetchAllSources() throws ExecutionException, InterruptedException {
        Map<String, List<NewsItem>> result = new HashMap<>();
        
        // 并行获取所有来源的数据
        List<CompletableFuture<Map.Entry<String, List<NewsItem>>>> futures = new ArrayList<>();
        
        for (String source : HOT_SOURCES) {
            CompletableFuture<Map.Entry<String, List<NewsItem>>> future = CompletableFuture.supplyAsync(() -> {
                try {
                    List<NewsItem> items = fetchSourceData(source);
                    return new AbstractMap.SimpleEntry<>(source, items);
                } catch (Exception e) {
                    log.error("获取来源 {} 数据失败: {}", source, e.getMessage());
                    return new AbstractMap.SimpleEntry<>(source, new ArrayList<>());
                }
            });
            
            futures.add(future);
        }
        
        // 等待所有请求完成
        CompletableFuture.allOf(futures.toArray(new CompletableFuture[0])).get();
        
        // 收集结果
        for (CompletableFuture<Map.Entry<String, List<NewsItem>>> future : futures) {
            Map.Entry<String, List<NewsItem>> entry = future.get();
            if (!entry.getValue().isEmpty()) {
                result.put(entry.getKey(), entry.getValue());
            }
        }
        
        return result;
    }
    
    /**
     * 获取单个来源的数据
     */
    private List<NewsItem> fetchSourceData(String sourceId) throws IOException {
        String urlStr = API_BASE_URL + "/s?id=" + sourceId;
        URL url = new URL(urlStr);
        
        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
        connection.setRequestMethod("GET");
        connection.setRequestProperty("Accept", "application/json");
        connection.setRequestProperty("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8");
        connection.setRequestProperty("Referer", "https://newsnow.busiyi.world/");
        connection.setRequestProperty("Origin", "https://newsnow.busiyi.world");
        connection.setConnectTimeout(10000);
        connection.setReadTimeout(10000);
        
        int responseCode = connection.getResponseCode();
        if (responseCode != 200) {
            throw new IOException("API请求失败，状态码: " + responseCode);
        }
        
        StringBuilder response = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(
                new InputStreamReader(connection.getInputStream(), "UTF-8"))) {
            String line;
            while ((line = reader.readLine()) != null) {
                response.append(line);
            }
        }
        
        return parseResponse(response.toString(), sourceId);
    }
    
    /**
     * 解析API响应
     */
    private List<NewsItem> parseResponse(String responseBody, String sourceId) {
        List<NewsItem> items = new ArrayList<>();
        
        try {
            JSONObject json = new JSONObject(responseBody);
            JSONArray itemsArray = json.getJSONArray("items");
            
            // 只获取前2条热点内容
            int limit = Math.min(2, itemsArray.length());
            for (int i = 0; i < limit; i++) {
                JSONObject item = itemsArray.getJSONObject(i);

                String title = item.has("title") ? item.getString("title") : "";
                String url = item.has("url") ? item.getString("url") : "";
                
                long pubDate = System.currentTimeMillis(); // 默认使用当前时间
                if (item.has("pubDate")) {
                    Object dateObj = item.get("pubDate");
                    if (dateObj instanceof Number) {
                        pubDate = ((Number) dateObj).longValue();
                    } else if (dateObj instanceof String) {
                        try {
                            pubDate = Long.parseLong((String) dateObj);
                        } catch (NumberFormatException e) {
                            pubDate = new Date().getTime();
                            log.warn("解析日期失败，使用当前时间: {}", e.getMessage());
                        }
                    }
                }
                
                String category = SOURCE_CATEGORIES.getOrDefault(sourceId, "其他");

                items.add(new NewsItem(title, url, pubDate, sourceId, category));
            }
            
            log.info("获取来源 {} 的 {} 条热点内容", sourceId, limit);
        } catch (Exception e) {
            log.error("解析来源 {} 数据失败: {}", sourceId, e.getMessage());
        }
        
        return items;
    }
    
    /**
     * 更新数据库，只添加新的热点
     */
    private void updateDatabase(Map<String, List<NewsItem>> allNews) {
        List<NewsItem> allItems = allNews.values().stream()
                .flatMap(List::stream)
                .sorted((a, b) -> Long.compare(b.getPubDate(), a.getPubDate()))
                .collect(Collectors.toList());
        
        // 获取当前数据库中的所有热点标题
        Map<String, HotTopic> existingTopicMap = new HashMap<>();
        List<HotTopic> existingTopics = list();
        
        // 获取当前最大排名
        int maxRank = 0;
        
        if (existingTopics != null && !existingTopics.isEmpty()) {
            for (HotTopic topic : existingTopics) {
                existingTopicMap.put(topic.getTitle(), topic);
                if (topic.getRank() != null && topic.getRank() > maxRank) {
                    maxRank = topic.getRank();
                }
            }
        }
        
        List<HotTopic> newTopics = new ArrayList<>();
        List<HotTopic> updatedTopics = new ArrayList<>();
        int rank = maxRank + 1;
        
        // 根据发布时间计算热度值，越新的热度值越高
        long currentTime = System.currentTimeMillis();
        
        for (NewsItem item : allItems) {
            // 检查标题是否已存在
            HotTopic existingTopic = existingTopicMap.get(item.getTitle());
            
            if (existingTopic != null) {
                // 标题已存在，只更新URL等信息，保留原始收集时间和其他统计数据
                log.info("更新已存在的热点: {}", item.getTitle());
                existingTopic.setSourceUrl(item.getUrl());
                existingTopic.setUpdateTime(new Date()); // 更新时间设为当前时间
                updatedTopics.add(existingTopic);
                
                // 更新标签（如果有分类信息）
                if (item.getCategory() != null && !item.getCategory().trim().isEmpty()) {
                    updateTopicTags(existingTopic.getTopicId(), item.getCategory(), item.getSourceId());
                }
                
                continue;
            }
            
            // 创建新的热点话题
            HotTopic hotTopic = new HotTopic();
            hotTopic.setTopicId(IdUtil.fastSimpleUUID());
            hotTopic.setTitle(item.getTitle());
            hotTopic.setDescription(item.getTitle()); // 使用标题作为描述
            hotTopic.setSource(SOURCE_NAMES.getOrDefault(item.getSourceId(), item.getSourceId()));
            hotTopic.setSourceUrl(item.getUrl());
            
            // 对于新添加的热点，设置初始阅读量和搜索量为0
            long pubTime = item.getPubDate() > 0 ? item.getPubDate() : currentTime;
            int initialViewCount = 0;
            int initialSearchCount = 0;
            
            hotTopic.setViewCount(initialViewCount);
            hotTopic.setSearchCount(initialSearchCount);
            
            // 计算热度值：阅读量*0.6 + 搜索量*0.4
            double hotValue = hotTopic.getViewCount() * 0.6 + hotTopic.getSearchCount() * 0.4;
            hotTopic.setHotValue(String.format("%.1f", hotValue));

            // 新添加的热点默认趋势为持平(0)，因为没有历史数据进行比较
            hotTopic.setTrend(0);

            hotTopic.setRank(rank++);
            
            // 确保收集时间不为1970年
            Date collectTime = pubTime > 0 ? new Date(pubTime) : new Date(currentTime);
            hotTopic.setCollectTime(collectTime);
            hotTopic.setUpdateTime(new Date());
            hotTopic.setStatus(1);
            
            newTopics.add(hotTopic);
        }
        
        // 保存新热点
        if (!newTopics.isEmpty()) {
            saveBatch(newTopics);
            log.info("成功添加 {} 条新热点数据到数据库", newTopics.size());
            
            // 为新热点添加标签
            for (int i = 0; i < newTopics.size(); i++) {
                HotTopic topic = newTopics.get(i);
                NewsItem item = allItems.get(i);
                
                // 如果有分类信息，则添加标签
                if (item.getCategory() != null && !item.getCategory().trim().isEmpty()) {
                    addTopicTags(topic.getTopicId(), item.getCategory(), item.getSourceId());
                }
            }
        } else {
            log.info("没有新的热点数据需要添加");
        }
        
        // 更新已存在的热点
        if (!updatedTopics.isEmpty()) {
            updateBatchById(updatedTopics);
            log.info("成功更新 {} 条已存在的热点数据", updatedTopics.size());
        }
        
        // 如果有新增或更新，则刷新排名
        if (!newTopics.isEmpty() || !updatedTopics.isEmpty()) {
            // 更新所有热点的排名
            updateAllHotTopicRanks();
        }
    }
    
    /**
     * 为热点话题添加标签
     * 
     * @param topicId 话题ID
     * @param category 分类
     * @param sourceId 来源ID
     */
    private void addTopicTags(String topicId, String category, String sourceId) {
        try {
            // 获取来源对应的分类中文名称作为标签
            String categoryName = SOURCE_CATEGORIES.getOrDefault(sourceId, "其他");
            contentTagService.addContentTagByName(topicId, "topic", categoryName, null);
            
            log.info("为热点ID={}添加标签成功: 分类={}", topicId, categoryName);
        } catch (Exception e) {
            log.error("为热点ID={}添加标签失败: {}", topicId, e.getMessage(), e);
        }
    }
    
    /**
     * 更新热点话题标签
     * 
     * @param topicId 话题ID
     * @param category 分类
     * @param sourceId 来源ID
     */
    private void updateTopicTags(String topicId, String category, String sourceId) {
        try {
            // 获取来源对应的分类中文名称作为标签
            String categoryName = SOURCE_CATEGORIES.getOrDefault(sourceId, "其他");
            List<String> tagNames = new ArrayList<>();
            tagNames.add(categoryName);
            
            // 更新标签（先删除原有标签，再添加新标签）
            contentTagService.updateContentTagsByName(topicId, "topic", tagNames, null);
            
            log.info("更新热点ID={}的标签成功: 分类={}", topicId, categoryName);
        } catch (Exception e) {
            log.error("更新热点ID={}的标签失败: {}", topicId, e.getMessage(), e);
        }
    }
    
    /**
     * 更新所有热点的排名
     * 按照热度值重新计算排名
     */
    private void updateAllHotTopicRanks() {
        refreshHotTopicRanks();
    }
    
    @Override
    public void refreshHotTopicRanks() {
        try {
            log.info("开始刷新热点话题排名...");
            
            // 获取所有热点并按热度值降序排序
            LambdaQueryWrapper<HotTopic> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HotTopic::getStatus, 1)
                    .orderByDesc(HotTopic::getHotValue);
            
            List<HotTopic> allHotTopics = list(queryWrapper);
            
            if (allHotTopics.isEmpty()) {
                log.info("没有热点话题数据，跳过排名更新");
                return;
            }
            
            // 使用临时排名方案，但采用大数字而非负数作为临时值
            // 这样即使第二步失败，排名也不会是负数
            log.info("将所有热点话题排名设置为临时值...");
            int updateCount = 0;
            int totalCount = allHotTopics.size();
            int tempRankBase = 1000000; // 使用一个足够大的基数
            
            // 第一步：将所有排名设置为临时值（大数字），避免唯一键冲突
            for (int i = 0; i < totalCount; i++) {
                HotTopic topic = allHotTopics.get(i);
                int tempRank = tempRankBase + i; // 使用大数字作为临时排名
                
                try {
                    int result = hotTopicMapper.updateRank(topic.getTopicId(), tempRank);
                    if (result > 0) {
                        updateCount++;
                    }
                } catch (Exception e) {
                    log.error("更新热点ID={}的临时排名失败: {}", topic.getTopicId(), e.getMessage());
                }
            }
            
            log.info("成功将 {} 条热点数据排名设置为临时值", updateCount);
            
            // 第二步：将临时排名更新为最终排名
            log.info("将临时排名更新为最终排名...");
            updateCount = 0;
            
            for (int i = 0; i < totalCount; i++) {
                HotTopic topic = allHotTopics.get(i);
                int finalRank = i + 1; // 最终排名从1开始
                
                try {
                    int result = hotTopicMapper.updateRank(topic.getTopicId(), finalRank);
                    if (result > 0) {
                        updateCount++;
                    }
                } catch (Exception e) {
                    log.error("更新热点ID={}的最终排名失败: {}", topic.getTopicId(), e.getMessage());
                }
            }
            
            if (updateCount > 0) {
                log.info("成功更新 {} 条热点数据排名", updateCount);
            } else {
                log.info("排名未发生变化，无需更新");
            }
        } catch (Exception e) {
            log.error("刷新热点话题排名失败: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 刷新指定热点话题的排名
     * 为避免排名冲突，需要更新所有热点的排名
     * 
     * @param topicIds 需要刷新排名的热点话题ID集合（仅用于日志记录）
     */
    private void refreshHotTopicRanksForTopics(Set<String> topicIds) {
        try {
            log.info("开始刷新热点话题排名，由 {} 个热点触发更新", topicIds.size());
            
            // 获取所有热点并按热度值降序排序
            LambdaQueryWrapper<HotTopic> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(HotTopic::getStatus, 1)
                    .orderByDesc(HotTopic::getHotValue);
            
            List<HotTopic> allHotTopics = list(queryWrapper);
            
            if (allHotTopics.isEmpty()) {
                log.info("没有热点话题数据，跳过排名更新");
                return;
            }
            
            // 使用临时排名方案，但采用大数字作为临时值
            log.info("将所有热点话题排名设置为临时值...");
            int updateCount = 0;
            int totalCount = allHotTopics.size();
            int tempRankBase = 1000000; // 使用一个足够大的基数
            
            // 第一步：将所有排名设置为临时值（大数字），避免唯一键冲突
            for (int i = 0; i < totalCount; i++) {
                HotTopic topic = allHotTopics.get(i);
                int tempRank = tempRankBase + i; // 使用大数字作为临时排名
                
                try {
                    int result = hotTopicMapper.updateRank(topic.getTopicId(), tempRank);
                    if (result > 0) {
                        updateCount++;
                        // 只记录指定热点ID的日志，减少日志量
                        if (topicIds.contains(topic.getTopicId())) {
                            log.info("成功将热点ID={} 的排名设置为临时值 {}", topic.getTopicId(), tempRank);
                        }
                    }
                } catch (Exception e) {
                    log.error("更新热点ID={}的临时排名失败: {}", topic.getTopicId(), e.getMessage());
                }
            }
            
            log.info("成功将 {} 条热点数据排名设置为临时值", updateCount);
            
            // 第二步：将临时排名更新为最终排名
            log.info("将临时排名更新为最终排名...");
            updateCount = 0;
            
            for (int i = 0; i < totalCount; i++) {
                HotTopic topic = allHotTopics.get(i);
                int finalRank = i + 1; // 最终排名从1开始
                
                try {
                    int result = hotTopicMapper.updateRank(topic.getTopicId(), finalRank);
                    if (result > 0) {
                        updateCount++;
                        // 只记录指定热点ID的日志，减少日志量
                        if (topicIds.contains(topic.getTopicId())) {
                            log.info("成功更新热点ID={} 的排名为 {}", topic.getTopicId(), finalRank);
                        }
                    }
                } catch (Exception e) {
                    log.error("更新热点ID={}的最终排名失败: {}", topic.getTopicId(), e.getMessage());
                }
            }
            
            log.info("成功更新 {} 条热点数据排名", updateCount);
        } catch (Exception e) {
            log.error("刷新热点话题排名失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 应用热度值衰减机制
     *
     * @param topic 热点话题
     * @param currentValue 当前热度值
     * @param yesterdayValue 昨日热度值
     * @return 应用衰减后的热度值
     */
    private double applyHotValueDecay(HotTopic topic, double currentValue, double yesterdayValue) {
        // 计算话题的年龄（天数）
        long topicAgeInDays = getTopicAgeInDays(topic.getCollectTime());

        // 如果当前热度值大于昨日值，说明有新的用户交互，不应用衰减
        if (currentValue > yesterdayValue) {
            return currentValue;
        }

        // 计算衰减率：话题越老，衰减越快
        double decayRate = calculateDecayRate(topicAgeInDays);

        // 应用衰减：新热度值 = 昨日热度值 * (1 - 衰减率)
        double decayedValue = yesterdayValue * (1 - decayRate);

        // 返回当前值和衰减值中的较大者，确保有用户交互时热度不会下降
        return Math.max(currentValue, decayedValue);
    }

    /**
     * 计算话题年龄（天数）
     */
    private long getTopicAgeInDays(Date collectTime) {
        if (collectTime == null) {
            return 0;
        }
        long diffInMillis = System.currentTimeMillis() - collectTime.getTime();
        return diffInMillis / (24 * 60 * 60 * 1000);
    }

    /**
     * 计算衰减率
     *
     * @param ageInDays 话题年龄（天数）
     * @return 衰减率（0-1之间）
     */
    private double calculateDecayRate(long ageInDays) {
        // 衰减规则：
        // 1天内：衰减率 2%
        // 2-3天：衰减率 5%
        // 4-7天：衰减率 10%
        // 8-14天：衰减率 15%
        // 15天以上：衰减率 20%

        if (ageInDays <= 1) {
            return 0.02; // 2%
        } else if (ageInDays <= 3) {
            return 0.05; // 5%
        } else if (ageInDays <= 7) {
            return 0.10; // 10%
        } else if (ageInDays <= 14) {
            return 0.15; // 15%
        } else {
            return 0.20; // 20%
        }
    }

    /**
     * 计算趋势
     *
     * @param currentValue 当前热度值
     * @param yesterdayValue 昨日热度值
     * @return 趋势值（1:上升, 0:持平, -1:下降）
     */
    private int calculateTrend(double currentValue, double yesterdayValue) {
        double change = currentValue - yesterdayValue;

        // 趋势判断阈值：降低阈值使趋势更敏感
        double threshold = 0.1;

        if (change > threshold) {
            return 1; // 上升
        } else if (change < -threshold) {
            return -1; // 下降
        } else {
            return 0; // 持平
        }
    }

    /**
     * 获取趋势文本描述
     */
    private String getTrendText(int trend) {
        switch (trend) {
            case 1: return "上升";
            case -1: return "下降";
            default: return "持平";
        }
    }

    /**
     * 新闻条目类
     */
    private static class NewsItem {
        private final String title;
        private final String url;
        private final long pubDate;
        private final String sourceId;
        private final String category;

        public NewsItem(String title, String url, long pubDate, String sourceId, String category) {
            this.title = title;
            this.url = url;
            this.pubDate = pubDate;
            this.sourceId = sourceId;
            this.category = category;
        }

        public String getTitle() {
            return title;
        }
        
        public String getUrl() {
            return url;
        }
        
        public long getPubDate() {
            return pubDate;
        }
        
        public String getSourceId() {
            return sourceId;
        }
        
        public String getCategory() {
            return category;
        }
    }
} 