package com.zhisuo.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 用户兴趣标签实体类
 */
@Data
@TableName("user_interests")
public class UserInterest {
    
    @TableId
    private String interestId;   // 兴趣标签ID
    
    private String userId;       // 用户ID
    
    private String tagId;        // 标签ID
    
    private Float weight;        // 权重值
    
    private Date createTime;     // 创建时间
    
    private Date updateTime;     // 更新时间
} 