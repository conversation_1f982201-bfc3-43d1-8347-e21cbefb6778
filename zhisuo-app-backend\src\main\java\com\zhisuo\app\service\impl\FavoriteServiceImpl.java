package com.zhisuo.app.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhisuo.app.entity.UserFavorite;
import com.zhisuo.app.mapper.UserFavoriteMapper;
import com.zhisuo.app.service.FavoriteService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 收藏服务实现类
 */
@Slf4j
@Service
public class FavoriteServiceImpl extends ServiceImpl<UserFavoriteMapper, UserFavorite> implements FavoriteService {
    
    @Autowired
    private UserFavoriteMapper userFavoriteMapper;
    
    @Override
    @Transactional
    public boolean toggleFavorite(String contentId, String contentType, String userId) {
        UserFavorite existingFavorite = userFavoriteMapper.selectByUserAndContent(userId, contentId, contentType);
        
        if (existingFavorite != null) {
            // 已收藏，取消收藏
            removeById(existingFavorite.getFavoriteId());
            log.info("用户{}取消收藏: contentId={}, contentType={}", userId, contentId, contentType);
            return false;
        } else {
            // 未收藏，添加收藏
            UserFavorite userFavorite = new UserFavorite();
            userFavorite.setFavoriteId(UUID.randomUUID().toString().replace("-", ""));
            userFavorite.setUserId(userId);
            userFavorite.setContentId(contentId);
            userFavorite.setContentType(contentType);
            userFavorite.setCreateTime(new Date());
            
            save(userFavorite);
            log.info("用户{}收藏: contentId={}, contentType={}", userId, contentId, contentType);
            return true;
        }
    }
    
    @Override
    public boolean isFavorited(String contentId, String contentType, String userId) {
        UserFavorite userFavorite = userFavoriteMapper.selectByUserAndContent(userId, contentId, contentType);
        return userFavorite != null;
    }
    
    @Override
    public Map<String, Object> getUserFavorites(String userId, String contentType, Integer page, Integer size) {
        Page<Map<String, Object>> favoritePage = new Page<>(page + 1, size);
        favoritePage = userFavoriteMapper.selectUserFavorites(favoritePage, userId, contentType);
        
        Map<String, Object> result = new HashMap<>();
        result.put("content", favoritePage.getRecords());
        result.put("totalElements", favoritePage.getTotal());
        result.put("totalPages", favoritePage.getPages());
        result.put("size", favoritePage.getSize());
        result.put("number", favoritePage.getCurrent() - 1);
        
        return result;
    }
    
    @Override
    @Transactional
    public void deleteFavorites(List<String> favoriteIds, String userId) {
        if (favoriteIds == null || favoriteIds.isEmpty()) {
            return;
        }
        
        // 验证收藏记录是否属于当前用户
        QueryWrapper<UserFavorite> queryWrapper = new QueryWrapper<>();
        queryWrapper.in("favorite_id", favoriteIds)
                   .eq("user_id", userId);
        
        List<UserFavorite> favorites = list(queryWrapper);
        if (favorites.size() != favoriteIds.size()) {
            throw new RuntimeException("部分收藏记录不存在或无权删除");
        }
        
        // 批量删除
        removeByIds(favoriteIds);
        log.info("用户{}批量删除收藏: count={}", userId, favoriteIds.size());
    }
}
