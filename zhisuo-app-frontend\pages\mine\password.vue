<template>
	<view class="page">
		<!-- 顶部导航栏 -->
		<view class="navbar">
			<view class="navbar-left" @click="goBack">
				<uni-icons type="left" size="24" color="#333"></uni-icons>
			</view>
			<view class="navbar-title">修改密码</view>
			<view class="navbar-right"></view>
		</view>
		
		<!-- 内容区 -->
		<view class="content">
			<view class="password-card">
				<view class="form-item">
					<input :type="passwordVisible.old ? 'text' : 'password'" v-model="oldPassword" placeholder="请输入原密码" />
					<view class="icon-wrapper" @click="togglePasswordVisibility('old')">
						<uni-icons :type="passwordVisible.old ? 'eye-filled' : 'eye-slash-filled'" size="20" color="#999"></uni-icons>
					</view>
				</view>
				<view class="form-item">
					<input :type="passwordVisible.new ? 'text' : 'password'" v-model="newPassword" placeholder="请输入新密码" />
					<view class="icon-wrapper" @click="togglePasswordVisibility('new')">
						<uni-icons :type="passwordVisible.new ? 'eye-filled' : 'eye-slash-filled'" size="20" color="#999"></uni-icons>
					</view>
				</view>
				<view class="form-item">
					<input :type="passwordVisible.confirm ? 'text' : 'password'" v-model="confirmPassword" placeholder="请确认新密码" />
					<view class="icon-wrapper" @click="togglePasswordVisibility('confirm')">
						<uni-icons :type="passwordVisible.confirm ? 'eye-filled' : 'eye-slash-filled'" size="20" color="#999"></uni-icons>
					</view>
				</view>
				<view class="tips">
					<text>密码长度为6-20位，建议使用字母、数字和符号组合</text>
				</view>
				<view class="reset-button" @click="resetInputs">
					<uni-icons type="refresh" size="16" color="#3264ED"></uni-icons>
					<text>重置输入</text>
				</view>
			</view>
		</view>
		
		<!-- 保存按钮 -->
		<view class="save-button" @click="savePassword" :class="{'loading': loading}">
			{{ saveButtonText }}
		</view>
	</view>
</template>

<script>
	import Api from '../../common/api.js';
	
	export default {
		data() {
			return {
				oldPassword: '',
				newPassword: '',
				confirmPassword: '',
				loading: false,
				saveButtonText: '保存',
				passwordVisible: {
					old: false,
					new: false,
					confirm: false
				}
			}
		},
		methods: {
			// 返回个人主页
			goBack() {
				// 直接跳转到个人主页
				uni.navigateTo({
					url: '/pages/mine/profile'
				});
			},
			
			// 切换密码可见性
			togglePasswordVisibility(field) {
				this.passwordVisible[field] = !this.passwordVisible[field];
			},
			
			// 重置输入
			resetInputs() {
				this.oldPassword = '';
				this.newPassword = '';
				this.confirmPassword = '';
				
				// 显示提示
				uni.showToast({
					title: '已重置输入',
					icon: 'none',
					duration: 1500
				});
			},
			
			// 保存密码
			savePassword() {
				if (this.loading) return;
				
				// 校验密码
				if (!this.oldPassword) {
					uni.showToast({
						title: '请输入原密码',
						icon: 'none'
					});
					return;
				}
				
				if (!this.newPassword) {
					uni.showToast({
						title: '请输入新密码',
						icon: 'none'
					});
					return;
				}
				
				if (this.newPassword.length < 6 || this.newPassword.length > 20) {
					uni.showToast({
						title: '密码长度应为6-20位',
						icon: 'none'
					});
					return;
				}
				
				if (this.newPassword !== this.confirmPassword) {
					uni.showToast({
						title: '两次密码输入不一致',
						icon: 'none'
					});
					return;
				}
				
				this.loading = true;
				this.saveButtonText = '修改中...';
				
				Api.request({
					url: '/v1/user/password',
					method: 'PUT',
					data: {
						oldPassword: this.oldPassword,
						newPassword: this.newPassword
					}
				}).then(res => {
					this.loading = false;
					this.saveButtonText = '保存';
					
					if (res.data && res.data.code === 0) {
						uni.showToast({
							title: '密码修改成功',
							icon: 'success'
						});
						
						// 延迟跳转到个人主页
						setTimeout(() => {
							uni.navigateTo({
								url: '/pages/mine/profile'
							});
						}, 1500);
					} else {
						uni.showToast({
							title: res.data?.message || '密码修改失败',
							icon: 'none'
						});
					}
				}).catch(err => {
					this.loading = false;
					this.saveButtonText = '保存';
					console.error('密码修改失败:', err);
					uni.showToast({
						title: '密码修改失败',
						icon: 'none'
					});
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	@keyframes card-enter {
		from {
			opacity: 0;
			transform: translateY(20rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}
	
	.page {
		background-color: #f2f5fc;
		min-height: 100vh;
		padding-bottom: 180rpx;
	}
	
	.navbar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 90rpx;
		padding: 0 30rpx;
		background-color: #fff;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
		position: sticky;
		top: 0;
		z-index: 100;
		
		.navbar-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}
		
		.navbar-left, .navbar-right {
			width: 60rpx;
			display: flex;
			align-items: center;
		}
	}
	
	.content {
		padding: 30rpx;
	}
	
	.password-card {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 40rpx 30rpx;
		box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
		animation: card-enter 0.5s ease-out;
		
		.form-item {
			height: 100rpx;
			border-bottom: 1px solid #f0f0f0;
			margin-bottom: 20rpx;
			display: flex;
			align-items: center;
			position: relative;
			
			input {
				height: 100%;
				flex: 1;
				font-size: 32rpx;
				padding-right: 50rpx;
			}
			
			.icon-wrapper {
				position: absolute;
				right: 10rpx;
				height: 100%;
				width: 50rpx;
				display: flex;
				align-items: center;
				justify-content: center;
			}
		}
		
		.tips {
			margin-top: 30rpx;
			font-size: 26rpx;
			color: #999;
			line-height: 1.5;
		}
		
		.reset-button {
			margin-top: 40rpx;
			display: flex;
			align-items: center;
			justify-content: center;
			padding: 20rpx 0;
			border-radius: 8rpx;
			background-color: #f5f7fa;
			transition: background-color 0.3s;
			
			&:active {
				background-color: #e8edf5;
			}
			
			text {
				color: #3264ED;
				font-size: 28rpx;
				margin-left: 10rpx;
			}
		}
	}
	
	.save-button {
		position: fixed;
		bottom: 50rpx;
		left: 50rpx;
		right: 50rpx;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		background: linear-gradient(to right, #4a89dc, #3264ED);
		color: white;
		font-size: 32rpx;
		font-weight: bold;
		border-radius: 45rpx;
		box-shadow: 0 10rpx 20rpx rgba(50, 100, 237, 0.3);
		transition: all 0.3s ease;
		
		&.loading {
			opacity: 0.8;
			transform: scale(0.98);
		}
		
		&:active {
			transform: scale(0.95);
			box-shadow: 0 5rpx 15rpx rgba(50, 100, 237, 0.2);
		}
	}
</style> 