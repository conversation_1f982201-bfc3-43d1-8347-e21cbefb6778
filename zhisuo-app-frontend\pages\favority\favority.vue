<template>
	<view class="favorites-page">
		<!-- 导航栏 -->
		<view class="navbar">
			<view class="nav-left" @click="goBack">
				<uni-icons type="left" size="20" color="#333"></uni-icons>
			</view>
			<view class="nav-title">我的收藏</view>
			<view class="nav-right">
				<view class="edit-btn" :class="{ active: isEditMode }" @click="toggleEditMode">
					{{ isEditMode ? '完成' : '编辑' }}
				</view>
			</view>
		</view>

		<!-- 筛选栏 -->
		<view class="filter-bar">
			<view class="filter-tabs">
				<view 
					class="filter-tab" 
					:class="{ active: currentFilter === 'all' }" 
					@click="setFilter('all')"
				>
					全部
				</view>
				<view 
					class="filter-tab" 
					:class="{ active: currentFilter === 'article' }" 
					@click="setFilter('article')"
				>
					文章
				</view>
				<view 
					class="filter-tab" 
					:class="{ active: currentFilter === 'topic' }" 
					@click="setFilter('topic')"
				>
					话题
				</view>
			</view>
		</view>

		<!-- 收藏列表 -->
		<view class="favorites-list">
			<view 
				class="favorite-item" 
				v-for="(item, index) in favoritesList" 
				:key="item.favoriteId"
				@click="handleItemClick(item)"
			>
				<!-- 编辑模式下的选择框 -->
				<view class="select-box" v-if="isEditMode" @click.stop="toggleSelect(item)">
					<view class="checkbox-wrapper" :class="{ selected: item.selected }">
						<uni-icons
							:type="item.selected ? 'checkmarkempty' : 'circle'"
							:color="item.selected ? '#fff' : '#ddd'"
							size="16"
						></uni-icons>
					</view>
				</view>

				<!-- 内容图片 -->
				<view class="item-image">
					<image 
						:src="item.contentImage || '/static/logo.png'" 
						mode="aspectFill"
						@error="handleImageError"
					></image>
					<view class="image-overlay">
						<uni-icons type="eye-filled" size="14" color="#fff"></uni-icons>
						<text>{{ item.viewCount || 0 }}</text>
					</view>
				</view>

				<!-- 内容信息 -->
				<view class="item-content">
					<view class="item-title">{{ item.contentTitle || item.title }}</view>
					<view class="item-meta">
						<view class="content-type">{{ getContentTypeLabel(item.contentType) }}</view>
					</view>
				</view>
			</view>

			<!-- 空状态 -->
			<view class="empty-state" v-if="favoritesList.length === 0 && !isLoading">
				<view class="empty-icon">
					<uni-icons type="heart" size="80" color="#ddd"></uni-icons>
				</view>
				<text class="empty-text">暂无收藏内容</text>
				<text class="empty-tip">去发现更多精彩内容吧~</text>
			</view>

			<!-- 加载状态 -->
			<view class="loading-more" v-if="isLoading">
				<uni-icons type="spinner-cycle" size="20" color="#722ED1"></uni-icons>
				<text>加载中...</text>
			</view>

			<!-- 没有更多数据 -->
			<view class="no-more" v-if="!hasMore && favoritesList.length > 0">
				<text>没有更多内容了</text>
			</view>
		</view>

		<!-- 编辑模式底部操作栏 -->
		<view class="bottom-actions" v-if="isEditMode">
			<view class="select-all" @click="toggleSelectAll">
				<view class="checkbox-wrapper" :class="{ selected: isAllSelected }">
					<uni-icons
						:type="isAllSelected ? 'checkmarkempty' : 'circle'"
						:color="isAllSelected ? '#fff' : '#ddd'"
						size="16"
					></uni-icons>
				</view>
				<text>全选</text>
			</view>
			<view class="action-buttons">
				<view class="delete-btn" @click="deleteSelected" :class="{ disabled: selectedCount === 0 }">
					<uni-icons type="trash" size="16" color="#fff"></uni-icons>
					<text>删除{{ selectedCount > 0 ? `(${selectedCount})` : '' }}</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import Api from '../../common/api.js';

export default {
	data() {
		return {
			favoritesList: [],
			currentFilter: 'all', // all, article, topic
			isEditMode: false,
			isLoading: false,
			hasMore: true,
			currentPage: 0,
			pageSize: 20
		};
	},
	computed: {
		selectedCount() {
			return this.favoritesList.filter(item => item.selected).length;
		},
		isAllSelected() {
			return this.favoritesList.length > 0 && this.favoritesList.every(item => item.selected);
		}
	},
	onLoad() {
		this.loadFavorites(true);
	},
	onReachBottom() {
		if (this.hasMore && !this.isLoading) {
			this.loadFavorites(false);
		}
	},
	onPullDownRefresh() {
		this.loadFavorites(true);
		setTimeout(() => {
			uni.stopPullDownRefresh();
		}, 1000);
	},
	methods: {
		// 返回上一页
		goBack() {
			uni.navigateBack();
		},

		// 切换编辑模式
		toggleEditMode() {
			this.isEditMode = !this.isEditMode;
			if (!this.isEditMode) {
				// 退出编辑模式时清除选择状态
				this.favoritesList.forEach(item => {
					this.$set(item, 'selected', false);
				});
			}
		},

		// 设置筛选条件
		setFilter(filter) {
			if (this.currentFilter !== filter) {
				this.currentFilter = filter;
				this.loadFavorites(true);
			}
		},

		// 加载收藏列表
		loadFavorites(isRefresh = false) {
			if (isRefresh) {
				this.currentPage = 0;
				this.hasMore = true;
				this.favoritesList = [];
			}

			if (this.isLoading) return;
			this.isLoading = true;

			const params = {
				page: this.currentPage,
				size: this.pageSize
			};

			if (this.currentFilter !== 'all') {
				params.contentType = this.currentFilter;
			}

			Api.request({
				url: '/v1/favorites/my',
				method: 'GET',
				data: params
			}).then(res => {
				if (res.data && res.data.code === 0) {
					const data = res.data.data;
					const newItems = data.content || [];
					
					if (isRefresh) {
						this.favoritesList = newItems;
					} else {
						this.favoritesList = [...this.favoritesList, ...newItems];
					}

					// 为新项目添加选择状态
					newItems.forEach(item => {
						this.$set(item, 'selected', false);
					});

					this.hasMore = this.currentPage < data.totalPages - 1;
					this.currentPage++;
				}
			}).catch(err => {
				console.error('获取收藏列表失败:', err);
				uni.showToast({
					title: '加载失败',
					icon: 'none'
				});
			}).finally(() => {
				this.isLoading = false;
			});
		},

		// 处理项目点击
		handleItemClick(item) {
			if (this.isEditMode) {
				this.toggleSelect(item);
			} else {
				this.navigateToContent(item);
			}
		},

		// 切换选择状态
		toggleSelect(item) {
			this.$set(item, 'selected', !item.selected);
		},

		// 全选/取消全选
		toggleSelectAll() {
			const shouldSelectAll = !this.isAllSelected;
			this.favoritesList.forEach(item => {
				this.$set(item, 'selected', shouldSelectAll);
			});
		},

		// 删除选中的收藏
		deleteSelected() {
			if (this.selectedCount === 0) return;

			uni.showModal({
				title: '确认删除',
				content: `确定要删除选中的 ${this.selectedCount} 个收藏吗？`,
				success: (res) => {
					if (res.confirm) {
						const selectedIds = this.favoritesList
							.filter(item => item.selected)
							.map(item => item.favoriteId);

						this.deleteFavorites(selectedIds);
					}
				}
			});
		},

		// 批量删除收藏
		deleteFavorites(favoriteIds) {
			Api.request({
				url: '/v1/favorites/batch',
				method: 'DELETE',
				data: favoriteIds
			}).then(res => {
				if (res.data && res.data.code === 0) {
					uni.showToast({
						title: '删除成功',
						icon: 'success'
					});
					
					// 从列表中移除已删除的项目
					this.favoritesList = this.favoritesList.filter(
						item => !favoriteIds.includes(item.favoriteId)
					);
					
					// 如果当前页没有数据了，尝试加载更多
					if (this.favoritesList.length === 0 && this.hasMore) {
						this.loadFavorites(true);
					}
				}
			}).catch(err => {
				console.error('删除收藏失败:', err);
				uni.showToast({
					title: '删除失败',
					icon: 'none'
				});
			});
		},

		// 跳转到内容详情
		navigateToContent(item) {
			if (!item || !item.contentId || !item.contentType) {
				console.error('内容信息不完整:', item);
				return;
			}

			let url = '';
			if (item.contentType === 'article') {
				url = `/pages/article/detail?id=${item.contentId}`;
			} else if (item.contentType === 'topic') {
				url = `/pages/discover/hotDetail?id=${item.contentId}`;
			} else {
				console.error('未知的内容类型:', item.contentType);
				return;
			}

			uni.navigateTo({
				url: url
			});
		},

		// 获取内容类型标签
		getContentTypeLabel(contentType) {
			const labels = {
				'article': '文章',
				'topic': '话题'
			};
			return labels[contentType] || '未知';
		},

		// 格式化时间
		formatTime(timeStr) {
			if (!timeStr) return '';
			
			const time = new Date(timeStr);
			const now = new Date();
			const diff = now - time;
			
			const minute = 60 * 1000;
			const hour = 60 * minute;
			const day = 24 * hour;
			
			if (diff < minute) {
				return '刚刚';
			} else if (diff < hour) {
				return Math.floor(diff / minute) + '分钟前';
			} else if (diff < day) {
				return Math.floor(diff / hour) + '小时前';
			} else if (diff < 7 * day) {
				return Math.floor(diff / day) + '天前';
			} else {
				return time.toLocaleDateString();
			}
		},

		// 处理图片加载错误
		handleImageError(e) {
			e.target.src = '/static/logo.png';
		}
	}
};
</script>

<style lang="scss" scoped>
.favorites-page {
	background-color: #f8f9fa;
	min-height: 100vh;
}

/* 导航栏样式 */
.navbar {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 20rpx 30rpx;
	background-color: #fff;
	border-bottom: 1rpx solid #eee;
	position: sticky;
	top: 0;
	z-index: 100;

	.nav-left {
		width: 80rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: flex-start;
	}

	.nav-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
	}

	.nav-right {
		width: 80rpx;
		height: 60rpx;
		display: flex;
		align-items: center;
		justify-content: flex-end;

		.edit-btn {
			font-size: 28rpx;
			color: #722ED1;
			padding: 10rpx 20rpx;
			border-radius: 20rpx;
			transition: all 0.3s ease;
			white-space: nowrap;
			writing-mode: horizontal-tb;
			text-orientation: mixed;

			&.active {
				background-color: #722ED1;
				color: #fff;
			}
		}
	}
}

/* 筛选栏样式 */
.filter-bar {
	background-color: #fff;
	padding: 20rpx 30rpx;
	border-bottom: 1rpx solid #eee;

	.filter-tabs {
		display: flex;
		gap: 40rpx;

		.filter-tab {
			font-size: 28rpx;
			color: #666;
			padding: 12rpx 24rpx;
			position: relative;
			transition: all 0.3s ease;
			border-radius: 20rpx;
			cursor: pointer;

			&.active {
				color: #722ED1;
				font-weight: bold;
				background-color: rgba(114, 46, 209, 0.1);
				transform: translateY(-2rpx);

				&::after {
					content: '';
					position: absolute;
					bottom: -20rpx;
					left: 50%;
					transform: translateX(-50%);
					width: 40rpx;
					height: 4rpx;
					background-color: #722ED1;
					border-radius: 2rpx;
				}
			}

			&:hover {
				background-color: rgba(114, 46, 209, 0.05);
			}
		}
	}
}

/* 收藏列表样式 */
.favorites-list {
	padding: 20rpx 30rpx;
	padding-bottom: 120rpx; /* 为底部操作栏留出空间 */
	transition: padding-bottom 0.3s ease;
}

.favorite-item {
	display: flex;
	align-items: flex-start;
	background-color: #fff;
	border-radius: 16rpx;
	padding: 24rpx;
	margin-bottom: 20rpx;
	box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.98);
	}

	.select-box {
		margin-right: 20rpx;
		padding: 10rpx;
		display: flex;
		align-items: center;
		justify-content: center;

		.checkbox-wrapper {
			width: 40rpx;
			height: 40rpx;
			border-radius: 50%;
			border: 2rpx solid #ddd;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;

			&.selected {
				background-color: #722ED1;
				border-color: #722ED1;
				transform: scale(1.1);
			}
		}
	}

	.item-image {
		position: relative;
		width: 160rpx;
		height: 120rpx;
		border-radius: 12rpx;
		overflow: hidden;
		flex-shrink: 0;
		margin-right: 24rpx;

		image {
			width: 100%;
			height: 100%;
		}

		.image-overlay {
			position: absolute;
			bottom: 8rpx;
			right: 8rpx;
			background: rgba(0, 0, 0, 0.6);
			border-radius: 12rpx;
			padding: 4rpx 8rpx;
			display: flex;
			align-items: center;
			gap: 4rpx;

			text {
				font-size: 20rpx;
				color: #fff;
			}
		}
	}

	.item-content {
		flex: 1;
		display: flex;
		flex-direction: column;
		justify-content: space-between;
		min-height: 120rpx;

		.item-title {
			font-size: 30rpx;
			font-weight: 600;
			color: #333;
			line-height: 1.4;
			margin-bottom: 12rpx;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			line-clamp: 2;
			overflow: hidden;
		}

		.item-description {
			font-size: 24rpx;
			color: #666;
			line-height: 1.4;
			margin-bottom: 12rpx;
			display: -webkit-box;
			-webkit-box-orient: vertical;
			-webkit-line-clamp: 2;
			line-clamp: 2;
			overflow: hidden;
		}

		.item-meta {
			display: flex;
			align-items: center;
			justify-content: space-between;
			margin-top: auto;

			.content-type {
				font-size: 22rpx;
				color: #722ED1;
				background: rgba(114, 46, 209, 0.1);
				padding: 4rpx 12rpx;
				border-radius: 12rpx;
			}

			.favorite-time {
				font-size: 22rpx;
				color: #999;
			}
		}
	}
}

/* 空状态样式 */
.empty-state {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	text-align: center;

	.empty-icon {
		margin-bottom: 40rpx;
		opacity: 0.6;
		animation: pulse 2s infinite;
	}

	.empty-text {
		font-size: 32rpx;
		color: #666;
		margin-bottom: 16rpx;
		font-weight: 500;
	}

	.empty-tip {
		font-size: 26rpx;
		color: #999;
	}
}

@keyframes pulse {
	0%, 100% {
		opacity: 0.6;
		transform: scale(1);
	}
	50% {
		opacity: 0.8;
		transform: scale(1.05);
	}
}

/* 加载状态样式 */
.loading-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;
	gap: 16rpx;

	text {
		font-size: 26rpx;
		color: #722ED1;
	}
}

.no-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 40rpx;

	text {
		font-size: 26rpx;
		color: #999;
	}
}

/* 底部操作栏样式 */
.bottom-actions {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background-color: #fff;
	border-top: 1rpx solid #eee;
	padding: 20rpx 30rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	z-index: 100;
	box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);

	.select-all {
		display: flex;
		align-items: center;
		gap: 16rpx;

		.checkbox-wrapper {
			width: 40rpx;
			height: 40rpx;
			border-radius: 50%;
			border: 2rpx solid #ddd;
			display: flex;
			align-items: center;
			justify-content: center;
			transition: all 0.3s ease;

			&.selected {
				background-color: #722ED1;
				border-color: #722ED1;
				transform: scale(1.1);
			}
		}

		text {
			font-size: 28rpx;
			color: #333;
		}
	}

	.action-buttons {
		display: flex;
		gap: 20rpx;

		.delete-btn {
			background-color: #ff4d4f;
			color: #fff;
			padding: 16rpx 24rpx;
			border-radius: 24rpx;
			font-size: 28rpx;
			transition: all 0.3s ease;
			display: flex;
			align-items: center;
			gap: 8rpx;
			min-width: 120rpx;
			justify-content: center;

			&.disabled {
				opacity: 0.5;
				pointer-events: none;
			}

			&:active {
				transform: scale(0.95);
			}
		}
	}
}
</style>
