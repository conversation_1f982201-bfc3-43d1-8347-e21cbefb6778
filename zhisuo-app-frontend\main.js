import App from './App'

// #ifndef VUE3
import Vue from 'vue'
import './uni.promisify.adaptor'
Vue.config.productionTip = false

// 添加全局事件监听器配置，修复passive警告
if (typeof window !== 'undefined') {
  // 添加 passive 支持检测
  let supportsPassive = false;
  try {
    const opts = Object.defineProperty({}, 'passive', {
      get: function() {
        supportsPassive = true;
        return true;
      }
    });
    window.addEventListener('test', null, opts);
    window.removeEventListener('test', null, opts);
  } catch (e) {}

  // 重写 addEventListener 方法，更全面地处理 passive 事件
  if (supportsPassive) {
    const originalAddEventListener = EventTarget.prototype.addEventListener;
    EventTarget.prototype.addEventListener = function(type, listener, options) {
      let optionsOrCapture = options;

      // 需要设置 passive 的事件类型
      const passiveEvents = ['touchstart', 'touchmove', 'wheel', 'mousewheel'];

      if (passiveEvents.includes(type)) {
        if (options === undefined || options === false || options === true) {
          // 对于简单的 boolean 或 undefined 参数，创建新的 options 对象
          optionsOrCapture = {
            passive: type !== 'touchmove', // touchmove 可能需要 preventDefault，所以不设为 passive
            capture: options === true
          };
        } else if (typeof options === 'object' && options !== null) {
          // 对于对象参数，如果没有明确设置 passive，则自动设置
          if (options.passive === undefined) {
            options.passive = type !== 'touchmove';
          }
        }
      }

      return originalAddEventListener.call(this, type, listener, optionsOrCapture);
    };
  }
}

App.mpType = 'app'
const app = new Vue({
  ...App
})
app.$mount()
// #endif

// #ifdef VUE3
import { createSSRApp } from 'vue'
export function createApp() {
  const app = createSSRApp(App)
  return {
    app
  }
}
// #endif