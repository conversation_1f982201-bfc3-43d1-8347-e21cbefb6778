<template>
	<view class="agreement-popup" v-if="show">
		<view class="mask" @click="cancel"></view>
		<view class="popup-content" :style="popupStyle">
			<view class="popup-header">
				<text class="title">{{ title }}</text>
				<view class="close-btn" @click="cancel">
					<uni-icons type="close" size="20" color="#999"></uni-icons>
				</view>
			</view>
			
			<scroll-view 
				class="popup-body" 
				scroll-y 
				@scrolltolower="handleScrollToLower" 
				:scroll-top="scrollTop" 
				:show-scrollbar="true"
				:style="{ '-webkit-overflow-scrolling': 'touch' }"
			>
				<view class="content">
					<view class="agreement-title">{{ title }}</view>
					<view class="agreement-text">{{ content }}</view>
				</view>
				<!-- 添加底部占位，确保能滚动到底部 -->
				<view class="scroll-bottom-space"></view>
			</scroll-view>
			
			<view class="popup-footer">
				<view class="scroll-tip" v-if="requireScroll && !reachedBottom">
					<uni-icons type="bottom" size="14" color="#999"></uni-icons>
					<text>请滑动至底部</text>
				</view>
				<button class="btn-confirm" :disabled="!canConfirm" :class="{'btn-active': canConfirm}" @click="confirm">{{ confirmText }}</button>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'AgreementPopup',
		props: {
			show: {
				type: Boolean,
				default: false
			},
			title: {
				type: String,
				default: '协议'
			},
			content: {
				type: String,
				default: ''
			},
			confirmText: {
				type: String,
				default: '同意'
			},
			// 弹窗宽度，可以是百分比或具体数值
			width: {
				type: String,
				default: '85%'
			},
			// 弹窗高度，可以是百分比或具体数值
			height: {
				type: String,
				default: '70%'
			},
			// 是否需要滚动到底部才能点击确认按钮
			requireScroll: {
				type: Boolean,
				default: true
			}
		},
		data() {
			return {
				reachedBottom: false,
				scrollTop: 0
			}
		},
		computed: {
			// 计算弹窗样式
			popupStyle() {
				return {
					width: this.width,
					height: this.height,
					left: `calc((100% - ${this.width}) / 2)`,
					top: `calc((100% - ${this.height}) / 2)`
				}
			},
			// 格式化内容，处理换行符
			formattedContent() {
				return this.content.replace(/\n\n/g, '<div class="paragraph-break"></div>')
					.replace(/\n/g, '<br>');
			},
			// 是否可以点击确认按钮
			canConfirm() {
				return !this.requireScroll || this.reachedBottom;
			}
		},
		watch: {
			show(newVal) {
				if (newVal) {
					this.reachedBottom = false;
					this.scrollTop = 0;
					// 如果不需要滚动到底部，直接设置为已到底部
					if (!this.requireScroll) {
						this.reachedBottom = true;
					} else {
						// 在弹窗显示后，添加延时检查内容高度
						setTimeout(() => {
							this.checkContentHeight();
						}, 300);
					}
				}
			}
		},
		methods: {
			// 检查内容高度，如果内容高度小于容器高度，直接允许点击
			checkContentHeight() {
				if (!this.requireScroll) {
					this.reachedBottom = true;
					return;
				}
				
				const query = uni.createSelectorQuery().in(this);
				query.select('.popup-body').boundingClientRect();
				query.select('.content').boundingClientRect();
				query.exec(res => {
					if (res[0] && res[1]) {
						const containerHeight = res[0].height;
						const contentHeight = res[1].height;
						// 如果内容高度小于或等于容器高度，直接设置为已到底部
						if (contentHeight <= containerHeight) {
							this.reachedBottom = true;
						}
					}
				});
			},
			// 使用scrolltolower事件替代scroll事件
			handleScrollToLower() {
				this.reachedBottom = true;
			},
			confirm() {
				if (this.canConfirm) {
					this.$emit('confirm');
				}
			},
			cancel() {
				this.$emit('cancel');
			}
		}
	}
</script>

<style lang="scss" scoped>
	.agreement-popup {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		z-index: 999;
		
		.mask {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background-color: rgba(0, 0, 0, 0.5);
		}
		
		.popup-content {
			position: absolute;
			background-color: #fff;
			border-radius: 24rpx;
			display: flex;
			flex-direction: column;
			overflow: hidden; /* 确保内容不会溢出 */
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
			
			.popup-header {
				padding: 36rpx 40rpx;
				border-bottom: 1rpx solid #f0f0f0;
				display: flex;
				justify-content: space-between;
				align-items: center;
				background-color: #fff;
				
				.title {
					font-size: 36rpx;
					font-weight: 600;
					color: #333;
				}
				
				.close-btn {
					padding: 10rpx;
					display: flex;
					align-items: center;
					justify-content: center;
				}
			}
			
			.popup-body {
				flex: 1;
				overflow-y: auto; /* 确保可以滚动 */
				-webkit-overflow-scrolling: touch; /* 增强iOS滚动体验 */
				padding: 0;
				
				/* 自定义滚动条样式 */
				&::-webkit-scrollbar {
					width: 8rpx;
					background-color: #f5f5f5;
				}
				
				&::-webkit-scrollbar-thumb {
					border-radius: 4rpx;
					background-color: #ddd;
				}
				
				.content {
					padding: 40rpx;
					
					.agreement-title {
						font-size: 32rpx;
						font-weight: 600;
						color: #333;
						margin-bottom: 30rpx;
						text-align: center;
					}
					
					.agreement-text {
						font-size: 28rpx;
						color: #333;
						line-height: 1.8;
						white-space: pre-wrap;
						word-break: break-all; /* 确保长文本会换行 */
						text-align: justify; /* 文本两端对齐 */
						
						/* 段落样式 */
						.paragraph-break {
							height: 20rpx;
						}
					}
				}
				
				.scroll-bottom-space {
					height: 50rpx; /* 添加底部空间确保可以滚动到底部 */
				}
			}
			
			.popup-footer {
				padding: 30rpx 40rpx 40rpx;
				border-top: 1rpx solid #f0f0f0;
				background-color: #fff;
				
				.scroll-tip {
					display: flex;
					align-items: center;
					justify-content: center;
					margin-bottom: 24rpx;
					
					text {
						font-size: 24rpx;
						color: #999;
						margin-left: 6rpx;
					}
				}
				
				.btn-confirm {
					height: 88rpx;
					line-height: 88rpx;
					text-align: center;
					border-radius: 44rpx;
					font-size: 32rpx;
					background-color: #ccc;
					color: #fff;
					border: none;
					
					&.btn-active {
						background-color: #3264ed;
					}
					
					&::after {
						border: none;
					}
				}
			}
		}
	}
</style> 