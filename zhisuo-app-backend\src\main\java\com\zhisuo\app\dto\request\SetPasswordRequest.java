package com.zhisuo.app.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;

/**
 * 密码设置请求DTO
 */
@Data
public class SetPasswordRequest {
    
    /**
     * 用户ID
     */
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    
    /**
     * 密码，6-20位
     */
    @Size(min = 6, max = 20, message = "密码长度应为6-20位")
    private String password;
    
    /**
     * 设备ID
     */
    private String deviceId;
    
    /**
     * 设备类型
     */
    private String deviceType;
} 