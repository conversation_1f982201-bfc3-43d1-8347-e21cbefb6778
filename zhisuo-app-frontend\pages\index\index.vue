<template>
	<view class="page">
		<!-- 顶部导航栏 -->
		<view class="navbar">
			<view class="logo-container">
				<view class="logo">
					<image src="/static/logo.png" mode="aspectFill"></image>
				</view>
				<text class="app-name">智索</text>
			</view>
			<view class="avatar" @click="navigateToMine">
				<image :src="userAvatar" mode="aspectFill"></image>
			</view>
		</view>

		<!-- 系统通知卡片轮播 -->
		<swiper class="notice-swiper" circular autoplay interval="4000" duration="800" indicator-dots indicator-active-color="#722ED1" easing-function="easeInOutCubic">
			<!-- 通知卡片1 -->
			<swiper-item class="swiper-item">
				<view class="notice-card">
					<view class="notice-content">
						<view class="notice-tag">重要系统更新通知</view>
						<view class="notice-title">新版本将于本周发布，带来全新体验</view>
						<view class="notice-time">
							<uni-icons type="calendar" size="14" color="#999"></uni-icons>
							<text>2024-01-20</text>
						</view>
					</view>
				</view>
			</swiper-item>
			
			<!-- 通知卡片2 -->
			<swiper-item class="swiper-item">
				<view class="notice-card">
					<view class="notice-content">
						<view class="notice-tag">活动预告</view>
						<view class="notice-title">智索用户体验大会即将举行，敬请期待</view>
						<view class="notice-time">
							<uni-icons type="calendar" size="14" color="#999"></uni-icons>
							<text>2024-01-25</text>
						</view>
					</view>
				</view>
			</swiper-item>
			
			<!-- 通知卡片3 -->
			<swiper-item class="swiper-item">
				<view class="notice-card">
					<view class="notice-content">
						<view class="notice-tag">功能上线通知</view>
						<view class="notice-title">AI智能分析功能已上线，立即体验</view>
						<view class="notice-time">
							<uni-icons type="calendar" size="14" color="#999"></uni-icons>
							<text>2024-01-18</text>
						</view>
					</view>
				</view>
			</swiper-item>
		</swiper>

		<!-- 推荐内容区域 -->
		<view class="recommend">
			<view class="section-header">
				<text>为您推荐</text>
				<view class="more" @click="navigateToArticleList">
					查看更多
					<uni-icons type="right" size="14" color="#722ED1"></uni-icons>
				</view>
			</view>

			<!-- 推荐文章列表 -->
			<view class="article-list">
				<!-- 加载状态 -->
				<view v-if="articlesLoading" class="loading-container">
					<view class="loading-text">正在加载推荐文章...</view>
				</view>

				<!-- 错误状态 -->
				<view v-else-if="articlesError" class="error-container">
					<view class="error-text">{{ articlesError }}</view>
					<view class="retry-btn" @click="getRecommendedArticles">重试</view>
				</view>

				<!-- 文章列表 -->
				<view v-else>
					<!-- 文章项 -->
					<view
						v-for="(article, index) in articles.slice(0, 3)"
						:key="article.articleId || index"
						class="article-item"
						@click="onArticleClick(article)"
					>
						<view class="article-image">
							<image
								:src="article.coverImage || '/static/logo.png'"
								mode="aspectFill"
								:lazy-load="true"
							></image>
						</view>
						<view class="article-info">
							<view class="article-title">{{ article.title || '标题加载中...' }}</view>
							<view class="article-desc">{{ article.description || '描述加载中...' }}</view>
							<view class="article-meta">
								<view class="meta-left">
									<view class="views">
										<uni-icons type="eye" size="14" color="#999"></uni-icons>
										<text>{{ formatViewCount(article.viewCount) }}</text>
									</view>
									<view class="comments">
										<uni-icons type="chat" size="14" color="#999"></uni-icons>
										<text>{{ formatViewCount(article.commentCount) }}</text>
									</view>
								</view>
								<view class="time-icon-group">
									<view v-if="article.iconUrl" class="article-icon">
										<image :src="article.iconUrl" mode="aspectFit"></image>
									</view>
									<view class="time">
										<uni-icons type="clock" size="14" color="#999"></uni-icons>
										<text>{{ formatTimeAgo(article.publishTime) }}</text>
									</view>
								</view>
							</view>
						</view>
					</view>

					<!-- 空状态 -->
					<view v-if="!articles || articles.length === 0" class="empty-container">
						<view class="empty-text">暂无推荐文章</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 热点趋势分析 -->
		<view class="trend">
			<view class="section-header">
				<view class="header-left">
					<text>热点趋势分析</text>
				</view>
				<view class="trend-tabs">
					<text class="tab" :class="{ active: trendPeriod === 'day' }" @click="changeTrendPeriod('day')">日</text>
					<text class="tab" :class="{ active: trendPeriod === 'week' }" @click="changeTrendPeriod('week')">周</text>
					<text class="tab" :class="{ active: trendPeriod === 'month' }" @click="changeTrendPeriod('month')">月</text>
				</view>
			</view>
			<view class="trend-chart">
				<view class="chart-container">
					<!-- 自定义折线图 -->
					<view class="line-chart">
						<!-- Y轴刻度 -->
						<view class="y-axis">
							<text v-for="(value, index) in yAxisLabels" :key="index" class="y-label" :style="{'--index': index}">{{value}}</text>
						</view>
						
						<!-- 图表主体 -->
						<view class="chart-body">
							<!-- 网格线 -->
							<view class="grid-lines">
								<view v-for="i in 5" :key="i" class="h-grid-line"></view>
							</view>
							
							<!-- 数据点 -->
							<view v-for="(point, index) in trendData.chartData" :key="`point-${trendPeriod}-${index}`" 
								class="data-point" 
								:class="{ active: index === activePointIndex }"
								:style="{ 
									left: `${index * (100 / (trendData.chartData.length - 1))}%`, 
									bottom: `${(point.value / maxValue) * 100}%`,
									'animation-delay': `${index * 0.1}s`
								}"
								@click="highlightPoint(index)">
								<view class="point-value" :class="{ 'show': index === activePointIndex }">
									{{point.value}}
								</view>
							</view>
							
							<!-- 连线和阴影 -->
							<view :key="`line-${trendPeriod}`" class="data-line" :style="{ 'clip-path': getClipPath(), 'animation-delay': `${trendData.chartData.length * 0.1 + 0.1}s` }">
								<view class="line-gradient"></view>
							</view>
						</view>
						
						<!-- X轴刻度 -->
						<view class="x-axis">
							<text v-for="(point, index) in trendData.chartData" :key="`label-${trendPeriod}-${index}`" 
								class="x-label" 
								:style="{ left: `${index * (100 / (trendData.chartData.length - 1))}%` }">
								{{point.label}}
							</text>
						</view>
					</view>
				</view>
				<view class="chart-info">
					<view class="chart-title">{{ trendData.title }}</view>
					<view class="chart-desc">
						{{ trendData.description }}
					</view>
					<view class="chart-stats">
						<view class="stat-item">
							<text class="stat-value">{{ trendData.growth }}</text>
							<text class="stat-label">环比增长</text>
						</view>
						<view class="stat-item">
							<text class="stat-value">{{ trendData.followers }}</text>
							<text class="stat-label">关注人数</text>
						</view>
						<view class="stat-item">
							<text class="stat-value">{{ trendData.rating }}</text>
							<text class="stat-label">平均评分</text>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- AI智能助手 -->
	<AIAssistant :show-analysis="false" />
</template>

<script>
	import Api from '../../common/api.js';
	import AIAssistant from '../../components/AIAssistant.vue';

	export default {
		components: {
			AIAssistant
		},
		data() {
			return {
				userInfo: null,
				// 文章相关数据
				articles: [], // 推荐文章列表
				articlesLoading: false, // 文章加载状态
				articlesError: null, // 文章加载错误信息
				trendPeriod: 'day', // 默认显示日数据
				activePointIndex: -1, // 当前激活的点索引，-1表示没有激活点
				chartAnimating: false, // 图表是否正在动画
				trendDataMap: {
					day: {
						title: 'AI技术应用领域日增长趋势',
						description: '基于用户行为分析，当前最受关注的是AI技术应用领域，日环比增长85%',
						growth: '85%',
						followers: '12.5k',
						rating: '4.8',
						chartData: [
							{ label: '00:00', value: 42 },
							{ label: '04:00', value: 35 },
							{ label: '08:00', value: 60 },
							{ label: '12:00', value: 78 },
							{ label: '16:00', value: 92 },
							{ label: '20:00', value: 85 },
							{ label: '24:00', value: 70 }
						]
					},
					week: {
						title: '区块链技术周趋势分析',
						description: '本周区块链技术关注度大幅上升，成为热门话题，周环比增长65%',
						growth: '65%',
						followers: '28.3k',
						rating: '4.5',
						chartData: [
							{ label: '周一', value: 45 },
							{ label: '周二', value: 52 },
							{ label: '周三', value: 49 },
							{ label: '周四', value: 65 },
							{ label: '周五', value: 80 },
							{ label: '周六', value: 75 },
							{ label: '周日', value: 68 }
						]
					},
					month: {
						title: '新能源技术月度分析报告',
						description: '本月新能源技术持续升温，相关企业股价普遍上涨，月环比增长120%',
						growth: '120%',
						followers: '45.7k',
						rating: '4.9',
						chartData: [
							{ label: '第1周', value: 30 },
							{ label: '第2周', value: 45 },
							{ label: '第3周', value: 75 },
							{ label: '第4周', value: 95 }
						]
					}
				}
			}
		},
		onLoad() {
			// 检查登录状态
			const token = uni.getStorageSync('token');
			if (!token) {
				// 未登录，跳转到登录页
				uni.redirectTo({
					url: '/pages/login/login'
				});
			} else {
				// 获取用户信息
				this.userInfo = uni.getStorageSync('userInfo');
				//console.log('用户信息:', this.userInfo);

				// 获取推荐文章列表
				this.getRecommendedArticles();
			}
		},
		computed: {
			// 获取用户头像
			userAvatar() {
				return this.userInfo?.avatar || '/static/logo.png';
			},
			
			// 获取当前选择周期的趋势数据
			trendData() {
				return this.trendDataMap[this.trendPeriod] || this.trendDataMap.day;
			},
			
			// 计算Y轴最大值（数据中的最大值向上取整到最近的10的倍数）
			maxValue() {
				const data = this.trendData.chartData || [];
				if (data.length === 0) return 100;
				
				const maxDataValue = Math.max(...data.map(item => item.value));
				return Math.ceil(maxDataValue / 10) * 10;
			},
			
			// 生成Y轴刻度标签
			yAxisLabels() {
				const max = this.maxValue;
				const step = max / 5;
				return [max, max - step, max - 2 * step, max - 3 * step, max - 4 * step, 0].map(val => Math.round(val));
			}
		},
		methods: {
			navigateToMine() {
				uni.switchTab({
					url: '/pages/mine/mine'
				});
			},

			// 跳转到文章列表页面
			navigateToArticleList() {
				uni.navigateTo({
					url: '/pages/article/list'
				});
			},

			// 获取推荐文章列表
			async getRecommendedArticles() {
				this.articlesLoading = true;
				this.articlesError = null;

				try {
					const response = await Api.request({
						url: '/v1/articles',
						method: 'GET',
						data: {
							page: 0,
							size: 3 // 首页只显示3篇推荐文章
						}
					});

					if (response.data && response.data.code === 0 && response.data.data) {
						this.articles = response.data.data.content || [];
						//console.log('获取推荐文章成功:', this.articles);
					} else {
						throw new Error(response.data?.message || '获取文章失败');
					}
				} catch (error) {
					console.error('获取推荐文章失败:', error);
					this.articlesError = error.message || '网络错误，请稍后重试';
					// 如果API调用失败，使用默认的静态数据作为备用
					this.articles = this.getDefaultArticles();
				} finally {
					this.articlesLoading = false;
				}
			},

			// 获取默认文章数据（作为备用）
			getDefaultArticles() {
				return [
					{
						id: 'default-1',
						articleId: 'default-1',
						title: '2024年AI技术发展趋势分析',
						description: '深度解析AI技术在各行业的应用前景',
						coverImage: '/static/logo.png',
						iconUrl: '/static/logo.png',
						source: '智索科技',
						sourceUrl: 'https://www.baidu.com',
						viewCount: 2300,
						commentCount: 23,
						publishTime: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString() // 2小时前
					},
					{
						id: 'default-2',
						articleId: 'default-2',
						title: '数字化转型：企业如何应对',
						description: '企业数字化转型的策略与实践',
						coverImage: '/static/logo.png',
						iconUrl: '/static/logo.png',
						source: '科技日报',
						sourceUrl: 'https://www.google.com',
						viewCount: 1800,
						commentCount: 15,
						publishTime: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString() // 3小时前
					},
					{
						id: 'default-3',
						articleId: 'default-3',
						title: '智能算法在日常生活中的应用',
						description: 'AI如何改变我们的生活方式',
						coverImage: '/static/logo.png',
						iconUrl: '/static/logo.png',
						source: 'TechCrunch',
						sourceUrl: 'https://techcrunch.com',
						viewCount: 1500,
						commentCount: 8,
						publishTime: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString() // 4小时前
					}
				];
			},

			// 格式化时间显示
			formatTimeAgo(timeStr) {
				if (!timeStr) return '刚刚';

				const now = new Date();
				const time = new Date(timeStr);
				const diff = now - time;

				const minutes = Math.floor(diff / (1000 * 60));
				const hours = Math.floor(diff / (1000 * 60 * 60));
				const days = Math.floor(diff / (1000 * 60 * 60 * 24));

				if (minutes < 60) {
					return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
				} else if (hours < 24) {
					return `${hours}小时前`;
				} else if (days < 7) {
					return `${days}天前`;
				} else {
					return time.toLocaleDateString();
				}
			},

			// 格式化浏览量显示
			formatViewCount(count) {
				if (!count || count === 0) return '0';

				if (count >= 10000) {
					return (count / 10000).toFixed(1) + 'w';
				} else if (count >= 1000) {
					return (count / 1000).toFixed(1) + 'k';
				} else {
					return count.toString();
				}
			},

			// 点击文章项
			onArticleClick(article) {
				if (!article || !article.articleId) {
					console.warn('文章数据无效:', article);
					return;
				}

				// 跳转到文章详情页
				uni.navigateTo({
					url: `/pages/article/detail?id=${article.articleId}`
				});
			},
			
			// 切换趋势分析周期
			changeTrendPeriod(period) {
				if (this.trendPeriod === period) return;
				
				this.trendPeriod = period;
				// 重置激活点
				this.activePointIndex = -1;
				
				// 重置动画状态
				this.resetChartAnimation();
			},
			
			// 重置图表动画
			resetChartAnimation() {
				// 获取所有数据点和线条元素
				const chartElements = document.querySelectorAll('.data-point, .data-line');
				
				// 重置动画
				chartElements.forEach(el => {
					el.style.animation = 'none';
					// 触发重排
					void el.offsetWidth;
					// 重新应用动画
					el.style.animation = '';
				});
			},
			
			// 高亮显示数据点
			highlightPoint(index) {
				this.activePointIndex = index;
			},
			
			// 生成折线图的clip-path
			getClipPath() {
				const data = this.trendData.chartData || [];
				if (data.length < 2) return 'none';
				
				const points = data.map((point, index) => {
					const x = index * (100 / (data.length - 1));
					const y = 100 - (point.value / this.maxValue * 100);
					return `${x}% ${y}%`;
				});
				
				// 添加底部点，形成闭合区域
				const lastIndex = data.length - 1;
				const lastX = lastIndex * (100 / (data.length - 1));
				
				return `polygon(0% 100%, ${points.join(', ')}, ${lastX}% 100%)`;
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		background-color: #f8f9fc;
		min-height: 100vh;
		padding-bottom: 120rpx;
		font-family: "Helvetica Neue", Arial, sans-serif;
	}

	.navbar {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 20rpx 30rpx;
		background-color: #fff;
		height: 100rpx;
		position: sticky;
		top: 0;
		z-index: 100;
		box-shadow: 0 2rpx 15rpx rgba(114, 46, 209, 0.08);
		
		.logo-container {
			display: flex;
			align-items: center;
			
			.app-name {
				font-size: 36rpx;
				font-weight: bold;
				margin-left: 15rpx;
				background: linear-gradient(135deg, #722ED1, #9254DE);
				-webkit-background-clip: text;
				background-clip: text;
				color: transparent;
				font-family: "Helvetica Neue", Arial, sans-serif;
				letter-spacing: 2rpx;
				text-shadow: 0 2rpx 10rpx rgba(114, 46, 209, 0.3);
				position: relative;
				
				&::after {
					content: '';
					position: absolute;
					bottom: -2rpx;
					left: 0;
					width: 100%;
					height: 2rpx;
					background: linear-gradient(to right, transparent, #722ED1, transparent);
				}
			}
		}

		.logo {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			overflow: hidden;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);

			image {
				width: 100%;
				height: 100%;
			}
		}

		.avatar {
			width: 80rpx;
			height: 80rpx;
			border-radius: 50%;
			overflow: hidden;
			box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
			transition: all 0.3s ease;
			
			&:active {
				transform: scale(0.95);
			}

			image {
				width: 100%;
				height: 100%;
			}
		}
	}

	.notice-swiper {
		margin: 30rpx;
		height: 240rpx;
		border-radius: 20rpx;
		overflow: hidden;
		width: calc(100% - 60rpx);
		box-shadow: 0 5rpx 25rpx rgba(114, 46, 209, 0.1);
		
		.swiper-item {
			width: 100% !important;
			height: 100%;
			display: flex;
			justify-content: center;
			align-items: center;
			box-sizing: border-box;
		}
		
		.wx-swiper-dot {
			width: 16rpx;
			height: 6rpx;
			border-radius: 3rpx;
			margin: 0 8rpx;
			transition: all 0.3s ease;
		}
		
		.wx-swiper-dot-active {
			width: 30rpx;
		}
	}

	.notice-card {
		border-radius: 20rpx;
		overflow: hidden;
		background: linear-gradient(135deg, #722ED1, #9254DE);
		background-size: cover;
		background-position: center;
		height: 240rpx;
		width: 100%;
		position: relative;
		display: flex;
		align-items: center;
		transition: all 0.3s ease;
		box-shadow: 0 5rpx 15rpx rgba(114, 46, 209, 0.15);
		
		&:active {
			opacity: 0.9;
			transform: translateY(2rpx);
		}

		&::after {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			background: rgba(0, 0, 0, 0.3);
			z-index: 1;
		}

		.notice-content {
			position: relative;
			z-index: 2;
			padding: 30rpx;
			color: #fff;

			.notice-tag {
				font-size: 24rpx;
				font-weight: bold;
				margin-bottom: 20rpx;
			}

			.notice-title {
				font-size: 32rpx;
				font-weight: bold;
				margin-bottom: 20rpx;
			}

			.notice-time {
				font-size: 24rpx;
				display: flex;
				align-items: center;

				text {
					margin-left: 6rpx;
				}
			}
		}
	}

	.section-header {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx;
		font-size: 32rpx;
		font-weight: bold;

		.more {
			font-size: 24rpx;
			color: #722ED1;
			display: flex;
			align-items: center;
			transition: all 0.3s ease;
			cursor: pointer;
			padding: 8rpx 12rpx;
			border-radius: 8rpx;

			&:hover {
				background-color: rgba(114, 46, 209, 0.1);
			}

			&:active {
				opacity: 0.8;
				transform: scale(0.95);
			}
		}
	}

	.article-list {
		padding: 0 30rpx;
	}

	.loading-container, .error-container, .empty-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 60rpx 30rpx;
		text-align: center;
	}

	.loading-text, .error-text, .empty-text {
		font-size: 28rpx;
		color: #999;
		margin-bottom: 20rpx;
	}

	.retry-btn {
		background-color: #722ED1;
		color: #fff;
		padding: 16rpx 32rpx;
		border-radius: 20rpx;
		font-size: 26rpx;
		transition: all 0.3s ease;

		&:active {
			opacity: 0.8;
			transform: scale(0.98);
		}
	}

	.article-item {
		display: flex;
		align-items: center; /* 垂直居中对齐 */
		background-color: #fff;
		border-radius: 16rpx;
		margin-bottom: 24rpx;
		padding: 24rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.04);
		transition: all 0.3s ease;
		min-height: 180rpx; /* 最小高度，保证文章项大小基本一致 */

		&:active {
			transform: translateY(1rpx);
			box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.04);
		}

		.article-image {
			width: 200rpx;
			height: 140rpx;
			border-radius: 12rpx;
			overflow: hidden;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
			flex-shrink: 0; /* 防止图片被压缩 */
			margin-right: 24rpx;

			image {
				width: 100%;
				height: 100%;
				transition: all 0.3s ease;
			}
		}

		.article-info {
			flex: 1;
			display: flex;
			flex-direction: column;
			justify-content: center;
			min-height: 90rpx; /* 与图片高度一致 */
			overflow: hidden;

			.article-title {
				font-size: 30rpx;
				font-weight: 600;
				color: #333;
				line-height: 1.3;
				margin-bottom: 12rpx;
				/* 标题只显示1行，超出部分用省略号 */
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			.article-desc {
				font-size: 26rpx;
				color: #666;
				line-height: 1.4;
				margin-bottom: 12rpx;
				/* 描述最多显示2行，超出部分用省略号 */
				display: -webkit-box;
				-webkit-box-orient: vertical;
				-webkit-line-clamp: 2;
				line-clamp: 2;
				overflow: hidden;
				text-overflow: ellipsis;
				flex: 1;
			}

			.article-meta {
				display: flex;
				justify-content: space-between;
				align-items: center;
				font-size: 24rpx;
				color: #999;

				.meta-left {
					display: flex;
					align-items: center;
					gap: 20rpx;

					.views, .comments {
						display: flex;
						align-items: center;

						text {
							margin-left: 6rpx;
							font-size: 24rpx;
						}
					}

					.comments {
						text {
							color: #999;
						}
					}
				}

				.time-icon-group {
					display: flex;
					align-items: center;
					gap: 8rpx;

					.article-icon {
						width: 28rpx;
						height: 28rpx;
						border-radius: 4rpx;
						overflow: hidden;
						flex-shrink: 0;

						image {
							width: 100%;
							height: 100%;
						}
					}

					.time {
						display: flex;
						align-items: center;

						text {
							margin-left: 6rpx;
							font-size: 24rpx;
						}
					}
				}
			}
		}
	}

	.trend {
		margin-top: 20rpx;

		.section-header {
			.trend-tabs {
				display: flex;
				
				.tab {
					font-size: 26rpx;
					color: #666;
					padding: 6rpx 20rpx;
					border-radius: 20rpx;
					margin-left: 10rpx;
					font-weight: normal;
					transition: all 0.3s ease;
					position: relative;
					overflow: hidden;
					
					&.active {
						background: rgba(114, 46, 209, 0.1);
						color: #722ED1;
						font-weight: bold;
						
						&::after {
							content: '';
							position: absolute;
							bottom: 0;
							left: 25%;
							width: 50%;
							height: 4rpx;
							background: #722ED1;
							border-radius: 2rpx;
						}
					}
					
					&:active {
						opacity: 0.8;
						transform: scale(0.98);
					}
				}
			}
		}

		.trend-chart {
			background-color: #fff;
			margin: 0 30rpx;
			border-radius: 20rpx;
			padding: 24rpx;
			box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.05);
			overflow: hidden;
			
			.chart-container {
				width: 100%;
				position: relative;
				height: 350rpx; // 减小高度
				
				.line-chart {
					width: 100%;
					height: 100%;
					display: flex;
					position: relative;
					padding: 20rpx 10rpx 40rpx 60rpx;
					box-sizing: border-box;
					
					.y-axis {
						position: absolute;
						left: 0;
						top: 0;
						bottom: 40rpx;
						width: 60rpx;
						display: flex;
						flex-direction: column;
						justify-content: space-between;
						
						.y-label {
							font-size: 20rpx;
							color: #999;
							text-align: right;
							padding-right: 10rpx;
							transform: translateY(50%);
							
							&:first-child {
								transform: translateY(0);
							}
							
							&:last-child {
								transform: translateY(0);
							}
						}
					}
					
					.chart-body {
						flex: 1;
						position: relative;
						height: 100%;
						
						.grid-lines {
							position: absolute;
							top: 0;
							left: 0;
							right: 0;
							bottom: 0;
							
							.h-grid-line {
								position: absolute;
								left: 0;
								right: 0;
								height: 1rpx;
								background-color: rgba(0, 0, 0, 0.05);
								
								&:nth-child(1) { top: 0%; }
								&:nth-child(2) { top: 25%; }
								&:nth-child(3) { top: 50%; }
								&:nth-child(4) { top: 75%; }
								&:nth-child(5) { top: 100%; }
							}
						}
						
						.data-point {
							position: absolute;
							width: 12rpx;
							height: 12rpx;
							border-radius: 50%;
							background-color: #fff;
							border: 3rpx solid #722ED1;
							transform: translate(-50%, 50%) scale(0);
							z-index: 3;
							transition: all 0.3s ease;
							animation: pointAppear 0.5s forwards;
							
							@keyframes pointAppear {
								0% {
									transform: translate(-50%, 50%) scale(0);
									opacity: 0;
								}
								100% {
									transform: translate(-50%, 50%) scale(1);
									opacity: 1;
								}
							}
							
							&.active {
								width: 20rpx;
								height: 20rpx;
								box-shadow: 0 0 10rpx rgba(114, 46, 209, 0.5);
							}
							
							.point-value {
								position: absolute;
								bottom: 100%;
								left: 50%;
								transform: translateX(-50%);
								background-color: #722ED1;
								color: #fff;
								font-size: 20rpx;
								padding: 4rpx 12rpx;
								border-radius: 10rpx;
								white-space: nowrap;
								opacity: 0;
								transition: all 0.3s ease;
								margin-bottom: 8rpx;
								
								&.show {
									opacity: 1;
								}
								
								&::after {
									content: '';
									position: absolute;
									top: 100%;
									left: 50%;
									transform: translateX(-50%);
									border-left: 6rpx solid transparent;
									border-right: 6rpx solid transparent;
									border-top: 6rpx solid #722ED1;
								}
							}
						}
						
						.data-line {
							position: absolute;
							top: 0;
							left: 0;
							right: 0;
							bottom: 0;
							opacity: 0;
							animation: lineAppear 0.5s forwards;
							
							@keyframes lineAppear {
								0% {
									opacity: 0;
								}
								100% {
									opacity: 1;
								}
							}
							
							.line-gradient {
								position: absolute;
								top: 0;
								left: 0;
								right: 0;
								bottom: 0;
								background: linear-gradient(to bottom, rgba(114, 46, 209, 0.2), rgba(114, 46, 209, 0));
							}
							
							&::before {
								content: '';
								position: absolute;
								top: 0;
								left: 0;
								right: 0;
								bottom: 0;
								border-top: 3rpx solid #722ED1;
							}
						}
					}
					
					.x-axis {
						position: absolute;
						left: 60rpx;
						right: 10rpx;
						bottom: 0;
						height: 40rpx;
						
						.x-label {
							position: absolute;
							font-size: 20rpx;
							color: #999;
							transform: translateX(-50%);
							text-align: center;
							white-space: nowrap;
						}
					}
				}
			}
			
			.chart-info {
				padding: 10rpx 0;
				
				.chart-title {
					font-size: 30rpx;
					font-weight: bold;
					color: #333;
					margin-bottom: 15rpx;
				}
				
				.chart-desc {
					font-size: 26rpx;
					color: #666;
					margin-bottom: 30rpx;
					line-height: 1.6;
				}
				
				.chart-stats {
					display: flex;
					justify-content: space-around;
					border-top: 1px solid #f0f0f0;
					padding-top: 20rpx;
					
					.stat-item {
						text-align: center;
						
						.stat-value {
							font-size: 36rpx;
							font-weight: bold;
							color: #722ED1;
							display: block;
							margin-bottom: 6rpx;
						}
						
						.stat-label {
							font-size: 24rpx;
							color: #999;
						}
					}
				}
			}
		}
	}
</style>