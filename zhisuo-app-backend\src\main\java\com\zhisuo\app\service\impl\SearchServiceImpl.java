package com.zhisuo.app.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhisuo.app.common.context.UserContext;
import com.zhisuo.app.dto.response.SearchResponse;
import com.zhisuo.app.dto.response.SearchSuggestionResponse;
import com.zhisuo.app.entity.*;
import com.zhisuo.app.mapper.ArticleMapper;
import com.zhisuo.app.mapper.HotTopicMapper;
import com.zhisuo.app.mapper.UserSearchHistoryMapper;
import com.zhisuo.app.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;


import java.util.*;
import java.util.stream.Collectors;

/**
 * 搜索服务实现类
 */
@Slf4j
@Service
public class SearchServiceImpl implements SearchService {
    
    @Autowired
    private HotTopicMapper hotTopicMapper;
    
    @Autowired
    private ArticleMapper articleMapper;
    
    @Autowired
    private UserSearchHistoryMapper userSearchHistoryMapper;
    
    @Autowired
    private ContentTagService contentTagService;
    
    @Autowired
    private TagService tagService;


    @Override
    public SearchResponse search(String keyword, String type, Integer page, Integer size) {
        long startTime = System.currentTimeMillis();

        log.info("开始搜索: keyword={}, type={}, page={}, size={}", keyword, type, page, size);

        SearchResponse response = new SearchResponse();
        response.setKeyword(keyword);
        response.setType(type);

        SearchResponse.SearchStats stats = new SearchResponse.SearchStats();

        // 根据类型搜索
        if ("all".equals(type) || "topic".equals(type)) {
            Page<HotTopic> topicPage = searchTopics(keyword, page, size, null, null);
            response.setTopics(topicPage);
            stats.setTopicCount(topicPage.getTotal());
            log.info("热点话题搜索结果: 总数={}, 当前页数据量={}", topicPage.getTotal(), topicPage.getRecords().size());
        }

        if ("all".equals(type) || "article".equals(type)) {
            Page<Article> articlePage = searchArticles(keyword, page, size, null, null);
            response.setArticles(articlePage);
            stats.setArticleCount(articlePage.getTotal());
            log.info("文章搜索结果: 总数={}, 当前页数据量={}", articlePage.getTotal(), articlePage.getRecords().size());
        }
        
        // 计算总数
        long totalCount = 0;
        if (response.getTopics() != null) {
            totalCount += response.getTopics().getTotal();
        }
        if (response.getArticles() != null) {
            totalCount += response.getArticles().getTotal();
        }
        response.setTotalCount(totalCount);
        
        // 获取相关搜索建议
        response.setRelatedKeywords(getRelatedKeywords(keyword, 5));
        
        // 设置统计信息
        stats.setSearchTime(System.currentTimeMillis() - startTime);
        stats.setHasMore(totalCount > (page + 1) * size);
        response.setStats(stats);
        
        // 记录搜索行为
        recordSearch(keyword, type);

        // 添加调试信息
        log.info("搜索响应数据: totalCount={}, topics.total={}, topics.records.size={}",
                response.getTotalCount(),
                response.getTopics() != null ? response.getTopics().getTotal() : 0,
                response.getTopics() != null ? response.getTopics().getRecords().size() : 0);

        return response;
    }
    
    @Override
    public Page<HotTopic> searchTopics(String keyword, Integer page, Integer size, String source, String tagId) {
        Page<HotTopic> pageObj = new Page<>(page, size);
        LambdaQueryWrapper<HotTopic> queryWrapper = new LambdaQueryWrapper<>();

        // 基础条件
        queryWrapper.eq(HotTopic::getStatus, 1);

        // 关键词搜索（标题和描述）
        if (StringUtils.hasText(keyword)) {
            log.info("搜索热点话题，关键词: {}", keyword);
            queryWrapper.and(wrapper -> wrapper
                .like(HotTopic::getTitle, keyword)
                .or()
                .like(HotTopic::getDescription, keyword)
            );
        }
        
        // 来源筛选
        if (StringUtils.hasText(source)) {
            queryWrapper.eq(HotTopic::getSource, source);
        }
        
        // 标签筛选
        if (StringUtils.hasText(tagId)) {
            // 获取有该标签的内容ID列表
            List<String> contentIds = contentTagService.getContentIdsByTag(tagId, "topic");
            if (!contentIds.isEmpty()) {
                queryWrapper.in(HotTopic::getTopicId, contentIds);
            } else {
                // 如果没有找到相关内容，返回空结果
                return pageObj;
            }
        }
        
        // 按热度值降序排序
        queryWrapper.orderByDesc(HotTopic::getHotValue);

        Page<HotTopic> result = hotTopicMapper.selectPage(pageObj, queryWrapper);
        log.info("热点话题搜索完成，关键词: {}, 结果数量: {}", keyword, result.getTotal());

        return result;
    }
    
    @Override
    public Page<Article> searchArticles(String keyword, Integer page, Integer size, String source, String tagId) {
        Page<Article> pageObj = new Page<>(page, size);
        LambdaQueryWrapper<Article> queryWrapper = new LambdaQueryWrapper<>();
        
        // 基础条件
        queryWrapper.eq(Article::getStatus, 1);
        
        // 关键词搜索（标题、描述和内容）
        if (StringUtils.hasText(keyword)) {
            queryWrapper.and(wrapper -> wrapper
                .like(Article::getTitle, keyword)
                .or()
                .like(Article::getDescription, keyword)
                .or()
                .like(Article::getContent, keyword)
            );
        }
        
        // 来源筛选
        if (StringUtils.hasText(source)) {
            queryWrapper.eq(Article::getSource, source);
        }
        
        // 标签筛选
        if (StringUtils.hasText(tagId)) {
            // 获取有该标签的内容ID列表
            List<String> contentIds = contentTagService.getContentIdsByTag(tagId, "article");
            if (!contentIds.isEmpty()) {
                queryWrapper.in(Article::getArticleId, contentIds);
            } else {
                // 如果没有找到相关内容，返回空结果
                return pageObj;
            }
        }
        
        // 按发布时间降序排序
        queryWrapper.orderByDesc(Article::getPublishTime);
        
        return articleMapper.selectPage(pageObj, queryWrapper);
    }
    
    @Override
    public SearchSuggestionResponse getSearchSuggestions(String keyword, Integer limit) {
        SearchSuggestionResponse response = new SearchSuggestionResponse();
        response.setKeyword(keyword);
        
        // 获取关键词联想建议
        List<String> suggestions = generateKeywordSuggestions(keyword, limit);
        response.setSuggestions(suggestions);
        
        // 获取热门搜索词
        List<String> hotKeywords = getHotKeywords(limit);
        response.setHotKeywords(hotKeywords);
        
        // 获取相关标签
        List<SearchSuggestionResponse.TagSuggestion> relatedTags = getRelatedTags(keyword, limit);
        response.setRelatedTags(relatedTags);

        // 获取热点话题建议
        List<SearchSuggestionResponse.TopicSuggestion> topicSuggestions = getTopicSuggestions(keyword, limit);
        response.setTopicSuggestions(topicSuggestions);

        // 获取用户历史搜索（如果已登录）
        String userId = UserContext.getUserId();
        if (StringUtils.hasText(userId)) {
            List<String> historyKeywords = getUserSearchHistory(userId, limit);
            response.setHistoryKeywords(historyKeywords);
        }

        return response;
    }
    
    @Override
    public List<String> getHotKeywords(Integer limit) {
        // 从搜索历史中统计热门关键词
        LambdaQueryWrapper<UserSearchHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(UserSearchHistory::getKeyword)
                .isNotNull(UserSearchHistory::getKeyword)
                .ne(UserSearchHistory::getKeyword, "")
                .ge(UserSearchHistory::getSearchTime, new Date(System.currentTimeMillis() - 7 * 24 * 60 * 60 * 1000L)) // 最近7天
                .orderByDesc(UserSearchHistory::getSearchTime);
        
        List<UserSearchHistory> histories = userSearchHistoryMapper.selectList(queryWrapper);
        
        // 统计关键词频次
        Map<String, Long> keywordCount = histories.stream()
                .collect(Collectors.groupingBy(UserSearchHistory::getKeyword, Collectors.counting()));
        
        // 按频次排序并返回前N个
        return keywordCount.entrySet().stream()
                .sorted(Map.Entry.<String, Long>comparingByValue().reversed())
                .limit(limit)
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
    }
    
    @Override
    public void recordSearch(String keyword, String type) {
        try {
            String userId = UserContext.getUserId();
            if (!StringUtils.hasText(userId) || !StringUtils.hasText(keyword)) {
                return;
            }

            String trimmedKeyword = keyword.trim();

            // 检查是否已存在相同的用户ID和关键词记录
            LambdaQueryWrapper<UserSearchHistory> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(UserSearchHistory::getUserId, userId)
                       .eq(UserSearchHistory::getKeyword, trimmedKeyword);

            UserSearchHistory existingHistory = userSearchHistoryMapper.selectOne(queryWrapper);

            if (existingHistory != null) {
                // 如果记录已存在，只更新搜索时间
                existingHistory.setSearchTime(new Date());
                userSearchHistoryMapper.updateById(existingHistory);
                log.info("更新搜索历史时间: userId={}, keyword={}, type={}", userId, trimmedKeyword, type);
            } else {
                // 如果记录不存在，插入新记录
                UserSearchHistory history = new UserSearchHistory();
                history.setHistoryId(IdUtil.fastSimpleUUID());
                history.setUserId(userId);
                history.setKeyword(trimmedKeyword);
                history.setSearchTime(new Date());

                userSearchHistoryMapper.insert(history);
                log.info("新增搜索历史: userId={}, keyword={}, type={}", userId, trimmedKeyword, type);
            }
        } catch (Exception e) {
            log.error("记录搜索历史失败: {}", e.getMessage(), e);
        }
    }
    
    @Override
    public SearchResponse searchByTag(String tagId, String type, Integer page, Integer size) {
        long startTime = System.currentTimeMillis();
        
        SearchResponse response = new SearchResponse();
        response.setType(type);
        
        // 获取标签信息
        Tag tag = tagService.getById(tagId);
        if (tag != null) {
            response.setKeyword("标签:" + tag.getTagName());
        }
        
        SearchResponse.SearchStats stats = new SearchResponse.SearchStats();
        
        // 根据类型搜索
        if ("all".equals(type) || "topic".equals(type)) {
            Page<HotTopic> topicPage = searchTopics(null, page, size, null, tagId);
            response.setTopics(topicPage);
            stats.setTopicCount(topicPage.getTotal());
        }
        
        if ("all".equals(type) || "article".equals(type)) {
            Page<Article> articlePage = searchArticles(null, page, size, null, tagId);
            response.setArticles(articlePage);
            stats.setArticleCount(articlePage.getTotal());
        }
        
        // 计算总数
        long totalCount = 0;
        if (response.getTopics() != null) {
            totalCount += response.getTopics().getTotal();
        }
        if (response.getArticles() != null) {
            totalCount += response.getArticles().getTotal();
        }
        response.setTotalCount(totalCount);
        
        // 设置统计信息
        stats.setSearchTime(System.currentTimeMillis() - startTime);
        stats.setHasMore(totalCount > (page + 1) * size);
        response.setStats(stats);
        
        return response;
    }
    
    @Override
    public List<String> getUserSearchHistory(String userId, Integer limit) {
        if (!StringUtils.hasText(userId)) {
            return new ArrayList<>();
        }
        
        LambdaQueryWrapper<UserSearchHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSearchHistory::getUserId, userId)
                .orderByDesc(UserSearchHistory::getSearchTime)
                .last("LIMIT " + limit);
        
        List<UserSearchHistory> histories = userSearchHistoryMapper.selectList(queryWrapper);
        
        // 去重并保持顺序
        return histories.stream()
                .map(UserSearchHistory::getKeyword)
                .distinct()
                .collect(Collectors.toList());
    }
    
    @Override
    public void clearUserSearchHistory(String userId) {
        if (!StringUtils.hasText(userId)) {
            return;
        }

        LambdaQueryWrapper<UserSearchHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSearchHistory::getUserId, userId);

        userSearchHistoryMapper.delete(queryWrapper);
        log.info("清空用户搜索历史: userId={}", userId);
    }

    @Override
    public void deleteUserSearchHistory(String userId, String keyword) {
        if (!StringUtils.hasText(userId) || !StringUtils.hasText(keyword)) {
            return;
        }

        LambdaQueryWrapper<UserSearchHistory> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(UserSearchHistory::getUserId, userId)
                   .eq(UserSearchHistory::getKeyword, keyword.trim());

        userSearchHistoryMapper.delete(queryWrapper);
        log.info("删除用户搜索历史: userId={}, keyword={}", userId, keyword);
    }
    
    @Override
    public List<String> getRelatedKeywords(String keyword, Integer limit) {
        // 简单的相关关键词生成逻辑
        List<String> related = new ArrayList<>();
        
        if (StringUtils.hasText(keyword)) {
            // 基于关键词生成相关搜索
            related.add(keyword + " 最新");
            related.add(keyword + " 分析");
            related.add(keyword + " 趋势");
            related.add(keyword + " 影响");
            related.add(keyword + " 发展");
        }
        
        return related.stream().limit(limit).collect(Collectors.toList());
    }
    
    /**
     * 生成关键词联想建议
     */
    private List<String> generateKeywordSuggestions(String keyword, Integer limit) {
        List<String> suggestions = new ArrayList<>();
        
        if (!StringUtils.hasText(keyword)) {
            return suggestions;
        }
        
        // 从热点话题标题中查找包含关键词的内容
        LambdaQueryWrapper<HotTopic> topicWrapper = new LambdaQueryWrapper<>();
        topicWrapper.select(HotTopic::getTitle)
                .eq(HotTopic::getStatus, 1)
                .like(HotTopic::getTitle, keyword)
                .orderByDesc(HotTopic::getHotValue)
                .last("LIMIT " + limit);
        
        List<HotTopic> topics = hotTopicMapper.selectList(topicWrapper);
        suggestions.addAll(topics.stream()
                .map(HotTopic::getTitle)
                .collect(Collectors.toList()));
        
        return suggestions.stream().distinct().limit(limit).collect(Collectors.toList());
    }
    
    /**
     * 获取相关标签
     */
    private List<SearchSuggestionResponse.TagSuggestion> getRelatedTags(String keyword, Integer limit) {
        List<SearchSuggestionResponse.TagSuggestion> tagSuggestions = new ArrayList<>();
        
        if (!StringUtils.hasText(keyword)) {
            return tagSuggestions;
        }
        
        // 查找标签名称包含关键词的标签
        LambdaQueryWrapper<Tag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Tag::getStatus, 1)
                .like(Tag::getTagName, keyword)
                .orderByAsc(Tag::getCreateTime)
                .last("LIMIT " + limit);
        
        List<Tag> tags = tagService.list(queryWrapper);
        
        for (Tag tag : tags) {
            SearchSuggestionResponse.TagSuggestion suggestion = new SearchSuggestionResponse.TagSuggestion();
            suggestion.setTagId(tag.getTagId());
            suggestion.setTagName(tag.getTagName());
            
            // 统计该标签的内容数量
            long contentCount = contentTagService.getContentCountByTag(tag.getTagId());
            suggestion.setContentCount(contentCount);
            
            tagSuggestions.add(suggestion);
        }
        
        return tagSuggestions;
    }

    /**
     * 获取热点话题建议
     */
    private List<SearchSuggestionResponse.TopicSuggestion> getTopicSuggestions(String keyword, Integer limit) {
        List<SearchSuggestionResponse.TopicSuggestion> topicSuggestions = new ArrayList<>();

        if (!StringUtils.hasText(keyword)) {
            return topicSuggestions;
        }

        // 查询匹配的热点话题
        LambdaQueryWrapper<HotTopic> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(HotTopic::getStatus, 1) // 只查询有效的热点话题
                .and(wrapper -> wrapper
                    .like(HotTopic::getTitle, keyword)
                    .or()
                    .like(HotTopic::getDescription, keyword)
                )
                .orderByDesc(HotTopic::getHotValue) // 按热度值排序
                .last("LIMIT " + limit);

        List<HotTopic> hotTopics = hotTopicMapper.selectList(queryWrapper);

        for (HotTopic topic : hotTopics) {
            SearchSuggestionResponse.TopicSuggestion suggestion = new SearchSuggestionResponse.TopicSuggestion();
            suggestion.setTopicId(topic.getTopicId());
            suggestion.setTitle(topic.getTitle());
            suggestion.setHotValue(topic.getHotValue());
            suggestion.setSearchCount(topic.getSearchCount());

            topicSuggestions.add(suggestion);
        }

        return topicSuggestions;
    }
}
