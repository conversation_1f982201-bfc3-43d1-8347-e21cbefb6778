package com.zhisuo.app.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zhisuo.app.entity.Article;

import java.util.List;

/**
 * 文章Service接口
 */
public interface ArticleService extends IService<Article> {
    
    /**
     * 获取文章列表
     * 
     * @param limit 数量限制
     * @return 文章列表
     */
    List<Article> getArticleList(Integer limit);
    
    /**
     * 获取文章分页列表
     *
     * @param page 分页对象
     * @param source 来源筛选
     * @return 文章分页列表
     */
    Page<Article> getArticlePage(Page<Article> page, String source);

    /**
     * 获取文章分页列表（支持排序）
     *
     * @param page 分页对象
     * @param source 来源筛选
     * @param sortBy 排序字段
     * @param sortOrder 排序方向
     * @return 文章分页列表
     */
    Page<Article> getArticlePage(Page<Article> page, String source, String sortBy, String sortOrder);
    
    /**
     * 获取今日文章分页列表
     * 
     * @param page 分页对象
     * @param source 来源筛选
     * @return 今日文章分页列表
     */
    Page<Article> getTodayArticlePage(Page<Article> page, String source);
    
    /**
     * 更新文章浏览量
     * 
     * @param articleId 文章ID
     */
    void updateViewCount(String articleId);
    
    /**
     * 更新文章点赞量
     *
     * @param articleId 文章ID
     */
    void updateLikeCount(String articleId);

    /**
     * 更新文章点赞量（支持增量）
     *
     * @param articleId 文章ID
     * @param increment 增量（1或-1）
     */
    void updateLikeCount(String articleId, int increment);

    /**
     * 更新文章评论数
     *
     * @param articleId 文章ID
     * @param increment 增量（1或-1）
     */
    void updateCommentCount(String articleId, int increment);
    
    /**
     * 定时获取并更新央视新闻文章
     */
    void fetchAndUpdateCCTVNews();
    
    /**
     * 根据来源获取文章
     * 
     * @param source 来源
     * @param limit 数量限制
     * @return 文章列表
     */
    List<Article> getArticlesBySource(String source, Integer limit);
}
