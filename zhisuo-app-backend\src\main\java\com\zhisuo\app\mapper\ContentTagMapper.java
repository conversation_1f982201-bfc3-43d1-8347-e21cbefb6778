package com.zhisuo.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhisuo.app.entity.ContentTag;
import com.zhisuo.app.entity.Tag;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 内容标签关联Mapper接口
 */
public interface ContentTagMapper extends BaseMapper<ContentTag> {
    
    /**
     * 获取内容的标签列表
     * 
     * @param contentId 内容ID
     * @param contentType 内容类型(article/topic)
     * @return 标签列表
     */
    @Select("SELECT t.* FROM tags t " +
            "JOIN content_tags ct ON t.tag_id = ct.tag_id " +
            "WHERE ct.content_id = #{contentId} AND ct.content_type = #{contentType} AND t.status = 1")
    List<Tag> getContentTags(@Param("contentId") String contentId, @Param("contentType") String contentType);
} 