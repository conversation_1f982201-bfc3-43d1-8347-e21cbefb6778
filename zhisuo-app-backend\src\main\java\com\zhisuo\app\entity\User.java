package com.zhisuo.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 用户实体类
 */
@Data
@TableName("users")
public class User {
    
    @TableId
    private String userId;     // 用户唯一标识
    
    private String phone;      // 手机号(登录账号)
    
    private String passwordHash;  // 密码哈希值
    
    private String passwordSalt;  // 密码盐值
    
    private String nickname;   // 用户昵称
    
    private String avatar;     // 头像URL
    
    private Integer memberLevel;  // 会员等级(0:普通,1:黄金,2:铂金等)
    
    private Date createTime;   // 注册时间
    
    private Date updateTime;   // 更新时间
    
    private Date lastLoginTime;  // 最后登录时间
    
    private Integer status;    // 状态(1:正常,0:禁用)
} 