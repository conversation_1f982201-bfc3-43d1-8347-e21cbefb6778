## 2.0.10（2024-06-07）
- 优化 uni-app x 中，size 属性的类型
## 2.0.9（2024-01-12）
fix: 修复图标大小默认值错误的问题
## 2.0.8（2023-12-14）
- 修复 项目未使用 ts 情况下，打包报错的bug
## 2.0.7（2023-12-14）
- 修复 size 属性为 string 时，不加单位导致尺寸异常的bug
## 2.0.6（2023-12-11）
- 优化 兼容老版本icon类型，如 top ，bottom 等
## 2.0.5（2023-12-11）
- 优化 兼容老版本icon类型，如 top ，bottom 等
## 2.0.4（2023-12-06）
- 优化 uni-app x 下示例项目图标排序
## 2.0.3（2023-12-06）
- 修复 nvue下引入组件报错的bug
## 2.0.2（2023-12-05）
-优化 size 属性支持单位
## 2.0.1（2023-12-05）
- 新增 uni-app x 支持定义图标
## 1.3.5（2022-01-24）
- 优化 size 属性可以传入不带单位的字符串数值
## 1.3.4（2022-01-24）
- 优化 size 支持其他单位
## 1.3.3（2022-01-17）
- 修复 nvue 有些图标不显示的bug，兼容老版本图标
## 1.3.2（2021-12-01）
- 优化 示例可复制图标名称
## 1.3.1（2021-11-23）
- 优化 兼容旧组件 type 值
## 1.3.0（2021-11-19）
- 新增 更多图标
- 优化 自定义图标使用方式
- 优化 组件UI，并提供设计资源，详见:[https://uniapp.dcloud.io/component/uniui/resource](https://uniapp.dcloud.io/component/uniui/resource)
- 文档迁移，详见:[https://uniapp.dcloud.io/component/uniui/uni-icons](https://uniapp.dcloud.io/component/uniui/uni-icons)
## 1.1.7（2021-11-08）
## 1.2.0（2021-07-30）
- 组件兼容 vue3，如何创建vue3项目，详见 [uni-app 项目支持 vue3 介绍](https://ask.dcloud.net.cn/article/37834)
## 1.1.5（2021-05-12）
- 新增 组件示例地址
## 1.1.4（2021-02-05）
- 调整为uni_modules目录规范
