package com.zhisuo.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhisuo.app.entity.UserLike;

import java.util.Map;

/**
 * 点赞服务接口
 */
public interface LikeService extends IService<UserLike> {
    
    /**
     * 切换点赞状态
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param userId 用户ID
     * @return 点赞后的状态（true=已点赞，false=已取消点赞）
     */
    boolean toggleLike(String contentId, String contentType, String userId);
    
    /**
     * 检查用户是否已点赞
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param userId 用户ID
     * @return 是否已点赞
     */
    boolean isLiked(String contentId, String contentType, String userId);
    
    /**
     * 获取内容的点赞数量
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 点赞数量
     */
    long getLikeCount(String contentId, String contentType);
    
    /**
     * 获取用户点赞的内容列表
     *
     * @param userId 用户ID
     * @param contentType 内容类型（可选）
     * @param page 页码
     * @param size 每页大小
     * @return 点赞内容列表
     */
    Map<String, Object> getUserLikes(String userId, String contentType, Integer page, Integer size);
}
