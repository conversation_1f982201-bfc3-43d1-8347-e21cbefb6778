package com.zhisuo.app.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Pattern;

/**
 * 短信验证码请求DTO
 */
@Data
public class SmsCodeRequest {
    
    @NotBlank(message = "手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式不正确")
    private String phone;
    
    @NotBlank(message = "验证码类型不能为空")
    @Pattern(regexp = "^(login|register)$", message = "验证码类型不正确")
    private String type;
} 