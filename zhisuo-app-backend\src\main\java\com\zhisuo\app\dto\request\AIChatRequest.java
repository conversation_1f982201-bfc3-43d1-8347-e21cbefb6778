package com.zhisuo.app.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;
import java.util.List;

/**
 * AI对话请求DTO
 */
@Data
public class AIChatRequest {
    
    /**
     * 用户消息
     */
    @NotBlank(message = "用户消息不能为空")
    @Size(max = 2000, message = "消息长度不能超过2000字符")
    private String message;
    
    /**
     * 页面类型 (article, hotDetail等)
     */
    private String pageType;
    
    /**
     * 内容数据 (用于生成上下文)
     */
    private ContentData contentData;
    
    /**
     * 消息历史 (用于保持对话上下文)
     */
    private List<ChatMessage> messageHistory;
    
    /**
     * 聊天消息
     */
    @Data
    public static class ChatMessage {
        /**
         * 消息角色 (user, assistant)
         */
        @NotBlank(message = "消息角色不能为空")
        private String role;
        
        /**
         * 消息内容
         */
        @NotBlank(message = "消息内容不能为空")
        private String content;
        
        /**
         * 消息时间戳 (支持ISO字符串格式或Long时间戳)
         */
        @JsonFormat(pattern = "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'", timezone = "UTC")
        private LocalDateTime timestamp;
    }
    
    /**
     * 内容数据
     */
    @Data
    public static class ContentData {
        /**
         * 标题
         */
        private String title;
        
        /**
         * 描述
         */
        private String description;
        
        /**
         * 作者
         */
        private String author;
        
        /**
         * 发布时间
         */
        private String publishTime;
        
        /**
         * 浏览量
         */
        private Integer viewCount;
        
        /**
         * 点赞数
         */
        private Integer likeCount;
        
        /**
         * 评论数
         */
        private Integer commentCount;
        
        /**
         * 内容摘要
         */
        private String summary;
        
        /**
         * 标签
         */
        private List<String> tags;
    }
}
