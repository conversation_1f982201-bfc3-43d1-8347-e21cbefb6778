package com.zhisuo.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhisuo.app.dto.response.CommentResponse;
import com.zhisuo.app.entity.Comment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 评论Mapper接口
 */
@Mapper
public interface CommentMapper extends BaseMapper<Comment> {
    
    /**
     * 分页查询评论列表（包含用户信息和点赞状态）
     *
     * @param page 分页对象
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param userId 当前用户ID
     * @return 评论列表
     */
    @Select("SELECT c.*, u.nickname as username, u.avatar as userAvatar, " +
            "CASE WHEN ul.like_id IS NOT NULL THEN 1 ELSE 0 END as isLiked " +
            "FROM comments c " +
            "LEFT JOIN users u ON c.user_id = u.user_id " +
            "LEFT JOIN user_likes ul ON c.comment_id = ul.content_id AND ul.content_type = 'comment' AND ul.user_id = #{userId} " +
            "WHERE c.content_id = #{contentId} AND c.content_type = #{contentType} AND c.parent_id IS NULL AND c.status = 1 " +
            "ORDER BY c.create_time DESC")
    Page<CommentResponse> selectCommentPage(Page<CommentResponse> page, 
                                          @Param("contentId") String contentId, 
                                          @Param("contentType") String contentType,
                                          @Param("userId") String userId);
    
    /**
     * 查询评论的回复列表
     *
     * @param parentId 父评论ID
     * @param userId 当前用户ID
     * @return 回复列表
     */
    @Select("SELECT c.*, u.nickname as username, u.avatar as userAvatar, " +
            "CASE WHEN ul.like_id IS NOT NULL THEN 1 ELSE 0 END as isLiked " +
            "FROM comments c " +
            "LEFT JOIN users u ON c.user_id = u.user_id " +
            "LEFT JOIN user_likes ul ON c.comment_id = ul.content_id AND ul.content_type = 'comment' AND ul.user_id = #{userId} " +
            "WHERE c.parent_id = #{parentId} AND c.status = 1 " +
            "ORDER BY c.create_time ASC")
    List<CommentResponse> selectRepliesByParentId(@Param("parentId") String parentId, @Param("userId") String userId);

    /**
     * 查询评论的所有子评论（用于删除）
     *
     * @param parentId 父评论ID
     * @return 子评论列表
     */
    @Select("SELECT * FROM comments WHERE parent_id = #{parentId} AND status = 1")
    List<Comment> selectChildComments(@Param("parentId") String parentId);

    /**
     * 查询用户的评论列表
     *
     * @param page 分页对象
     * @param userId 用户ID
     * @return 评论列表
     */
    @Select("SELECT c.*, u.nickname as username, u.avatar as userAvatar, " +
            "CASE WHEN ul.like_id IS NOT NULL THEN 1 ELSE 0 END as isLiked " +
            "FROM comments c " +
            "LEFT JOIN users u ON c.user_id = u.user_id " +
            "LEFT JOIN user_likes ul ON c.comment_id = ul.content_id AND ul.content_type = 'comment' AND ul.user_id = #{userId} " +
            "WHERE c.user_id = #{userId} AND c.status = 1 " +
            "ORDER BY c.create_time DESC")
    Page<CommentResponse> selectUserComments(Page<CommentResponse> page, @Param("userId") String userId);
    
    /**
     * 查询评论详情（包含用户信息和点赞状态）
     *
     * @param commentId 评论ID
     * @param userId 当前用户ID
     * @return 评论详情
     */
    @Select("SELECT c.*, u.nickname as username, u.avatar as userAvatar, " +
            "CASE WHEN ul.like_id IS NOT NULL THEN 1 ELSE 0 END as isLiked " +
            "FROM comments c " +
            "LEFT JOIN users u ON c.user_id = u.user_id " +
            "LEFT JOIN user_likes ul ON c.comment_id = ul.content_id AND ul.content_type = 'comment' AND ul.user_id = #{userId} " +
            "WHERE c.comment_id = #{commentId} AND c.status = 1")
    CommentResponse selectCommentDetail(@Param("commentId") String commentId, @Param("userId") String userId);
    
    /**
     * 更新评论点赞数
     *
     * @param commentId 评论ID
     * @param increment 增量（1或-1）
     */
    @Update("UPDATE comments SET like_count = like_count + #{increment} WHERE comment_id = #{commentId}")
    void updateLikeCount(@Param("commentId") String commentId, @Param("increment") int increment);
    
    /**
     * 统计内容的评论数量
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 评论数量
     */
    @Select("SELECT COUNT(*) FROM comments WHERE content_id = #{contentId} AND content_type = #{contentType} AND status = 1")
    long countByContent(@Param("contentId") String contentId, @Param("contentType") String contentType);
}
