package com.zhisuo.app.service.impl;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhisuo.app.entity.ContentTag;
import com.zhisuo.app.entity.Tag;
import com.zhisuo.app.mapper.ContentTagMapper;
import com.zhisuo.app.service.ContentTagService;
import com.zhisuo.app.service.TagService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 内容标签关联Service实现类
 */
@Slf4j
@Service
public class ContentTagServiceImpl extends ServiceImpl<ContentTagMapper, ContentTag> implements ContentTagService {
    
    @Autowired
    private ContentTagMapper contentTagMapper;
    
    @Autowired
    private TagService tagService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addContentTag(String contentId, String contentType, String tagId) {
        if (contentId == null || contentId.trim().isEmpty() || 
            contentType == null || contentType.trim().isEmpty() || 
            tagId == null || tagId.trim().isEmpty()) {
            return false;
        }
        
        // 检查标签是否已关联
        LambdaQueryWrapper<ContentTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContentTag::getContentId, contentId)
                .eq(ContentTag::getContentType, contentType)
                .eq(ContentTag::getTagId, tagId);
        
        if (count(queryWrapper) > 0) {
            log.info("内容标签关联已存在: contentId={}, contentType={}, tagId={}", contentId, contentType, tagId);
            return true;
        }
        
        // 创建新的关联
        ContentTag contentTag = new ContentTag();
        contentTag.setId(IdUtil.fastSimpleUUID());
        contentTag.setContentId(contentId);
        contentTag.setContentType(contentType);
        contentTag.setTagId(tagId);
        contentTag.setCreateTime(new Date());
        
        boolean result = save(contentTag);
        if (result) {
            log.info("添加内容标签关联成功: contentId={}, contentType={}, tagId={}", contentId, contentType, tagId);
        } else {
            log.error("添加内容标签关联失败: contentId={}, contentType={}, tagId={}", contentId, contentType, tagId);
        }
        
        return result;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAddContentTags(String contentId, String contentType, List<String> tagIds) {
        if (contentId == null || contentId.trim().isEmpty() || 
            contentType == null || contentType.trim().isEmpty() || 
            tagIds == null || tagIds.isEmpty()) {
            return false;
        }
        
        // 去重
        List<String> distinctTagIds = tagIds.stream()
                .filter(id -> id != null && !id.trim().isEmpty())
                .distinct()
                .collect(Collectors.toList());
        
        // 查询已存在的关联
        LambdaQueryWrapper<ContentTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContentTag::getContentId, contentId)
                .eq(ContentTag::getContentType, contentType)
                .in(ContentTag::getTagId, distinctTagIds);
        
        List<ContentTag> existingTags = list(queryWrapper);
        
        // 记录已存在的标签ID
        List<String> existingTagIds = existingTags.stream()
                .map(ContentTag::getTagId)
                .collect(Collectors.toList());
        
        // 创建不存在的关联
        List<ContentTag> newContentTags = new ArrayList<>();
        for (String tagId : distinctTagIds) {
            if (!existingTagIds.contains(tagId)) {
                ContentTag contentTag = new ContentTag();
                contentTag.setId(IdUtil.fastSimpleUUID());
                contentTag.setContentId(contentId);
                contentTag.setContentType(contentType);
                contentTag.setTagId(tagId);
                contentTag.setCreateTime(new Date());
                
                newContentTags.add(contentTag);
            }
        }
        
        // 批量保存新关联
        if (!newContentTags.isEmpty()) {
            boolean result = saveBatch(newContentTags);
            if (result) {
                log.info("批量添加内容标签关联成功: contentId={}, contentType={}, 数量={}", contentId, contentType, newContentTags.size());
            } else {
                log.error("批量添加内容标签关联失败: contentId={}, contentType={}, 数量={}", contentId, contentType, newContentTags.size());
                return false;
            }
        }
        
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addContentTagByName(String contentId, String contentType, String tagName, String category) {
        if (contentId == null || contentId.trim().isEmpty() || 
            contentType == null || contentType.trim().isEmpty() || 
            tagName == null || tagName.trim().isEmpty()) {
            return false;
        }
        
        // 获取或创建标签
        Tag tag = tagService.createTagIfNotExists(tagName, category);
        if (tag == null) {
            log.error("创建标签失败: tagName={}", tagName);
            return false;
        }
        
        // 添加内容标签关联
        return addContentTag(contentId, contentType, tag.getTagId());
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchAddContentTagsByName(String contentId, String contentType, List<String> tagNames, String category) {
        if (contentId == null || contentId.trim().isEmpty() || 
            contentType == null || contentType.trim().isEmpty() || 
            tagNames == null || tagNames.isEmpty()) {
            return false;
        }
        
        // 批量获取或创建标签
        List<Tag> tags = tagService.batchCreateTagsIfNotExist(tagNames, category);
        if (tags.isEmpty()) {
            log.error("批量创建标签失败: tagNames={}", tagNames);
            return false;
        }
        
        // 提取标签ID列表
        List<String> tagIds = tags.stream()
                .map(Tag::getTagId)
                .collect(Collectors.toList());
        
        // 批量添加内容标签关联
        return batchAddContentTags(contentId, contentType, tagIds);
    }
    
    @Override
    public List<Tag> getContentTags(String contentId, String contentType) {
        if (contentId == null || contentId.trim().isEmpty() || 
            contentType == null || contentType.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        return contentTagMapper.getContentTags(contentId, contentType);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean deleteContentTags(String contentId, String contentType) {
        if (contentId == null || contentId.trim().isEmpty() || 
            contentType == null || contentType.trim().isEmpty()) {
            return false;
        }
        
        LambdaQueryWrapper<ContentTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContentTag::getContentId, contentId)
                .eq(ContentTag::getContentType, contentType);
        
        boolean result = remove(queryWrapper);
        if (result) {
            log.info("删除内容标签关联成功: contentId={}, contentType={}", contentId, contentType);
        } else {
            log.info("没有内容标签关联需要删除: contentId={}, contentType={}", contentId, contentType);
        }
        
        return true;
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateContentTags(String contentId, String contentType, List<String> tagIds) {
        // 先删除原有标签关联
        deleteContentTags(contentId, contentType);
        
        // 如果新标签列表为空，则直接返回成功
        if (tagIds == null || tagIds.isEmpty()) {
            return true;
        }
        
        // 添加新的标签关联
        return batchAddContentTags(contentId, contentType, tagIds);
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateContentTagsByName(String contentId, String contentType, List<String> tagNames, String category) {
        // 先删除原有标签关联
        deleteContentTags(contentId, contentType);
        
        // 如果新标签列表为空，则直接返回成功
        if (tagNames == null || tagNames.isEmpty()) {
            return true;
        }
        
        // 添加新的标签关联
        return batchAddContentTagsByName(contentId, contentType, tagNames, category);
    }

    @Override
    public List<String> getContentIdsByTag(String tagId, String contentType) {
        if (tagId == null || tagId.trim().isEmpty() ||
            contentType == null || contentType.trim().isEmpty()) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<ContentTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.select(ContentTag::getContentId)
                .eq(ContentTag::getTagId, tagId)
                .eq(ContentTag::getContentType, contentType);

        List<ContentTag> contentTags = list(queryWrapper);
        return contentTags.stream()
                .map(ContentTag::getContentId)
                .collect(Collectors.toList());
    }

    @Override
    public long getContentCountByTag(String tagId) {
        if (tagId == null || tagId.trim().isEmpty()) {
            return 0;
        }

        LambdaQueryWrapper<ContentTag> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ContentTag::getTagId, tagId);

        return count(queryWrapper);
    }
}