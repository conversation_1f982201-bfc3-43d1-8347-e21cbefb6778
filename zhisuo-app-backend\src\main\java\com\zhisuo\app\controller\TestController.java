package com.zhisuo.app.controller;

import com.zhisuo.app.common.Result;
import com.zhisuo.app.config.AIConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 测试控制器
 */
@Slf4j
@RestController
@RequestMapping("/api/test")
public class TestController {
    
    @Autowired
    private AIConfig aiConfig;
    
    /**
     * 测试AI配置
     */
    @GetMapping("/ai-config")
    public Result<AIConfig> testAIConfig() {
        log.info("AI配置测试: {}", aiConfig);
        return Result.success(aiConfig);
    }
    
    /**
     * 测试异常
     */
    @GetMapping("/exception")
    public Result<String> testException() {
        throw new RuntimeException("这是一个测试异常");
    }
}
