package com.zhisuo.app.dto.response;

import lombok.Data;

import java.util.List;

/**
 * AI分析响应DTO
 */
@Data
public class AIAnalysisResponse {
    
    /**
     * 内容摘要
     */
    private String summary;
    
    /**
     * 关键词
     */
    private List<String> keywords;
    
    /**
     * 情感分析
     */
    private String sentiment;
    
    /**
     * 热度评分 (1-10)
     */
    private Integer hotScore;
    
    /**
     * 推荐理由
     */
    private String recommendation;
    
    /**
     * 相关话题
     */
    private List<String> relatedTopics;

    /**
     * 未来趋势分析
     */
    private String futureTrends;

    /**
     * 内容质量评分 (1-10)
     */
    private Integer qualityScore;

    /**
     * 影响力评估
     */
    private String influenceAssessment;

    /**
     * 分析ID (保存到数据库后的ID)
     */
    private String analysisId;

    /**
     * 分析时间戳
     */
    private Long timestamp;

    /**
     * 是否为备用分析 (当API调用失败时)
     */
    private Boolean isFallback;

    /**
     * 是否已保存到数据库
     */
    private Boolean savedToDatabase = false;
    
    public AIAnalysisResponse() {
        this.timestamp = System.currentTimeMillis();
        this.isFallback = false;
    }
    
    public static AIAnalysisResponse fallback() {
        AIAnalysisResponse response = new AIAnalysisResponse();
        response.setIsFallback(true);
        response.setSummary("暂时无法生成智能分析，请稍后重试");
        response.setFutureTrends("暂时无法分析未来趋势");
        response.setQualityScore(5);
        response.setInfluenceAssessment("暂时无法评估影响力");
        return response;
    }
}
