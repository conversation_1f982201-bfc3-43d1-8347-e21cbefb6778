package com.zhisuo.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 用户点赞实体类
 */
@Data
@TableName("user_likes")
public class UserLike {
    
    @TableId
    private String likeId;       // 点赞ID
    
    private String userId;       // 用户ID
    
    private String contentId;    // 内容ID
    
    private String contentType;  // 内容类型(article/topic/comment)
    
    private Date createTime;     // 点赞时间
} 