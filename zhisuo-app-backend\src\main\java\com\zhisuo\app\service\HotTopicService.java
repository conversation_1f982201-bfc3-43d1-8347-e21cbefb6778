package com.zhisuo.app.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.zhisuo.app.entity.HotTopic;

import java.util.List;

/**
 * 热点话题Service接口
 */
public interface HotTopicService extends IService<HotTopic> {
    
    /**
     * 获取热点话题列表
     * 
     * @param limit 数量限制
     * @return 热点话题列表
     */
    List<HotTopic> getHotTopicList(Integer limit);
    
    /**
     * 获取热点话题分页列表
     * 
     * @param page 分页对象
     * @param source 来源平台
     * @return 热点话题分页列表
     */
    Page<HotTopic> getHotTopicPage(Page<HotTopic> page, String source);
    
    /**
     * 获取今日热点话题分页列表
     * 
     * @param page 分页对象
     * @param source 来源平台
     * @return 今日热点话题分页列表
     */
    Page<HotTopic> getTodayHotTopicPage(Page<HotTopic> page, String source);
    
    /**
     * 获取昨日热点话题分页列表
     * 
     * @param page 分页对象
     * @param source 来源平台
     * @return 昨日热点话题分页列表
     */
    Page<HotTopic> getYesterdayHotTopicPage(Page<HotTopic> page, String source);
    
    /**
     * 更新浏览量
     * 
     * @param topicId 话题ID
     */
    void updateViewCount(String topicId);
    
    /**
     * 更新搜索量
     * 
     * @param topicId 话题ID
     */
    void updateSearchCount(String topicId);
    
    /**
     * 获取并更新热点话题数据
     */
    void fetchAndUpdateHotTopics();
    
    /**
     * 获取并更新昨日热点话题数据
     */
    void fetchAndUpdateYesterdayHotTopics();
    
    /**
     * 刷新热点话题排名
     * 根据热度值重新计算排名
     */
    void refreshHotTopicRanks();
    
    /**
     * 更新热点话题的热度值历史记录并计算趋势
     */
    void updateHotValueHistory();
    
    /**
     * 更新昨日热点话题的热度值历史记录并计算趋势
     */
    void updateYesterdayHotValueHistory();
} 