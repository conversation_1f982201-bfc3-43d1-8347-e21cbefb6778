package com.zhisuo.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 用户搜索历史实体类
 */
@Data
@TableName("user_search_history")
public class UserSearchHistory {
    
    @TableId
    private String historyId;    // 历史记录ID
    
    private String userId;       // 关联用户ID
    
    private String keyword;      // 搜索关键词
    
    private Date searchTime;     // 搜索时间
} 