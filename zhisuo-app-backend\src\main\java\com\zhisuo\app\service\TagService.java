package com.zhisuo.app.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.zhisuo.app.entity.Tag;

import java.util.List;

/**
 * 标签Service接口
 */
public interface TagService extends IService<Tag> {
    
    /**
     * 根据标签名称获取标签
     * 
     * @param tagName 标签名称
     * @return 标签对象，如果不存在则返回null
     */
    Tag getByTagName(String tagName);
    
    /**
     * 获取所有标签列表
     * 
     * @param category 保留参数，已不再使用
     * @return 标签列表
     */
    List<Tag> getTagsByCategory(String category);
    
    /**
     * 创建标签（如果不存在）
     * 
     * @param tagName 标签名称
     * @param category 保留参数，已不再使用
     * @return 创建的标签对象
     */
    Tag createTagIfNotExists(String tagName, String category);
    
    /**
     * 批量创建标签（如果不存在）
     *
     * @param tagNames 标签名称列表
     * @param category 保留参数，已不再使用
     * @return 创建的标签对象列表
     */
    List<Tag> batchCreateTagsIfNotExist(List<String> tagNames, String category);

    /**
     * 获取内容的标签名称列表
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 标签名称列表
     */
    List<String> getContentTagNames(String contentId, String contentType);
}