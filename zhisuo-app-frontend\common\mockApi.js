// 模拟API响应
const mockResponses = {
  // 短信验证码
  '/v1/auth/sms/send': (data) => {
    console.log('模拟发送短信验证码:', data);
    return {
      code: 0,
      message: '验证码发送成功',
      data: {
        expireTime: 60
      }
    };
  },
  
  // 短信登录
  '/v1/auth/login/sms': (data) => {
    console.log('模拟短信登录:', data);
    // 模拟两个用户
    const users = {
      '13800138000': { // 已设置密码的用户
        userId: 'user_123456',
        nickname: '测试用户1',
        avatar: 'https://cdn.zhisuo.com/avatar/default.png',
        memberLevel: 0,
        hasPassword: true
      },
      '13800138001': { // 未设置密码的用户
        userId: 'user_654321',
        nickname: '测试用户2',
        avatar: 'https://cdn.zhisuo.com/avatar/default.png',
        memberLevel: 0,
        hasPassword: false
      }
    };
    
    const user = users[data.phone];
    if (!user) {
      // 新用户，自动注册
      const newUserId = 'user_' + Date.now();
      return {
        code: 0,
        message: '登录成功',
        data: {
          token: 'mock_token_' + Date.now(),
          refreshToken: 'mock_refresh_' + Date.now(),
          expiresIn: 86400,
          isNewUser: true,
          needSetPassword: true,
          userInfo: {
            userId: newUserId,
            nickname: '用户' + data.phone.substring(data.phone.length - 4),
            avatar: 'https://cdn.zhisuo.com/avatar/default.png',
            memberLevel: 0
          }
        }
      };
    }
    
    // 已注册用户
    return {
      code: 0,
      message: '登录成功',
      data: {
        token: 'mock_token_' + Date.now(),
        refreshToken: 'mock_refresh_' + Date.now(),
        expiresIn: 86400,
        isNewUser: false,
        needSetPassword: !user.hasPassword,
        userInfo: {
          userId: user.userId,
          nickname: user.nickname,
          avatar: user.avatar,
          memberLevel: user.memberLevel
        }
      }
    };
  },
  
  // 密码登录
  '/v1/auth/login/password': (data) => {
    console.log('模拟密码登录:', data);
    
    // 只允许13800138000以123456密码登录
    if (data.phone === '13800138000' && data.password === '123456') {
      return {
        code: 0,
        message: '登录成功',
        data: {
          token: 'mock_token_' + Date.now(),
          refreshToken: 'mock_refresh_' + Date.now(),
          expiresIn: 86400,
          userInfo: {
            userId: 'user_123456',
            nickname: '测试用户1',
            avatar: 'https://cdn.zhisuo.com/avatar/default.png',
            memberLevel: 0
          }
        }
      };
    }
    
    return {
      code: 1001,
      message: '账号或密码错误'
    };
  },
  
  // 设置密码
  '/v1/auth/password/set': (data) => {
    console.log('模拟设置密码:', data);
    return {
      code: 0,
      message: '密码设置成功',
      data: {
        token: 'mock_token_' + Date.now(),
        refreshToken: 'mock_refresh_' + Date.now(),
        expiresIn: 86400
      }
    };
  },
  
  // 跳过密码设置
  '/v1/auth/password/skip': (data) => {
    console.log('模拟跳过密码设置:', data);
    return {
      code: 0,
      message: '操作成功',
      data: {
        token: 'mock_token_' + Date.now(),
        refreshToken: 'mock_refresh_' + Date.now(),
        expiresIn: 86400
      }
    };
  },

  // 获取文章列表
  '/v1/articles': (data) => {
    console.log('模拟获取文章列表:', data);
    const page = data?.page || 0;
    const size = data?.size || 10;
    const sortBy = data?.sortBy || 'publishTime';
    const sortOrder = data?.sortOrder || 'desc';

    // 生成模拟文章数据
    const generateArticles = (startIndex, count) => {
      const articles = [];
      for (let i = 0; i < count; i++) {
        const index = startIndex + i;
        articles.push({
          articleId: `article_${index}`,
          title: `文章标题 ${index + 1} - 这是一个很长的标题用来测试显示效果`,
          description: `这是文章 ${index + 1} 的描述内容，用来展示文章的主要内容概要，让用户能够快速了解文章的主题和要点。`,
          coverImage: '/static/logo.png',
          viewCount: Math.floor(Math.random() * 5000) + 100,
          commentCount: Math.floor(Math.random() * 50) + 1,
          publishTime: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
          author: '智索编辑',
          category: '技术分享'
        });
      }
      return articles;
    };

    // 生成所有文章数据用于排序
    const allArticles = generateArticles(0, 50);

    // 排序
    allArticles.sort((a, b) => {
      let aValue, bValue;

      if (sortBy === 'viewCount') {
        aValue = a.viewCount;
        bValue = b.viewCount;
      } else if (sortBy === 'commentCount') {
        aValue = a.commentCount;
        bValue = b.commentCount;
      } else {
        // 默认按发布时间排序
        aValue = new Date(a.publishTime).getTime();
        bValue = new Date(b.publishTime).getTime();
      }

      if (sortOrder === 'desc') {
        return bValue - aValue;
      } else {
        return aValue - bValue;
      }
    });

    // 分页
    const startIndex = page * size;
    const endIndex = startIndex + size;
    const articles = allArticles.slice(startIndex, endIndex);

    return {
      code: 0,
      message: '获取成功',
      data: {
        content: articles,
        totalElements: 50, // 总共50篇文章
        totalPages: Math.ceil(50 / size),
        currentPage: page,
        size: size,
        hasNext: (page + 1) * size < 50
      }
    };
  }
};

// 模拟API请求
const mockRequest = (options) => {
  return new Promise((resolve, reject) => {
    const { url, data, method } = options;
    
    // 延迟模拟网络请求
    setTimeout(() => {
      if (mockResponses[url]) {
        const response = mockResponses[url](data);
        resolve({
          data: response,
          statusCode: 200,
          header: {}
        });
      } else {
        reject({
          errMsg: `未找到模拟接口: ${url}`
        });
      }
    }, 500);
  });
};

export default {
  BASE_URL: '', // 模拟API不需要基础URL
  request: mockRequest
}; 