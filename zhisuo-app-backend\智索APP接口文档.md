# 智索APP接口文档

## 目录

- [1. 文档说明](#1-文档说明)
- [2. 接口规范](#2-接口规范)
- [3. 认证模块](#3-认证模块)
- [4. 用户模块](#4-用户模块)
- [5. 上传模块](#5-上传模块)
- [6. 文章模块](#6-文章模块)
- [7. 热点话题模块](#7-热点话题模块)
- [8. 用户交互模块](#8-用户交互模块)
- [9. 系统通知模块](#9-系统通知模块)
- [10. 错误码说明](#10-错误码说明)

## 1. 文档说明

本文档描述智索APP的后端接口设计，基于现有的数据库设计和后端代码实现。接口遵循RESTful设计原则，采用HTTP协议进行通信。

### 1.1 接口版本

当前接口版本: v1

### 1.2 接口基础路径

所有接口均以 `/v1` 作为基础路径前缀

## 2. 接口规范

### 2.1 请求格式

- GET请求：参数通过URL Query Parameters传递
- POST/PUT/DELETE请求：参数通过JSON格式在Request Body中传递
- 编码格式：UTF-8
- Content-Type: application/json

### 2.2 响应格式

统一的JSON响应结构：

```json
{
    "code": 0,            // 状态码，0表示成功，非0表示失败
    "message": "success", // 状态描述
    "data": {             // 业务数据
        // 具体业务数据
    }
}
```

### 2.3 认证方式

采用JWT (JSON Web Token)进行身份认证。

- 登录成功后，服务器返回token
- 客户端请求需在Header中携带token: `Authorization: Bearer {token}`
- token过期或无效时，服务端返回401状态码

## 3. 认证模块

### 3.1 发送短信验证码

- **接口URL**: `/v1/auth/sms/send`
- **请求方式**: POST
- **功能说明**: 发送短信验证码
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| phone | String | 是 | 手机号 |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| expireTime | Integer | 验证码有效期(秒) |

- **请求示例**:
```json
{
    "phone": "13800138000"
}
```

- **响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "expireTime": 300
    }
}
```

### 3.2 短信验证码登录

- **接口URL**: `/v1/auth/login/sms`
- **请求方式**: POST
- **功能说明**: 使用短信验证码登录（如果用户不存在将自动注册）
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| phone | String | 是 | 手机号 |
| code | String | 是 | 短信验证码 |
| deviceId | String | 是 | 设备ID |
| deviceType | String | 是 | 设备类型(ios/android/h5) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| token | String | JWT令牌 |
| refreshToken | String | 刷新令牌 |
| expiresIn | Long | 令牌有效期(秒) |
| isNewUser | Boolean | 是否新用户 |
| needSetPassword | Boolean | 是否需要设置密码 |
| userInfo | Object | 用户基本信息 |

- **请求示例**:
```json
{
    "phone": "13800138000",
    "code": "6666",
    "deviceId": "device_id_123456",
    "deviceType": "android"
}
```

- **响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
        "expiresIn": 86400,
        "isNewUser": false,
        "needSetPassword": true,
        "userInfo": {
            "userId": "USR00000000000000000000000000001",
            "phone": "13800138000",
            "nickname": "用户1234",
            "avatar": "https://cdn.zhisuo.com/avatar/default.png",
            "memberLevel": 0
        }
    }
}
```

### 3.3 密码登录

- **接口URL**: `/v1/auth/login/password`
- **请求方式**: POST
- **功能说明**: 使用账号密码登录
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| phone | String | 是 | 手机号 |
| password | String | 是 | 密码 |
| deviceId | String | 是 | 设备ID |
| deviceType | String | 是 | 设备类型(ios/android/h5) |

- **响应参数**:

与3.2短信验证码登录接口相同

- **请求示例**:
```json
{
    "phone": "13800138000",
    "password": "password123",
    "deviceId": "device_id_123456",
    "deviceType": "android"
}
```

### 3.4 设置密码

- **接口URL**: `/v1/auth/password/set`
- **请求方式**: POST
- **功能说明**: 设置用户密码
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| userId | String | 是 | 用户ID |
| password | String | 是 | 密码 |
| deviceId | String | 是 | 设备ID |
| deviceType | String | 是 | 设备类型(ios/android/h5) |

- **响应参数**:

与登录接口相同，返回token等信息

### 3.5 跳过密码设置

- **接口URL**: `/v1/auth/password/skip`
- **请求方式**: POST
- **功能说明**: 跳过密码设置，使用默认密码
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| userId | String | 是 | 用户ID |
| deviceId | String | 是 | 设备ID |
| deviceType | String | 是 | 设备类型(ios/android/h5) |

- **响应参数**:

与登录接口相同，返回token等信息

### 3.6 刷新Token

- **接口URL**: `/v1/auth/token/refresh`
- **请求方式**: POST
- **功能说明**: 使用refreshToken刷新访问令牌
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| refreshToken | String | 是 | 刷新令牌 |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| token | String | 新的访问令牌 |
| refreshToken | String | 新的刷新令牌 |
| expiresIn | Long | 令牌有效期(秒) |

### 3.7 退出登录

- **接口URL**: `/v1/auth/logout`
- **请求方式**: POST
- **功能说明**: 用户退出登录
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**: 无

## 4. 用户模块

### 4.1 获取用户信息

- **接口URL**: `/v1/user/info`
- **请求方式**: GET
- **功能说明**: 获取当前登录用户信息
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**: 无
- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| userId | String | 用户ID |
| phone | String | 手机号 |
| nickname | String | 用户昵称 |
| avatar | String | 头像URL |
| memberLevel | Integer | 会员等级(0:普通,1:黄金,2:铂金等) |

- **响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "userId": "USR00000000000000000000000000001",
        "phone": "13800138000",
        "nickname": "张小明",
        "avatar": "https://cdn.zhisuo.com/avatar/user1.jpg",
        "memberLevel": 0
    }
}
```

### 4.2 更新用户信息

- **接口URL**: `/v1/user/info`
- **请求方式**: PUT
- **功能说明**: 更新用户基本信息
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| nickname | String | 否 | 用户昵称(最大12个字符) |
| avatar | String | 否 | 头像URL |

- **响应参数**:

更新后的用户信息，格式同4.1接口

### 4.3 修改密码

- **接口URL**: `/v1/user/password`
- **请求方式**: PUT
- **功能说明**: 修改用户密码
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| oldPassword | String | 是 | 原密码 |
| newPassword | String | 是 | 新密码 |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| success | Boolean | 是否成功 |

## 5. 上传模块

### 5.1 上传头像

- **接口URL**: `/v1/upload/avatar`
- **请求方式**: POST
- **功能说明**: 上传用户头像
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| file | File | 是 | 头像文件(图片格式，大小不超过2MB) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| url | String | 上传后的头像URL |

- **响应示例**:
```json
{
    "code": 0,
    "message": "success",
    "data": {
        "url": "https://cdn.zhisuo.com/avatar/user1.jpg"
    }
}
```

## 6. 文章模块

### 6.1 获取文章列表

- **接口URL**: `/v1/articles`
- **请求方式**: GET
- **功能说明**: 获取文章列表
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |
| source | String | 否 | 来源筛选 |
| tagId | String | 否 | 标签ID筛选 |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| content | Array | 文章列表 |
| totalElements | Long | 总记录数 |
| totalPages | Integer | 总页数 |
| size | Integer | 每页大小 |
| number | Integer | 当前页码 |

### 6.2 获取文章详情

- **接口URL**: `/v1/articles/{articleId}`
- **请求方式**: GET
- **功能说明**: 获取文章详情内容
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| articleId | String | 是 | 文章ID(路径参数) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| articleId | String | 文章ID |
| title | String | 文章标题 |
| description | String | 文章描述 |
| content | String | 文章内容 |
| coverImage | String | 封面图URL |
| source | String | 来源 |
| sourceUrl | String | 来源URL |
| iconUrl | String | 图标URL |
| viewCount | Integer | 阅读数 |
| commentCount | Integer | 评论数 |
| likeCount | Integer | 点赞数 |
| isRead | Boolean | 是否已读 |
| isFavorite | Boolean | 是否已收藏 |
| publishTime | String | 发布时间 |
| tags | Array | 标签列表 |

## 7. 热点话题模块

### 7.1 获取热点话题列表

- **接口URL**: `/v1/hot-topics`
- **请求方式**: GET
- **功能说明**: 获取热点话题列表
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |
| source | String | 否 | 来源平台筛选 |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| content | Array | 热点话题列表 |
| totalElements | Long | 总记录数 |
| totalPages | Integer | 总页数 |
| size | Integer | 每页大小 |
| number | Integer | 当前页码 |

### 7.2 获取热点话题详情

- **接口URL**: `/v1/hot-topics/{topicId}`
- **请求方式**: GET
- **功能说明**: 获取热点话题详情
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| topicId | String | 是 | 话题ID(路径参数) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| topicId | String | 话题ID |
| title | String | 话题标题 |
| description | String | 话题描述 |
| source | String | 来源平台 |
| sourceUrl | String | 来源URL |
| hotValue | String | 热度值 |
| viewCount | Integer | 阅读量 |
| searchCount | Integer | 搜索量 |
| trend | Integer | 趋势(1:上升,0:持平,-1:下降) |
| rank | Integer | 热榜排名 |
| isRead | Boolean | 是否已读 |
| isFavorite | Boolean | 是否已收藏 |
| collectTime | String | 收集时间 |
| tags | Array | 标签列表 |

### 7.3 搜索热点内容

- **接口URL**: `/v1/search`
- **请求方式**: GET
- **功能说明**: 搜索热点内容(包括话题和文章)
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| keyword | String | 是 | 搜索关键词 |
| type | String | 否 | 内容类型(topic/article)，不传则搜索全部 |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| topics | Object | 话题搜索结果(分页) |
| articles | Object | 文章搜索结果(分页) |

## 8. 用户交互模块

### 8.1 收藏内容

- **接口URL**: `/v1/favorites`
- **请求方式**: POST
- **功能说明**: 收藏内容
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentId | String | 是 | 内容ID |
| contentType | String | 是 | 内容类型(article/topic) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| favoriteId | String | 收藏ID |

### 8.2 取消收藏

- **接口URL**: `/v1/favorites/{favoriteId}`
- **请求方式**: DELETE
- **功能说明**: 取消收藏
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| favoriteId | String | 是 | 收藏ID(路径参数) |

- **响应参数**: 无

### 8.3 获取收藏列表

- **接口URL**: `/v1/favorites`
- **请求方式**: GET
- **功能说明**: 获取用户收藏列表
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentType | String | 否 | 内容类型(article/topic)，不传则获取全部 |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| content | Array | 收藏列表 |
| totalElements | Long | 总记录数 |
| totalPages | Integer | 总页数 |

### 8.4 点赞内容

- **接口URL**: `/v1/likes`
- **请求方式**: POST
- **功能说明**: 点赞内容
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentId | String | 是 | 内容ID |
| contentType | String | 是 | 内容类型(article/topic/comment) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| likeId | String | 点赞ID |

### 8.5 取消点赞

- **接口URL**: `/v1/likes/{likeId}`
- **请求方式**: DELETE
- **功能说明**: 取消点赞
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| likeId | String | 是 | 点赞ID(路径参数) |

- **响应参数**: 无

### 8.6 发表评论

- **接口URL**: `/v1/comments`
- **请求方式**: POST
- **功能说明**: 发表评论
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentId | String | 是 | 内容ID |
| contentType | String | 是 | 内容类型(article/topic) |
| content | String | 是 | 评论内容 |
| parentId | String | 否 | 父评论ID(回复评论时需要) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| commentId | String | 评论ID |

### 8.7 获取评论列表

- **接口URL**: `/v1/comments`
- **请求方式**: GET
- **功能说明**: 获取内容的评论列表
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| contentId | String | 是 | 内容ID |
| contentType | String | 是 | 内容类型(article/topic) |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| content | Array | 评论列表 |
| totalElements | Long | 总记录数 |
| totalPages | Integer | 总页数 |

## 9. 系统通知模块

### 9.1 获取系统通知列表

- **接口URL**: `/v1/notices`
- **请求方式**: GET
- **功能说明**: 获取系统通知列表
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| page | Integer | 否 | 页码，默认0 |
| size | Integer | 否 | 每页大小，默认20 |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| content | Array | 通知列表 |
| totalElements | Long | 总记录数 |
| totalPages | Integer | 总页数 |
| unreadCount | Integer | 未读通知数量 |

### 9.2 获取通知详情

- **接口URL**: `/v1/notices/{noticeId}`
- **请求方式**: GET
- **功能说明**: 获取通知详情
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| noticeId | String | 是 | 通知ID(路径参数) |

- **响应参数**:

| 参数名 | 类型 | 说明 |
| ------ | ---- | ---- |
| noticeId | String | 通知ID |
| title | String | 通知标题 |
| content | String | 通知内容 |
| noticeType | Integer | 通知类型 |
| imageUrl | String | 图片URL |
| publishTime | String | 发布时间 |
| isRead | Boolean | 是否已读 |

### 9.3 标记通知已读

- **接口URL**: `/v1/notices/read`
- **请求方式**: PUT
- **功能说明**: 标记通知为已读
- **请求头**: `Authorization: Bearer {token}`
- **请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
| ------ | ---- | ---- | ---- |
| noticeIds | Array | 是 | 通知ID列表 |

- **响应参数**: 无

## 10. 错误码说明

| 错误码 | 错误信息 | 说明 |
| ------ | -------- | ---- |
| 0 | success | 成功 |
| 1000 | System error | 系统错误 |
| 2000 | Invalid parameter | 参数不合法 |
| 3000 | Unauthorized | 未授权 |
| 3001 | Token expired | 令牌过期 |
| 3002 | Invalid token | 无效令牌 |
| 4000 | Resource not found | 资源不存在 |
| 5000 | Business error | 业务错误 |
| 5001 | Phone already registered | 手机号已注册 |
| 5002 | Invalid verification code | 验证码错误 |
| 5003 | Verification code expired | 验证码过期 |
| 5004 | Password error | 密码错误 