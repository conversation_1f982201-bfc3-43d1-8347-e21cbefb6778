package com.zhisuo.app.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhisuo.app.dto.request.CommentCreateRequest;
import com.zhisuo.app.dto.response.CommentResponse;
import com.zhisuo.app.entity.Comment;
import com.zhisuo.app.mapper.CommentMapper;
import com.zhisuo.app.service.ArticleService;
import com.zhisuo.app.service.CommentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.UUID;

/**
 * 评论服务实现类
 */
@Slf4j
@Service
public class CommentServiceImpl extends ServiceImpl<CommentMapper, Comment> implements CommentService {
    
    @Autowired
    private CommentMapper commentMapper;

    @Autowired
    private ArticleService articleService;
    
    @Override
    public Page<CommentResponse> getCommentPage(String contentId, String contentType, String userId, Integer page, Integer size) {
        Page<CommentResponse> commentPage = new Page<>(page + 1, size); // MyBatis-Plus使用1基索引
        commentPage = commentMapper.selectCommentPage(commentPage, contentId, contentType, userId);
        
        // 为每个评论加载回复列表
        for (CommentResponse comment : commentPage.getRecords()) {
            List<CommentResponse> replies = getAllRepliesForComment(comment.getCommentId(), userId);

            // 为每个回复设置被回复的用户名
            setReplyToUsername(replies, comment);

            comment.setReplies(replies);
        }
        
        return commentPage;
    }
    
    @Override
    @Transactional
    public String createComment(CommentCreateRequest request, String userId) {
        Comment comment = new Comment();
        comment.setCommentId(UUID.randomUUID().toString().replace("-", ""));
        comment.setContentId(request.getContentId());
        comment.setContentType(request.getContentType());
        comment.setUserId(userId);
        comment.setContent(request.getContent());
        comment.setParentId(request.getParentId());
        comment.setLikeCount(0);
        comment.setCreateTime(new Date());
        comment.setStatus(1);

        save(comment);

        // 更新文章评论数
        if ("article".equals(request.getContentType())) {
            articleService.updateCommentCount(request.getContentId(), 1);
        }

        log.info("用户{}发表评论: {}", userId, comment.getCommentId());
        return comment.getCommentId();
    }
    
    @Override
    @Transactional
    public void deleteComment(String commentId, String userId) {
        Comment comment = getById(commentId);
        if (comment == null) {
            throw new RuntimeException("评论不存在");
        }

        if (!comment.getUserId().equals(userId)) {
            throw new RuntimeException("无权删除此评论");
        }

        // 查询所有子评论
        List<Comment> childComments = commentMapper.selectChildComments(commentId);
        int deletedCount = 1; // 包含主评论

        // 软删除所有子评论
        if (!childComments.isEmpty()) {
            for (Comment childComment : childComments) {
                childComment.setStatus(0);
                updateById(childComment);
                deletedCount++;
            }
            log.info("删除评论{}的{}条子评论", commentId, childComments.size());
        }

        // 软删除主评论
        comment.setStatus(0);
        updateById(comment);

        // 更新文章评论数（减去主评论和所有子评论的数量）
        if ("article".equals(comment.getContentType())) {
            articleService.updateCommentCount(comment.getContentId(), -deletedCount);
        }

        log.info("用户{}删除评论: {}，共删除{}条评论", userId, commentId, deletedCount);
    }
    
    @Override
    public CommentResponse getCommentDetail(String commentId, String userId) {
        CommentResponse comment = commentMapper.selectCommentDetail(commentId, userId);
        if (comment != null) {
            // 加载回复列表
            List<CommentResponse> replies = getAllRepliesForComment(commentId, userId);

            // 为每个回复设置被回复的用户名
            setReplyToUsername(replies, comment);

            comment.setReplies(replies);
        }
        return comment;
    }
    
    @Override
    public Page<CommentResponse> getUserComments(String userId, Integer page, Integer size) {
        Page<CommentResponse> commentPage = new Page<>(page + 1, size);
        return commentMapper.selectUserComments(commentPage, userId);
    }
    
    @Override
    public void updateLikeCount(String commentId, int increment) {
        commentMapper.updateLikeCount(commentId, increment);
    }
    
    @Override
    public long getCommentCount(String contentId, String contentType) {
        return commentMapper.countByContent(contentId, contentType);
    }

    /**
     * 为回复列表设置被回复的用户名
     * 根据parent_id查找被回复的用户名
     */
    private void setReplyToUsername(List<CommentResponse> replies, CommentResponse parentComment) {
        if (replies == null || replies.isEmpty()) {
            return;
        }

        // 创建一个Map，包含一级评论和所有回复，方便查找
        Map<String, String> commentUserMap = new HashMap<>();
        commentUserMap.put(parentComment.getCommentId(), parentComment.getUsername());

        // 先将所有回复的用户名加入Map
        for (CommentResponse reply : replies) {
            commentUserMap.put(reply.getCommentId(), reply.getUsername());
        }

        // 为每个回复设置被回复的用户名
        for (CommentResponse reply : replies) {
            if (reply.getParentId() != null) {
                String replyToUsername = commentUserMap.get(reply.getParentId());
                if (replyToUsername != null) {
                    reply.setReplyToUsername(replyToUsername);
                }
            }
        }
    }

    /**
     * 获取某个一级评论的所有回复（包括回复的回复）
     * 递归查找所有相关的回复
     */
    private List<CommentResponse> getAllRepliesForComment(String rootCommentId, String userId) {
        List<CommentResponse> allReplies = new ArrayList<>();

        // 先查找直接回复一级评论的回复
        List<CommentResponse> directReplies = commentMapper.selectRepliesByParentId(rootCommentId, userId);
        allReplies.addAll(directReplies);

        // 递归查找回复的回复
        for (CommentResponse reply : directReplies) {
            List<CommentResponse> subReplies = getAllRepliesRecursive(reply.getCommentId(), userId);
            allReplies.addAll(subReplies);
        }

        return allReplies;
    }

    /**
     * 递归查找回复的回复
     */
    private List<CommentResponse> getAllRepliesRecursive(String commentId, String userId) {
        List<CommentResponse> allReplies = new ArrayList<>();
        List<CommentResponse> replies = commentMapper.selectRepliesByParentId(commentId, userId);

        for (CommentResponse reply : replies) {
            allReplies.add(reply);
            // 继续递归查找
            List<CommentResponse> subReplies = getAllRepliesRecursive(reply.getCommentId(), userId);
            allReplies.addAll(subReplies);
        }

        return allReplies;
    }
}
