<template>
	<view class="loading-container">
		<!-- 动画样式1：旋转圆圈 -->
		<view v-if="type === 'spinner'" class="loading-animation loading-spinner">
			<view class="spinner"></view>
			<text v-if="showText">{{ text }}</text>
		</view>
		
		<!-- 动画样式2：跳动圆点 -->
		<view v-else-if="type === 'dots'" class="loading-animation loading-dots">
			<view class="dots-container">
				<view class="dot"></view>
				<view class="dot"></view>
				<view class="dot"></view>
			</view>
			<text v-if="showText">{{ text }}</text>
		</view>
		
		<!-- 动画样式3：波浪效果 -->
		<view v-else-if="type === 'wave'" class="loading-animation loading-wave">
			<view class="wave-container">
				<view class="wave-bar"></view>
				<view class="wave-bar"></view>
				<view class="wave-bar"></view>
				<view class="wave-bar"></view>
				<view class="wave-bar"></view>
			</view>
			<text v-if="showText">{{ text }}</text>
		</view>
		
		<!-- 动画样式4：脉冲效果 -->
		<view v-else-if="type === 'pulse'" class="loading-animation loading-pulse">
			<view class="pulse-circle"></view>
			<text v-if="showText">{{ text }}</text>
		</view>
		
		<!-- 动画样式5：呼吸圆环 -->
		<view v-else-if="type === 'breathing'" class="loading-animation loading-breathing">
			<view class="breathing-ring"></view>
			<text v-if="showText">{{ text }}</text>
		</view>
		
		<!-- 动画样式6：弹跳球 -->
		<view v-else-if="type === 'bounce'" class="loading-animation loading-bounce">
			<view class="bounce-ball"></view>
			<text v-if="showText">{{ text }}</text>
		</view>
		
		<!-- 默认：旋转圆圈 -->
		<view v-else class="loading-animation loading-spinner">
			<view class="spinner"></view>
			<text v-if="showText">{{ text }}</text>
		</view>
	</view>
</template>

<script>
export default {
	name: 'LoadingAnimation',
	props: {
		// 动画类型：spinner, dots, wave, pulse, breathing, bounce
		type: {
			type: String,
			default: 'spinner'
		},
		// 显示文本
		text: {
			type: String,
			default: '正在加载...'
		},
		// 是否显示文本
		showText: {
			type: Boolean,
			default: true
		},
		// 主题色
		color: {
			type: String,
			default: '#722ED1'
		},
		// 大小
		size: {
			type: String,
			default: 'medium' // small, medium, large
		}
	}
}
</script>

<style lang="scss" scoped>
.loading-container {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 20rpx;
}

.loading-animation {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	gap: 16rpx;

	text {
		color: #999;
		font-size: 26rpx;
		text-align: center;
	}
}

// 动画样式1：旋转圆圈
.loading-spinner {
	.spinner {
		width: 32rpx;
		height: 32rpx;
		border: 3rpx solid #f3f3f3;
		border-top: 3rpx solid #722ED1;
		border-radius: 50%;
		animation: spin 1s linear infinite;
	}
}

// 动画样式2：跳动圆点
.loading-dots {
	.dots-container {
		display: flex;
		gap: 8rpx;
	}

	.dot {
		width: 8rpx;
		height: 8rpx;
		background-color: #722ED1;
		border-radius: 50%;
		animation: bounce 1.4s ease-in-out infinite both;

		&:nth-child(1) { animation-delay: -0.32s; }
		&:nth-child(2) { animation-delay: -0.16s; }
		&:nth-child(3) { animation-delay: 0s; }
	}
}

// 动画样式3：波浪效果
.loading-wave {
	.wave-container {
		display: flex;
		gap: 4rpx;
	}

	.wave-bar {
		width: 4rpx;
		height: 24rpx;
		background-color: #722ED1;
		border-radius: 2rpx;
		animation: wave 1.2s ease-in-out infinite;

		&:nth-child(1) { animation-delay: 0s; }
		&:nth-child(2) { animation-delay: 0.1s; }
		&:nth-child(3) { animation-delay: 0.2s; }
		&:nth-child(4) { animation-delay: 0.3s; }
		&:nth-child(5) { animation-delay: 0.4s; }
	}
}

// 动画样式4：脉冲效果
.loading-pulse {
	.pulse-circle {
		width: 24rpx;
		height: 24rpx;
		background-color: #722ED1;
		border-radius: 50%;
		animation: pulse 1.5s ease-in-out infinite;
	}
}

// 动画样式5：呼吸圆环
.loading-breathing {
	.breathing-ring {
		width: 32rpx;
		height: 32rpx;
		border: 3rpx solid #722ED1;
		border-radius: 50%;
		animation: breathing 2s ease-in-out infinite;
	}
}

// 动画样式6：弹跳球
.loading-bounce {
	.bounce-ball {
		width: 16rpx;
		height: 16rpx;
		background-color: #722ED1;
		border-radius: 50%;
		animation: bouncing 0.6s ease-in-out infinite alternate;
	}
}

// 动画关键帧
@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

@keyframes bounce {
	0%, 80%, 100% {
		transform: scale(0);
	}
	40% {
		transform: scale(1);
	}
}

@keyframes wave {
	0%, 40%, 100% {
		transform: scaleY(0.4);
	}
	20% {
		transform: scaleY(1);
	}
}

@keyframes pulse {
	0% {
		transform: scale(0);
		opacity: 1;
	}
	100% {
		transform: scale(1);
		opacity: 0;
	}
}

@keyframes breathing {
	0%, 100% {
		transform: scale(1);
		opacity: 1;
	}
	50% {
		transform: scale(1.2);
		opacity: 0.7;
	}
}

@keyframes bouncing {
	0% {
		transform: translateY(0);
	}
	100% {
		transform: translateY(-20rpx);
	}
}
</style>
