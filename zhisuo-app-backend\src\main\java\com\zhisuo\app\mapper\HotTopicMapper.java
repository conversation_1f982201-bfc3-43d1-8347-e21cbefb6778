package com.zhisuo.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.zhisuo.app.entity.HotTopic;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;
 
/**
 * 热点话题Mapper接口
 */
public interface HotTopicMapper extends BaseMapper<HotTopic> {
    
    /**
     * 更新浏览量，同时更新热度值(热度值 = 阅读量*0.6 + 搜索量*0.4)
     * 
     * @param topicId 话题ID
     * @return 影响的行数
     */
    @Update("UPDATE hot_topics SET view_count = view_count + 1, hot_value = CONCAT(ROUND(view_count * 0.6 + search_count * 0.4, 1)), update_time = NOW() WHERE topic_id = #{topicId}")
    int updateViewCount(String topicId);
    
    /**
     * 更新搜索量，同时更新热度值(热度值 = 阅读量*0.6 + 搜索量*0.4)
     * 
     * @param topicId 话题ID
     * @return 影响的行数
     */
    @Update("UPDATE hot_topics SET search_count = search_count + 1, hot_value = CONCAT(ROUND(view_count * 0.6 + search_count * 0.4, 1)), update_time = NOW() WHERE topic_id = #{topicId}")
    int updateSearchCount(String topicId);
    
    /**
     * 从Redis同步浏览量到数据库
     * 
     * @param topicId 话题ID
     * @param count 增加的浏览量
     * @return 影响的行数
     */
    @Update("UPDATE hot_topics SET view_count = view_count + #{count}, hot_value = CONCAT(ROUND(view_count * 0.6 + search_count * 0.4, 1)), update_time = NOW() WHERE topic_id = #{topicId}")
    int updateViewCountFromRedis(@Param("topicId") String topicId, @Param("count") int count);
    
    /**
     * 从Redis同步搜索量到数据库
     * 
     * @param topicId 话题ID
     * @param count 增加的搜索量
     * @return 影响的行数
     */
    @Update("UPDATE hot_topics SET search_count = search_count + #{count}, hot_value = CONCAT(ROUND(view_count * 0.6 + search_count * 0.4, 1)), update_time = NOW() WHERE topic_id = #{topicId}")
    int updateSearchCountFromRedis(@Param("topicId") String topicId, @Param("count") int count);
    
    /**
     * 更新热点话题趋势
     * 
     * @param topicId 话题ID
     * @param trend 趋势值(1:上升,0:持平,-1:下降)
     * @return 影响的行数
     */
    @Update("UPDATE hot_topics SET trend = #{trend}, update_time = NOW() WHERE topic_id = #{topicId}")
    int updateTrend(@Param("topicId") String topicId, @Param("trend") int trend);
    
    /**
     * 更新热点话题排名
     * 
     * @param topicId 话题ID
     * @param rank 排名
     * @return 影响的行数
     */
    @Update("UPDATE hot_topics SET `rank` = #{rank}, update_time = NOW() WHERE topic_id = #{topicId}")
    int updateRank(@Param("topicId") String topicId, @Param("rank") int rank);
} 