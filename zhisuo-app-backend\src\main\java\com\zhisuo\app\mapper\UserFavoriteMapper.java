package com.zhisuo.app.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhisuo.app.entity.UserFavorite;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.Map;

/**
 * 用户收藏Mapper接口
 */
@Mapper
public interface UserFavoriteMapper extends BaseMapper<UserFavorite> {
    
    /**
     * 检查用户是否已收藏
     *
     * @param userId 用户ID
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 收藏记录
     */
    @Select("SELECT * FROM user_favorites WHERE user_id = #{userId} AND content_id = #{contentId} AND content_type = #{contentType}")
    UserFavorite selectByUserAndContent(@Param("userId") String userId, 
                                       @Param("contentId") String contentId, 
                                       @Param("contentType") String contentType);
    
    /**
     * 分页查询用户收藏的内容
     *
     * @param page 分页对象
     * @param userId 用户ID
     * @param contentType 内容类型（可选）
     * @return 收藏内容列表
     */
    @Select("<script>" +
            "SELECT uf.*, " +
            "CASE " +
            "  WHEN uf.content_type = 'article' THEN a.title " +
            "  WHEN uf.content_type = 'topic' THEN ht.title " +
            "END as contentTitle, " +
            "CASE " +
            "  WHEN uf.content_type = 'article' THEN a.description " +
            "  WHEN uf.content_type = 'topic' THEN ht.description " +
            "END as contentDescription, " +
            "CASE " +
            "  WHEN uf.content_type = 'article' THEN a.cover_image " +
            "  WHEN uf.content_type = 'topic' THEN NULL " +
            "END as contentImage, " +
            "CASE " +
            "  WHEN uf.content_type = 'article' THEN a.view_count " +
            "  WHEN uf.content_type = 'topic' THEN ht.view_count " +
            "END as viewCount, " +
            "CASE " +
            "  WHEN uf.content_type = 'article' THEN a.like_count " +
            "  WHEN uf.content_type = 'topic' THEN 0 " +
            "END as likeCount " +
            "FROM user_favorites uf " +
            "LEFT JOIN articles a ON uf.content_id = a.article_id AND uf.content_type = 'article' " +
            "LEFT JOIN hot_topics ht ON uf.content_id = ht.topic_id AND uf.content_type = 'topic' " +
            "WHERE uf.user_id = #{userId} " +
            "<if test='contentType != null'>" +
            "  AND uf.content_type = #{contentType} " +
            "</if>" +
            "ORDER BY uf.create_time DESC" +
            "</script>")
    Page<Map<String, Object>> selectUserFavorites(Page<Map<String, Object>> page,
                                                  @Param("userId") String userId,
                                                  @Param("contentType") String contentType);

    /**
     * 获取用户收藏内容的标签统计
     *
     * @param userId 用户ID
     * @return 标签统计结果
     */
    @Select("SELECT t.tag_name, COUNT(*) as count " +
            "FROM user_favorites uf " +
            "INNER JOIN content_tags ct ON uf.content_id = ct.content_id AND uf.content_type = ct.content_type " +
            "INNER JOIN tags t ON ct.tag_id = t.tag_id " +
            "WHERE uf.user_id = #{userId} AND t.status = 1 " +
            "GROUP BY t.tag_id, t.tag_name " +
            "ORDER BY count DESC")
    java.util.List<Map<String, Object>> selectUserTagCounts(@Param("userId") String userId);
}
