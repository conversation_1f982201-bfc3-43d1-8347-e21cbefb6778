package com.zhisuo.app.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * AI配置类
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "siliconflow.ai")
public class AIConfig {
    
    /**
     * API基础URL
     */
    private String baseUrl;
    
    /**
     * API密钥
     */
    private String apiKey;
    
    /**
     * 模型名称
     */
    private String model;
    
    /**
     * 最大token数
     */
    private Integer maxTokens;
    
    /**
     * 温度参数
     */
    private Double temperature;
    
    /**
     * top_p参数
     */
    private Double topP;
    
    /**
     * 请求超时时间(毫秒)
     */
    private Integer timeout;
}
