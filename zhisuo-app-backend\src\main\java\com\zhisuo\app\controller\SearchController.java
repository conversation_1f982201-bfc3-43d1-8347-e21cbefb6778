package com.zhisuo.app.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhisuo.app.common.Result;
import com.zhisuo.app.common.context.UserContext;
import com.zhisuo.app.dto.response.SearchResponse;
import com.zhisuo.app.dto.response.SearchSuggestionResponse;
import com.zhisuo.app.entity.HotTopic;
import com.zhisuo.app.entity.Article;
import com.zhisuo.app.service.SearchService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 搜索控制器
 */
@RestController
@RequestMapping("/v1/search")
public class SearchController {
    
    @Autowired
    private SearchService searchService;
    
    /**
     * 综合搜索（热点+文章）
     *
     * @param keyword 搜索关键词
     * @param type 内容类型（topic/article/all）
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    @GetMapping
    public Result<SearchResponse> search(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "all") String type,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        
        SearchResponse response = searchService.search(keyword, type, page, size);
        return Result.success(response);
    }
    
    /**
     * 搜索热点话题
     *
     * @param keyword 搜索关键词
     * @param page 页码
     * @param size 每页大小
     * @param source 来源筛选
     * @param tagId 标签筛选
     * @return 热点话题搜索结果
     */
    @GetMapping("/topics")
    public Result<Page<HotTopic>> searchTopics(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String source,
            @RequestParam(required = false) String tagId) {
        
        Page<HotTopic> result = searchService.searchTopics(keyword, page, size, source, tagId);
        return Result.success(result);
    }
    
    /**
     * 搜索文章
     *
     * @param keyword 搜索关键词
     * @param page 页码
     * @param size 每页大小
     * @param source 来源筛选
     * @param tagId 标签筛选
     * @return 文章搜索结果
     */
    @GetMapping("/articles")
    public Result<Page<Article>> searchArticles(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size,
            @RequestParam(required = false) String source,
            @RequestParam(required = false) String tagId) {
        
        Page<Article> result = searchService.searchArticles(keyword, page, size, source, tagId);
        return Result.success(result);
    }
    
    /**
     * 获取搜索建议
     *
     * @param keyword 搜索关键词前缀
     * @param limit 建议数量限制
     * @return 搜索建议列表
     */
    @GetMapping("/suggestions")
    public Result<SearchSuggestionResponse> getSearchSuggestions(
            @RequestParam String keyword,
            @RequestParam(defaultValue = "10") Integer limit) {
        
        SearchSuggestionResponse response = searchService.getSearchSuggestions(keyword, limit);
        return Result.success(response);
    }
    
    /**
     * 获取热门搜索词
     *
     * @param limit 数量限制
     * @return 热门搜索词列表
     */
    @GetMapping("/hot-keywords")
    public Result<List<String>> getHotKeywords(@RequestParam(defaultValue = "10") Integer limit) {
        List<String> hotKeywords = searchService.getHotKeywords(limit);
        return Result.success(hotKeywords);
    }
    
    /**
     * 记录搜索行为
     *
     * @param request 搜索记录请求
     * @return 结果
     */
    @PostMapping("/record")
    public Result<Void> recordSearch(@RequestBody Map<String, String> request) {
        String keyword = request.get("keyword");
        String type = request.getOrDefault("type", "all");

        searchService.recordSearch(keyword, type);
        return Result.success();
    }
    
    /**
     * 按标签搜索内容
     *
     * @param tagId 标签ID
     * @param type 内容类型
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    @GetMapping("/by-tag")
    public Result<SearchResponse> searchByTag(
            @RequestParam String tagId,
            @RequestParam(defaultValue = "all") String type,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {

        SearchResponse response = searchService.searchByTag(tagId, type, page, size);
        return Result.success(response);
    }

    /**
     * 获取用户搜索历史
     *
     * @param limit 数量限制
     * @return 搜索历史列表
     */
    @GetMapping("/history")
    public Result<List<String>> getUserSearchHistory(@RequestParam(defaultValue = "10") Integer limit) {
        String userId = UserContext.getUserId();
        List<String> history = searchService.getUserSearchHistory(userId, limit);
        return Result.success(history);
    }

    /**
     * 删除单个搜索历史记录
     *
     * @param request 删除请求
     * @return 结果
     */
    @DeleteMapping("/history")
    public Result<Void> deleteSearchHistory(@RequestBody Map<String, String> request) {
        String keyword = request.get("keyword");
        String userId = UserContext.getUserId();
        searchService.deleteUserSearchHistory(userId, keyword);
        return Result.success();
    }

    /**
     * 清空用户搜索历史
     *
     * @return 结果
     */
    @DeleteMapping("/history/all")
    public Result<Void> clearUserSearchHistory() {
        String userId = UserContext.getUserId();
        searchService.clearUserSearchHistory(userId);
        return Result.success();
    }
}
