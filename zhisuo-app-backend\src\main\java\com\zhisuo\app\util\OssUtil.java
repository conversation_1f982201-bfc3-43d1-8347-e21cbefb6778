package com.zhisuo.app.util;

import com.aliyun.oss.OSS;
import com.aliyun.oss.OSSClientBuilder;
import com.aliyun.oss.model.ObjectMetadata;
import com.zhisuo.app.config.OssConfig;
import org.apache.commons.io.FilenameUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.io.InputStream;
import java.util.UUID;

@Component
public class OssUtil {

    @Autowired
    private OssConfig ossConfig;

    /**
     * 上传文件到OSS
     * @param file 文件
     * @param dir 目录
     * @return 返回文件访问路径
     */
    public String uploadFile(MultipartFile file, String dir) throws IOException {
        // 获取原始文件名
        String originalFileName = file.getOriginalFilename();
        // 获取文件扩展名
        String ext = "." + FilenameUtils.getExtension(originalFileName);
        // 生成UUID作为文件名
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String fileName = uuid + ext;
        
        // 完整的文件路径
        String objectName = dir + fileName;
        
        // 创建OSS客户端
        OSS ossClient = new OSSClientBuilder().build(
                ossConfig.getEndpoint(),
                ossConfig.getAccessKeyId(),
                ossConfig.getAccessKeySecret());
        
        try {
            InputStream inputStream = file.getInputStream();
            
            // 创建上传Object的Metadata
            ObjectMetadata metadata = new ObjectMetadata();
            // 设置文件类型
            metadata.setContentType(getContentType(ext));
            // 设置文件大小
            metadata.setContentLength(file.getSize());
            
            // 上传文件
            ossClient.putObject(ossConfig.getBucketName(), objectName, inputStream, metadata);
            
            // 关闭OSSClient
            ossClient.shutdown();
            
            // 返回文件访问路径
            return "https://" + ossConfig.getBucketName() + "." + ossConfig.getEndpoint() + "/" + objectName;
        } catch (IOException e) {
            throw e;
        }
    }
    
    /**
     * 获取文件的ContentType
     * @param fileExtension 文件扩展名
     * @return ContentType
     */
    private String getContentType(String fileExtension) {
        if (fileExtension.equalsIgnoreCase(".jpg") || fileExtension.equalsIgnoreCase(".jpeg")) {
            return "image/jpeg";
        } else if (fileExtension.equalsIgnoreCase(".png")) {
            return "image/png";
        } else if (fileExtension.equalsIgnoreCase(".gif")) {
            return "image/gif";
        } else if (fileExtension.equalsIgnoreCase(".bmp")) {
            return "image/bmp";
        } else {
            return "application/octet-stream";
        }
    }
} 