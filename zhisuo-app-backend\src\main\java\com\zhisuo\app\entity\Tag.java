package com.zhisuo.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 标签实体类
 */
@Data
@TableName("tags")
public class Tag {
    
    @TableId
    private String tagId;       // 标签ID
    
    private String tagName;     // 标签名称
    
    private String icon;        // 标签图标
    
    private Date createTime;    // 创建时间
    
    private Integer status;     // 状态(1:启用,0:禁用)
} 