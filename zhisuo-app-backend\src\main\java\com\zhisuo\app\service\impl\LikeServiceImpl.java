package com.zhisuo.app.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.zhisuo.app.entity.UserLike;
import com.zhisuo.app.mapper.UserLikeMapper;
import com.zhisuo.app.service.ArticleService;
import com.zhisuo.app.service.CommentService;
import com.zhisuo.app.service.LikeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 点赞服务实现类
 */
@Slf4j
@Service
public class LikeServiceImpl extends ServiceImpl<UserLikeMapper, UserLike> implements LikeService {
    
    @Autowired
    private UserLikeMapper userLikeMapper;
    
    @Autowired
    private ArticleService articleService;
    
    @Autowired
    private CommentService commentService;
    
    @Override
    @Transactional
    public boolean toggleLike(String contentId, String contentType, String userId) {
        UserLike existingLike = userLikeMapper.selectByUserAndContent(userId, contentId, contentType);
        
        if (existingLike != null) {
            // 已点赞，取消点赞
            removeById(existingLike.getLikeId());
            updateContentLikeCount(contentId, contentType, -1);
            log.info("用户{}取消点赞: contentId={}, contentType={}", userId, contentId, contentType);
            return false;
        } else {
            // 未点赞，添加点赞
            UserLike userLike = new UserLike();
            userLike.setLikeId(UUID.randomUUID().toString().replace("-", ""));
            userLike.setUserId(userId);
            userLike.setContentId(contentId);
            userLike.setContentType(contentType);
            userLike.setCreateTime(new Date());
            
            save(userLike);
            updateContentLikeCount(contentId, contentType, 1);
            log.info("用户{}点赞: contentId={}, contentType={}", userId, contentId, contentType);
            return true;
        }
    }
    
    @Override
    public boolean isLiked(String contentId, String contentType, String userId) {
        UserLike userLike = userLikeMapper.selectByUserAndContent(userId, contentId, contentType);
        return userLike != null;
    }
    
    @Override
    public long getLikeCount(String contentId, String contentType) {
        return userLikeMapper.countByContent(contentId, contentType);
    }
    
    @Override
    public Map<String, Object> getUserLikes(String userId, String contentType, Integer page, Integer size) {
        Page<Map<String, Object>> likePage = new Page<>(page + 1, size);
        likePage = userLikeMapper.selectUserLikes(likePage, userId, contentType);
        
        Map<String, Object> result = new HashMap<>();
        result.put("content", likePage.getRecords());
        result.put("totalElements", likePage.getTotal());
        result.put("totalPages", likePage.getPages());
        result.put("size", likePage.getSize());
        result.put("number", likePage.getCurrent() - 1);
        
        return result;
    }
    
    /**
     * 更新内容的点赞数
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @param increment 增量
     */
    private void updateContentLikeCount(String contentId, String contentType, int increment) {
        switch (contentType) {
            case "article":
                articleService.updateLikeCount(contentId, increment);
                break;
            case "comment":
                commentService.updateLikeCount(contentId, increment);
                break;
            case "topic":
                // TODO: 实现话题点赞数更新
                break;
            default:
                log.warn("未知的内容类型: {}", contentType);
        }
    }
}
