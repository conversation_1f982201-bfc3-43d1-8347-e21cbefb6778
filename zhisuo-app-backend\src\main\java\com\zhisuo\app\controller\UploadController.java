package com.zhisuo.app.controller;

import com.zhisuo.app.common.ErrorCode;
import com.zhisuo.app.common.Result;
import com.zhisuo.app.common.context.UserContext;
import com.zhisuo.app.util.OssUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/v1/upload")
public class UploadController {

    @Autowired
    private OssUtil ossUtil;

    /**
     * 上传头像
     * @param file 头像文件
     * @return 返回上传结果
     */
    @PostMapping("/avatar")
    public Result<?> uploadAvatar(@RequestParam("file") MultipartFile file) {
        // 检查当前用户是否登录
        String userId = UserContext.getUserId();
        if (!StringUtils.hasText(userId)) {
            return Result.error(ErrorCode.UNAUTHORIZED);
        }
        
        // 检查文件是否为空
        if (file == null || file.isEmpty()) {
            return Result.error(5000, "上传文件不能为空");
        }
        
        // 检查文件类型
        String contentType = file.getContentType();
        if (contentType == null || !contentType.startsWith("image/")) {
            return Result.error(5000, "上传文件类型错误，仅支持图片格式");
        }
        
        // 检查文件大小，限制为2MB
        if (file.getSize() > 2 * 1024 * 1024) {
            return Result.error(5000, "上传图片大小不能超过2MB");
        }
        
        try {
            // 上传到OSS，保存在image/avatar/目录下
            String avatarUrl = ossUtil.uploadFile(file, "image/avatar/");
            
            // 返回上传结果
            Map<String, String> data = new HashMap<>();
            data.put("url", avatarUrl);
            return Result.success(data);
        } catch (IOException e) {
            e.printStackTrace();
            return Result.error(5000, "上传头像失败: " + e.getMessage());
        }
    }
} 