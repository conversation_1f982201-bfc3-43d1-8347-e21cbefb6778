package com.zhisuo.app.controller;

import com.zhisuo.app.common.Result;
import com.zhisuo.app.common.context.UserContext;
import com.zhisuo.app.dto.request.LikeRequest;
import com.zhisuo.app.service.LikeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.HashMap;
import java.util.Map;

/**
 * 点赞控制器
 */
@RestController
@RequestMapping("/v1/likes")
public class LikeController {
    
    @Autowired
    private LikeService likeService;
    
    /**
     * 切换点赞状态
     * 如果已点赞则取消点赞，如果未点赞则点赞
     *
     * @param request 点赞请求
     * @return 点赞状态
     */
    @PostMapping
    public Result<Map<String, Object>> toggleLike(@RequestBody @Valid LikeRequest request) {
        String userId = UserContext.getUserId();

        boolean isLiked = likeService.toggleLike(request.getContentId(), request.getContentType(), userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("isLiked", isLiked);
        result.put("message", isLiked ? "点赞成功" : "取消点赞成功");
        
        return Result.success(result);
    }
    
    /**
     * 检查用户是否已点赞
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 点赞状态
     */
    @GetMapping("/check")
    public Result<Map<String, Object>> checkLikeStatus(
            @RequestParam String contentId,
            @RequestParam String contentType) {
        
        String userId = UserContext.getUserId();

        boolean isLiked = likeService.isLiked(contentId, contentType, userId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("isLiked", isLiked);
        
        return Result.success(result);
    }
    
    /**
     * 获取内容的点赞数量
     *
     * @param contentId 内容ID
     * @param contentType 内容类型
     * @return 点赞数量
     */
    @GetMapping("/count")
    public Result<Map<String, Object>> getLikeCount(
            @RequestParam String contentId,
            @RequestParam String contentType) {
        
        long likeCount = likeService.getLikeCount(contentId, contentType);
        
        Map<String, Object> result = new HashMap<>();
        result.put("likeCount", likeCount);
        
        return Result.success(result);
    }
    
    /**
     * 获取用户点赞的内容列表
     *
     * @param contentType 内容类型（可选）
     * @param page 页码
     * @param size 每页大小
     * @return 点赞列表
     */
    @GetMapping("/my")
    public Result<Map<String, Object>> getMyLikes(
            @RequestParam(required = false) String contentType,
            @RequestParam(defaultValue = "0") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        
        String userId = UserContext.getUserId();

        Map<String, Object> result = likeService.getUserLikes(userId, contentType, page, size);
        
        return Result.success(result);
    }
}
