package com.zhisuo.app.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhisuo.app.dto.response.SearchResponse;
import com.zhisuo.app.dto.response.SearchSuggestionResponse;
import com.zhisuo.app.entity.Article;
import com.zhisuo.app.entity.HotTopic;

import java.util.List;

/**
 * 搜索服务接口
 */
public interface SearchService {
    
    /**
     * 综合搜索
     *
     * @param keyword 搜索关键词
     * @param type 搜索类型（topic/article/all）
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    SearchResponse search(String keyword, String type, Integer page, Integer size);
    
    /**
     * 搜索热点话题
     *
     * @param keyword 搜索关键词
     * @param page 页码
     * @param size 每页大小
     * @param source 来源筛选
     * @param tagId 标签筛选
     * @return 热点话题搜索结果
     */
    Page<HotTopic> searchTopics(String keyword, Integer page, Integer size, String source, String tagId);
    
    /**
     * 搜索文章
     *
     * @param keyword 搜索关键词
     * @param page 页码
     * @param size 每页大小
     * @param source 来源筛选
     * @param tagId 标签筛选
     * @return 文章搜索结果
     */
    Page<Article> searchArticles(String keyword, Integer page, Integer size, String source, String tagId);
    
    /**
     * 获取搜索建议
     *
     * @param keyword 搜索关键词前缀
     * @param limit 建议数量限制
     * @return 搜索建议
     */
    SearchSuggestionResponse getSearchSuggestions(String keyword, Integer limit);
    
    /**
     * 获取热门搜索词
     *
     * @param limit 数量限制
     * @return 热门搜索词列表
     */
    List<String> getHotKeywords(Integer limit);
    
    /**
     * 记录搜索行为
     *
     * @param keyword 搜索关键词
     * @param type 搜索类型
     */
    void recordSearch(String keyword, String type);
    
    /**
     * 按标签搜索内容
     *
     * @param tagId 标签ID
     * @param type 内容类型
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    SearchResponse searchByTag(String tagId, String type, Integer page, Integer size);
    
    /**
     * 获取用户搜索历史
     *
     * @param userId 用户ID
     * @param limit 数量限制
     * @return 搜索历史列表
     */
    List<String> getUserSearchHistory(String userId, Integer limit);
    
    /**
     * 清空用户搜索历史
     *
     * @param userId 用户ID
     */
    void clearUserSearchHistory(String userId);

    /**
     * 删除用户单个搜索历史记录
     *
     * @param userId 用户ID
     * @param keyword 搜索关键词
     */
    void deleteUserSearchHistory(String userId, String keyword);

    /**
     * 获取相关搜索建议
     *
     * @param keyword 搜索关键词
     * @param limit 数量限制
     * @return 相关搜索建议
     */
    List<String> getRelatedKeywords(String keyword, Integer limit);
}
