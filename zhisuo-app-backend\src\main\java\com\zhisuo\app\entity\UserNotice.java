package com.zhisuo.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 用户通知实体类
 */
@Data
@TableName("user_notices")
public class UserNotice {
    
    @TableId
    private String id;          // 主键
    
    private String userId;      // 用户ID
    
    private String noticeId;    // 通知ID
    
    private Integer isRead;     // 是否已读(1:已读,0:未读)
    
    private Date readTime;      // 阅读时间
} 