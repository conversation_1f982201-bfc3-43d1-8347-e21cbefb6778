<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>环形图测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            max-width: 500px;
            margin: 0 auto;
        }
        
        .ring-chart {
            position: relative;
            width: 200px;
            height: 200px;
            margin: 20px auto;
        }
        
        .ring-background {
            position: absolute;
            width: 100%;
            height: 100%;
            border-radius: 50%;
            top: 0;
            left: 0;
        }
        
        .ring-inner {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 120px;
            height: 120px;
            border-radius: 50%;
            background-color: #fff;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.05);
        }
        
        .ring-center-text {
            font-size: 18px;
            font-weight: bold;
            color: #3264ED;
        }
        
        .legend {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 15px;
            margin-top: 20px;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }
        
        .legend-text {
            font-size: 14px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h2>环形图测试</h2>
        <p>测试数据：娱乐(33%)、新闻(33%)、金融(33%)</p>
        
        <div class="ring-chart">
            <div class="ring-background" id="ringBackground"></div>
            <div class="ring-inner">
                <div class="ring-center-text">兴趣</div>
            </div>
        </div>
        
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background: #3264ED;"></div>
                <div class="legend-text">娱乐 (33%)</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #52C41A;"></div>
                <div class="legend-text">新闻 (33%)</div>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #FA8C16;"></div>
                <div class="legend-text">金融 (33%)</div>
            </div>
        </div>
    </div>

    <script>
        // 模拟兴趣数据
        const interests = [
            { name: '娱乐', percentage: 33, color: '#3264ED' },
            { name: '新闻', percentage: 33, color: '#52C41A' },
            { name: '金融', percentage: 34, color: '#FA8C16' }  // 总和为100%
        ];

        // 生成环形图样式
        function generateRingStyle(interests) {
            let gradientStops = [];
            let currentAngle = 0;

            interests.forEach((item) => {
                const segmentAngle = (item.percentage / 100) * 360;
                const startAngle = currentAngle;
                const endAngle = currentAngle + segmentAngle;

                gradientStops.push(`${item.color} ${startAngle}deg`);
                gradientStops.push(`${item.color} ${endAngle}deg`);

                currentAngle = endAngle;
            });

            // 如果总角度小于360度，填充剩余部分为透明
            if (currentAngle < 360) {
                gradientStops.push(`transparent ${currentAngle}deg`);
                gradientStops.push(`transparent 360deg`);
            }

            return `conic-gradient(from -90deg, ${gradientStops.join(', ')})`;
        }

        // 应用样式
        document.getElementById('ringBackground').style.background = generateRingStyle(interests);
    </script>
</body>
</html>
