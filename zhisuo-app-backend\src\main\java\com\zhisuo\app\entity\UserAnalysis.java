package com.zhisuo.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 用户分析报告实体类
 *
 * 字段映射关系：
 * - title: 分析标题 (对应文章标题)
 * - description: 分析描述 (对应分析后的摘要)
 * - content: 分析内容 (对应分析后的未来趋势、影响力评估以及相关话题)
 */
@Data
@TableName("user_analysis")
public class UserAnalysis {

    @TableId
    private String analysisId;    // 分析ID

    private String userId;        // 用户ID

    private String articleId;     // 文章ID (便于在mine.vue中查看最近分析详情)

    private String title;         // 分析标题 (对应文章标题)

    private String description;   // 分析描述 (对应分析后的摘要)

    private String content;       // 分析内容 (对应分析后的未来趋势、影响力评估以及相关话题)

    private Date createTime;      // 创建时间

    private Date updateTime;      // 更新时间

    private Integer status;       // 状态(1:显示,0:隐藏)
}