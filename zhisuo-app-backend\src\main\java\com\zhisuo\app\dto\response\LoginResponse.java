package com.zhisuo.app.dto.response;

import lombok.Data;

/**
 * 登录响应DTO
 */
@Data
public class LoginResponse {
    
    private String token;           // JWT令牌
    
    private String refreshToken;    // 刷新令牌
    
    private Long expiresIn;         // 令牌有效期(秒)
    
    private Boolean isNewUser;      // 是否新用户
    
    private Boolean needSetPassword; // 是否需要设置密码
    
    private UserInfoResponse userInfo;    // 用户基本信息
} 