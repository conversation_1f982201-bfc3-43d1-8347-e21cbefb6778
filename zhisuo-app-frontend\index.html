<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <script>
      var coverSupport = 'CSS' in window && typeof CSS.supports === 'function' && (CSS.supports('top: env(a)') ||
        CSS.supports('top: constant(a)'))
      document.write(
        '<meta name="viewport" content="width=device-width, user-scalable=no, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0' +
        (coverSupport ? ', viewport-fit=cover' : '') + '" />')
    </script>
    <title>智索APP</title>
    <!-- favicon设置 -->
    <link rel="icon" type="image/png" sizes="32x32" href="/static/logo.png?v=3">
    <link rel="icon" type="image/png" sizes="16x16" href="/static/logo.png?v=3">
    <link rel="shortcut icon" type="image/png" href="/static/logo.png?v=3">
    <!-- 预加载uni-icons字体文件 -->
    <link rel="preload" href="/uni_modules/uni-icons/components/uni-icons/uniicons.ttf" as="font" type="font/ttf" crossorigin>
    <!--preload-links-->
    <!--app-context-->
  </head>
  <body>
    <div id="app"><!--app-html--></div>
    <script type="module" src="/main.js"></script>
  </body>
</html>
