<template>
	<view class="page">
		<!-- 自定义导航栏 -->
		<view class="navbar">
			<view class="navbar-content">
				<view class="navbar-left" @click="goBack">
					<uni-icons type="left" size="20" color="#333"></uni-icons>
				</view>
				<view class="navbar-title">分析详情</view>
				<view class="navbar-right">
					<view class="share-btn" @click="shareAnalysis">
						<uni-icons type="redo" size="18" color="#333"></uni-icons>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view class="loading-container" v-if="loading">
			<view class="loading-content">
				<uni-icons type="spinner-cycle" size="40" color="#2979ff"></uni-icons>
				<text class="loading-text">正在加载分析详情...</text>
			</view>
		</view>

		<!-- 错误状态 -->
		<view class="error-container" v-else-if="error">
			<view class="error-content">
				<uni-icons type="closeempty" size="50" color="#ff4d4f"></uni-icons>
				<text class="error-text">{{ error }}</text>
				<view class="retry-btn" @click="loadAnalysisDetail">
					<uni-icons type="reload" size="16" color="#fff"></uni-icons>
					<text>重新加载</text>
				</view>
			</view>
		</view>

		<!-- 分析内容 -->
		<view class="content" v-else-if="analysis">
			<!-- 分析标题卡片 -->
			<view class="title-card">
				<view class="title-content">
					<view class="analysis-title">{{ analysis.title }}</view>
					<view class="analysis-meta">
						<view class="meta-item">
							<uni-icons type="calendar" size="14" color="#666"></uni-icons>
							<text>{{ formatTime(analysis.createTime) }}</text>
						</view>
						<view class="meta-item" v-if="analysis.updateTime && analysis.updateTime !== analysis.createTime">
							<uni-icons type="reload" size="14" color="#666"></uni-icons>
							<text>更新于 {{ formatTime(analysis.updateTime) }}</text>
						</view>
					</view>
				</view>
			</view>

			<!-- 分析内容区域 -->
			<view class="analysis-content">
				<!-- 内容摘要 -->
				<view class="content-card" v-if="analysis.description">
					<view class="card-header">
						<text class="header-title">内容摘要</text>
					</view>
					<view class="card-content">
						<text class="summary-text">{{ analysis.description }}</text>
					</view>
				</view>

				<!-- 智能分析 -->
				<view class="content-card" v-if="analysis.content">
					<view class="card-header">
						<text class="header-title">智能分析</text>
					</view>
					<view class="card-content">
						<view class="analysis-sections">
							<view
								v-for="(section, index) in parsedContent"
								:key="index"
								class="analysis-section"
								:class="getAnalysisSectionClass(section)"
							>
								<view class="section-header">
									<view class="section-icon"></view>
									<text>{{ section.title }}</text>
								</view>
								<view class="section-content" :class="getAnalysisContentClass(section)">
									<text v-if="section.title === '关键词'" class="keyword-tag"
										v-for="(keyword, idx) in section.content.split('、')" :key="idx">
										{{ keyword.trim() }}
									</text>
									<text v-else-if="section.title === '未来趋势'" class="trends-text">{{ section.content }}</text>
									<text v-else class="section-text">{{ section.content }}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>

			<!-- 操作按钮 -->
			<view class="action-buttons">
				<view class="action-btn primary" @click="viewOriginalArticle" v-if="analysis.article_id || analysis.articleId">
					<view class="btn-icon">
						<uni-icons type="eye" size="18" color="#fff"></uni-icons>
					</view>
					<text>查看原文</text>
				</view>
				<view class="action-btn secondary" @click="deleteAnalysis">
					<view class="btn-icon">
						<uni-icons type="trash" size="18" color="#ff4d4f"></uni-icons>
					</view>
					<text>删除分析</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import Api from '../../common/api.js';

	export default {
		data() {
			return {
				analysisId: '',
				analysis: null,
				loading: false,
				error: null,
				fromPage: 'mine' // 记录来源页面，默认为mine
			}
		},
		onLoad(options) {
			if (options.id) {
				this.analysisId = options.id;
				// 记录来源页面
				this.fromPage = options.from || 'mine';
				this.loadAnalysisDetail();
			} else {
				this.error = '分析ID不存在';
			}
		},
		computed: {
			// 解析分析内容为结构化数据
			parsedContent() {
				if (!this.analysis || !this.analysis.content) {
					return [];
				}

				try {
					// 尝试解析JSON格式的内容
					const content = JSON.parse(this.analysis.content);
					if (Array.isArray(content)) {
						return content;
					}
				} catch (e) {
					// 如果不是JSON格式，解析带标签的内容
					return this.parseTaggedContent(this.analysis.content);
				}

				return [];
			}
		},
		methods: {
			// 解析带标签的内容
			parseTaggedContent(content) {
				const sections = [];
				const lines = content.split('\n');
				let currentSection = null;

				for (let line of lines) {
					line = line.trim();
					if (!line) continue;

					// 检查是否是标签行（如【关键词】、【未来趋势】等）
					const tagMatch = line.match(/^【(.+?)】$/);
					if (tagMatch) {
						// 如果有当前段落，先保存
						if (currentSection) {
							sections.push(currentSection);
						}
						// 开始新段落
						currentSection = {
							title: this.getTagTitle(tagMatch[1]),
							content: '',
							tag: tagMatch[1]
						};
					} else if (currentSection) {
						// 添加内容到当前段落
						if (currentSection.content) {
							currentSection.content += '\n';
						}
						currentSection.content += line;
					}
				}

				// 保存最后一个段落
				if (currentSection) {
					sections.push(currentSection);
				}

				// 如果没有找到标签，按原来的方式分割
				if (sections.length === 0) {
					const paragraphs = content.split('\n\n');
					return paragraphs.map((paragraph, index) => ({
						title: `段落 ${index + 1}`,
						content: paragraph.trim()
					})).filter(item => item.content);
				}

				return sections;
			},

			// 获取标签对应的显示标题
			getTagTitle(tag) {
				const tagMap = {
					'关键词': '关键词',
					'未来趋势': '未来趋势',
					'影响力评估': '影响力评估',
					'相关话题': '相关话题'
				};
				return tagMap[tag] || tag;
			},

			// 获取分析区块样式类
			getAnalysisSectionClass(section) {
				if (section.title === '关键词') return 'keywords-section';
				if (section.title === '未来趋势') return 'trends-section';
				return '';
			},

			// 获取分析内容样式类
			getAnalysisContentClass(section) {
				if (section.title === '关键词') return 'keywords-list';
				if (section.title === '未来趋势') return 'trends-content';
				return 'section-content';
			},
			// 返回上一页
			goBack() {
				uni.navigateBack({
					delta: 1
				});
			},

			// 加载分析详情
			async loadAnalysisDetail() {
				this.loading = true;
				this.error = null;

				try {
					const response = await Api.request({
						url: `/v1/ai/analysis/${this.analysisId}`,
						method: 'GET'
					});

					if (response.data && response.data.code === 0 && response.data.data) {
						this.analysis = response.data.data;
					} else {
						throw new Error(response.data?.message || '获取分析详情失败');
					}
				} catch (error) {
					console.error('获取分析详情失败:', error);
					this.error = error.message || '网络错误，请稍后重试';
				} finally {
					this.loading = false;
				}
			},

			// 查看原文章
			viewOriginalArticle() {
				// 兼容不同的字段名格式
				const articleId = this.analysis?.article_id || this.analysis?.articleId;
				if (!this.analysis || !articleId) {
					uni.showToast({
						title: '原文章不存在',
						icon: 'none'
					});
					return;
				}

				// 检查文章详情页面是否存在
				uni.navigateTo({
					url: `/pages/article/detail?id=${articleId}`,
					fail: (err) => {
						console.error('跳转到文章详情页失败:', err);
						uni.showToast({
							title: '页面跳转失败',
							icon: 'none'
						});
					}
				});
			},

			// 删除分析
			deleteAnalysis() {
				uni.showModal({
					title: '确认删除',
					content: '确定要删除这个分析吗？删除后无法恢复。',
					success: async (res) => {
						if (res.confirm) {
							try {
								const response = await Api.request({
									url: `/v1/ai/analysis/${this.analysisId}`,
									method: 'DELETE'
								});

								if (response.data && response.data.code === 0) {
									uni.showToast({
										title: '删除成功',
										icon: 'success'
									});

									// 根据来源页面进行智能跳转
									setTimeout(() => {
										this.handleDeleteNavigation();
									}, 1500);
								} else {
									throw new Error(response.data?.message || '删除失败');
								}
							} catch (error) {
								console.error('删除分析失败:', error);
								uni.showToast({
									title: error.message || '删除失败',
									icon: 'none'
								});
							}
						}
					}
				});
			},

			// 处理删除后的导航逻辑
			handleDeleteNavigation() {
				if (this.fromPage === 'list') {
					// 从分析列表进入，跳转回列表页并刷新
					uni.redirectTo({
						url: '/pages/analysis/list?refresh=true'
					});
				} else {
					// 从个人中心进入，跳转回个人中心
					// 使用事件通知来触发刷新
					uni.$emit('refreshAnalysis');
					uni.switchTab({
						url: '/pages/mine/mine'
					});
				}
			},

			// 分享分析
			shareAnalysis() {
				uni.showActionSheet({
					itemList: ['复制链接', '保存图片'],
					success: (res) => {
						if (res.tapIndex === 0) {
							// 复制链接逻辑
							uni.showToast({
								title: '链接已复制',
								icon: 'success'
							});
						} else if (res.tapIndex === 1) {
							// 保存图片逻辑
							uni.showToast({
								title: '功能开发中',
								icon: 'none'
							});
						}
					}
				});
			},

			// 格式化时间
			formatTime(time) {
				if (!time) return '';
				const date = new Date(time);
				return date.toLocaleString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit'
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	/* 页面基础样式 */
	.page {
		background-color: #f2f5fc;
		min-height: 100vh;

		// 确保所有文本清晰可见
		text {
			text-shadow: none !important;
		}
	}

	/* 导航栏样式 */
	.navbar {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		z-index: 1000;
		background: #fff;
		padding-top: var(--status-bar-height);
		border-bottom: 1px solid #f0f0f0;

		.navbar-content {
			display: flex;
			align-items: center;
			justify-content: space-between;
			height: 88rpx;
			padding: 0 30rpx;

			.navbar-left {
				width: 60rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				background-color: #f5f5f5;
				transition: all 0.3s ease;

				&:active {
					background-color: #e6e6e6;
					transform: scale(0.95);
				}
			}

			.navbar-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
			}

			.navbar-right {
				width: 60rpx;
				height: 60rpx;
				display: flex;
				align-items: center;
				justify-content: center;

				.share-btn {
					width: 60rpx;
					height: 60rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					border-radius: 50%;
					background-color: #f5f5f5;
					transition: all 0.3s ease;

					&:active {
						background-color: #e6e6e6;
						transform: scale(0.95);
					}
				}
			}
		}
	}

	/* 加载和错误状态 */
	.loading-container,
	.error-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		min-height: 60vh;
		padding: 40rpx;

		.loading-content,
		.error-content {
			display: flex;
			flex-direction: column;
			align-items: center;
			background: rgba(255, 255, 255, 0.9);
			backdrop-filter: blur(20px);
			border-radius: 24rpx;
			padding: 60rpx 40rpx;
			box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
		}

		.loading-text,
		.error-text {
			margin-top: 24rpx;
			font-size: 28rpx;
			color: #666;
			text-align: center;
		}

		.retry-btn {
			margin-top: 30rpx;
			background: linear-gradient(135deg, #2979ff, #722ED1);
			color: #fff;
			padding: 20rpx 40rpx;
			border-radius: 50rpx;
			font-size: 28rpx;
			font-weight: bold;
			display: flex;
			align-items: center;
			gap: 12rpx;
			box-shadow: 0 6rpx 20rpx rgba(41, 121, 255, 0.3);
			transition: all 0.3s ease;

			&:active {
				transform: scale(0.95);
			}
		}
	}

	/* 内容区域 */
	.content {
		padding-top: calc(var(--status-bar-height) + 88rpx + 20rpx);
		padding-bottom: 40rpx;
	}

	/* 标题卡片 */
	.title-card {
		margin: 20rpx;
		border-radius: 16rpx;
		background: #fff;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

		.title-content {
			padding: 24rpx 30rpx;

			.analysis-title {
				font-size: 30rpx;
				font-weight: bold;
				color: #333;
				line-height: 1.4;
				margin-bottom: 12rpx;
			}

			.analysis-meta {
				display: flex;
				flex-wrap: wrap;
				gap: 16rpx;

				.meta-item {
					display: flex;
					align-items: center;
					gap: 6rpx;
					font-size: 24rpx;
					color: #666;
				}
			}
		}
	}

	/* 分析内容 */
	.analysis-content {
		padding: 0 20rpx;

		.content-card {
			background-color: #fff;
			border-radius: 20rpx;
			margin-bottom: 24rpx;
			overflow: hidden;
			box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);

			.card-header {
				padding: 24rpx 30rpx;
				border-bottom: 1px solid #f0f0f0;

				.header-title {
					font-size: 30rpx;
					font-weight: bold;
					color: #333;
					position: relative;
					padding-left: 20rpx;

					&::before {
						content: '';
						position: absolute;
						left: 0;
						top: 50%;
						transform: translateY(-50%);
						width: 6rpx;
						height: 28rpx;
						background: linear-gradient(to bottom, #722ED1, #3264ED);
						border-radius: 3rpx;
					}
				}
			}

			.card-content {
				padding: 24rpx 30rpx 24rpx 40rpx;
				position: relative;
				z-index: 1;

				.summary-text {
					font-size: 28rpx;
					color: #333;
					line-height: 3;
					background: #f8faff;
					padding: 20rpx 20rpx 20rpx 20rpx;
					margin-left: 10rpx;
					border-radius: 12rpx;
					border-left: 4rpx solid #722ED1;
					border-right: 4rpx solid #722ED1;
					text-shadow: none;
					box-shadow: none;
				}

				.analysis-sections {
					.analysis-section {
						margin-bottom: 20rpx;
						position: relative;
						z-index: 2;

						&:last-child {
							margin-bottom: 0;
						}

						.section-header {
							font-size: 28rpx;
							font-weight: bold;
							color: #333;
							margin-bottom: 12rpx;
							display: flex;
							align-items: center;
							gap: 8rpx;

							.section-icon {
								width: 6rpx;
								height: 20rpx;
								background: linear-gradient(to bottom, #722ED1, #3264ED);
								border-radius: 3rpx;
							}
						}

						.section-text {
							font-size: 26rpx;
							color: #333;
							line-height: 1.6;
							text-shadow: none;
						}

						// 关键词样式
						&.keywords-section {
							.section-content {
								display: flex;
								flex-wrap: wrap;
								gap: 10rpx;
							}

							.keyword-tag {
								background: #f0f6ff;
								color: #3264ED;
								padding: 6rpx 14rpx;
								border-radius: 14rpx;
								font-size: 24rpx;
								border: 1px solid #e0ecff;
								transition: all 0.3s ease;

								&:active {
									background: #e0ecff;
									transform: scale(0.95);
								}
							}
						}

						// 趋势预测样式
						&.trends-section {
							.trends-text {
								font-size: 26rpx;
								color: #333;
								line-height: 1.6;
								background: #fff8e1;
								padding: 16rpx;
								margin-left: 10rpx;
								border-radius: 10rpx;
								border-left: 3rpx solid #faad14;
								border-right: 3rpx solid #faad14;
								box-shadow: none;
								text-shadow: none;
							}
						}

						// 趋势内容样式
						.trends-content {
							.trends-text {
								font-size: 26rpx;
								color: #333;
								line-height: 3;
								background: #fff8e1;
								padding: 16rpx;
								margin-left: 10rpx;
								border-radius: 10rpx;
								border-left: 3rpx solid #faad14;
								box-shadow: none;
								text-shadow: none;
							}
						}
					}
				}
			}
		}
	}

	/* 操作按钮 */
	.action-buttons {
		display: flex;
		gap: 16rpx;
		padding: 24rpx 20rpx;
		margin-bottom: 30rpx;

		.action-btn {
			flex: 1;
			display: flex;
			align-items: center;
			justify-content: center;
			gap: 8rpx;
			height: 80rpx;
			border-radius: 40rpx;
			font-size: 26rpx;
			font-weight: 500;
			transition: all 0.3s ease;

			&.primary {
				background: linear-gradient(135deg, #722ED1, #3264ED);
				color: #fff;
				box-shadow: 0 4rpx 12rpx rgba(114, 46, 209, 0.3);

				&:active {
					transform: scale(0.95);
				}
			}

			&.secondary {
				background: #fff;
				color: #ff4d4f;
				border: 1px solid #ff4d4f;

				&:active {
					background: #fff2f0;
					transform: scale(0.95);
				}
			}
		}
	}
</style>

