package com.zhisuo.app.dto.response;

import com.zhisuo.app.entity.HotTopic;
import com.zhisuo.app.entity.Tag;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 包含标签的热点话题响应DTO
 */
@Data
public class HotTopicWithTagsResponse {
    
    private String topicId;       // 话题ID
    private String title;         // 话题标题
    private String description;   // 话题描述
    private String source;        // 来源平台
    private String sourceUrl;     // 来源URL
    private String hotValue;      // 热度值
    private Integer viewCount;    // 阅读量
    private Integer searchCount;  // 搜索量
    private Integer trend;        // 趋势(1:上升,0:持平,-1:下降)
    private Integer rank;         // 热榜排名
    private Date collectTime;     // 收集时间
    private Date updateTime;      // 更新时间
    private Integer status;       // 状态
    
    // 标签信息
    private List<String> tags;    // 标签名称列表
    private List<Tag> tagDetails; // 标签详细信息列表（可选）

    // 用户状态信息
    private Boolean isFavorited;  // 是否已收藏
    
    /**
     * 从HotTopic实体转换为响应DTO
     * 
     * @param hotTopic 热点话题实体
     * @return 响应DTO
     */
    public static HotTopicWithTagsResponse fromEntity(HotTopic hotTopic) {
        if (hotTopic == null) {
            return null;
        }
        
        HotTopicWithTagsResponse response = new HotTopicWithTagsResponse();
        response.setTopicId(hotTopic.getTopicId());
        response.setTitle(hotTopic.getTitle());
        response.setDescription(hotTopic.getDescription());
        response.setSource(hotTopic.getSource());
        response.setSourceUrl(hotTopic.getSourceUrl());
        response.setHotValue(hotTopic.getHotValue());
        response.setViewCount(hotTopic.getViewCount());
        response.setSearchCount(hotTopic.getSearchCount());
        response.setTrend(hotTopic.getTrend());
        response.setRank(hotTopic.getRank());
        response.setCollectTime(hotTopic.getCollectTime());
        response.setUpdateTime(hotTopic.getUpdateTime());
        response.setStatus(hotTopic.getStatus());
        
        return response;
    }
    
    /**
     * 从HotTopic实体和标签列表转换为响应DTO
     *
     * @param hotTopic 热点话题实体
     * @param tags 标签列表
     * @return 响应DTO
     */
    public static HotTopicWithTagsResponse fromEntityWithTags(HotTopic hotTopic, List<Tag> tags) {
        HotTopicWithTagsResponse response = fromEntity(hotTopic);
        if (response != null && tags != null) {
            response.setTagDetails(tags);
            response.setTags(tags.stream()
                    .map(Tag::getTagName)
                    .collect(java.util.stream.Collectors.toList()));
        }
        return response;
    }

    /**
     * 从HotTopic实体、标签列表和收藏状态转换为响应DTO
     *
     * @param hotTopic 热点话题实体
     * @param tags 标签列表
     * @param isFavorited 是否已收藏
     * @return 响应DTO
     */
    public static HotTopicWithTagsResponse fromEntityWithTagsAndFavorite(HotTopic hotTopic, List<Tag> tags, Boolean isFavorited) {
        HotTopicWithTagsResponse response = fromEntityWithTags(hotTopic, tags);
        if (response != null) {
            response.setIsFavorited(isFavorited);
        }
        return response;
    }
}
