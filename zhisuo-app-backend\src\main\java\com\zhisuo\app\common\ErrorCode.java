package com.zhisuo.app.common;

import lombok.Getter;

/**
 * 错误码枚举
 */
@Getter
public enum ErrorCode {
    
    // 通用错误码
    SUCCESS(0, "success"),
    SYSTEM_ERROR(1000, "System error"),
    SERVICE_UNAVAILABLE(1001, "Service unavailable"),
    INVALID_PARAMETER(2000, "Invalid parameter"),
    MISSING_PARAMETER(2001, "Missing required parameter"),
    UNAUTHORIZED(3000, "Unauthorized"),
    TOKEN_EXPIRED(3001, "Token expired"),
    INVALID_TOKEN(3002, "Invalid token"),
    ACCESS_DENIED(3003, "Access denied"),
    NOT_LOGIN(3004, "User not logged in"),
    RESOURCE_NOT_FOUND(4000, "Resource not found"),
    USER_NOT_FOUND(4001, "User not found"),
    
    // 业务错误码
    BUSINESS_ERROR(5000, "Business error"),
    PHONE_ALREADY_REGISTERED(5001, "Phone already registered"),
    INVALID_VERIFICATION_CODE(5002, "Invalid verification code"),
    VERIFICATION_CODE_EXPIRED(5003, "Verification code expired"),
    PASSWORD_ERROR(5004, "Password error"),
    OPERATION_TOO_FREQUENT(5005, "Operation too frequent");
    
    private final Integer code;
    private final String message;
    
    ErrorCode(Integer code, String message) {
        this.code = code;
        this.message = message;
    }
} 