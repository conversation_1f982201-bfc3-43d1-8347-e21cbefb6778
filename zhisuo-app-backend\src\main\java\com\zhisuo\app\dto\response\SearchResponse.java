package com.zhisuo.app.dto.response;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.zhisuo.app.entity.Article;
import com.zhisuo.app.entity.HotTopic;
import lombok.Data;

import java.util.List;

/**
 * 搜索响应DTO
 */
@Data
public class SearchResponse {
    
    /**
     * 搜索关键词
     */
    private String keyword;
    
    /**
     * 搜索类型
     */
    private String type;
    
    /**
     * 总结果数
     */
    private Long totalCount;
    
    /**
     * 热点话题搜索结果
     */
    private Page<HotTopic> topics;
    
    /**
     * 文章搜索结果
     */
    private Page<Article> articles;
    
    /**
     * 相关搜索建议
     */
    private List<String> relatedKeywords;
    
    /**
     * 搜索统计信息
     */
    private SearchStats stats;
    
    /**
     * 搜索统计信息内部类
     */
    @Data
    public static class SearchStats {
        /**
         * 热点话题数量
         */
        private Long topicCount;
        
        /**
         * 文章数量
         */
        private Long articleCount;
        
        /**
         * 搜索耗时（毫秒）
         */
        private Long searchTime;
        
        /**
         * 是否有更多结果
         */
        private Boolean hasMore;
    }
}
