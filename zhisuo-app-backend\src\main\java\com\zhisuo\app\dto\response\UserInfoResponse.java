package com.zhisuo.app.dto.response;

import lombok.Data;

/**
 * 用户基本信息DTO
 */
@Data
public class UserInfoResponse {
    
    private String userId;       // 用户ID

    private String phone;       // 电话号码
    
    private String nickname;     // 用户昵称
    
    private String avatar;       // 头像URL
    
    private Integer memberLevel; // 会员等级
    
    /**
     * 从User实体转换为DTO
     * 
     * @param user 用户实体
     * @return UserInfoResponse
     */
    public static UserInfoResponse fromUser(com.zhisuo.app.entity.User user) {
        if (user == null) {
            return null;
        }
        
        UserInfoResponse dto = new UserInfoResponse();
        dto.setUserId(user.getUserId());
        dto.setPhone(user.getPhone());
        dto.setNickname(user.getNickname());
        dto.setAvatar(user.getAvatar());
        dto.setMemberLevel(user.getMemberLevel());
        
        return dto;
    }
} 