// API配置文件
import mockApi from './mockApi.js';

// 是否使用模拟API
const USE_MOCK = false; // 设置为true使用模拟API，false使用真实API

const BASE_URL = process.env.NODE_ENV === 'development' 
  ? 'http://localhost:8080' // 开发环境API地址
  : 'https://api.zhisuo.com'; // 生产环境API地址

// 请求拦截器
const requestInterceptor = (config) => {
  // 添加token
  const token = uni.getStorageSync('token');
  if (token) {
    config.header = {
      ...config.header,
      'Authorization': `Bearer ${token}`
    };
  }

  // 添加基础URL
  if (!config.url.startsWith('http')) {
    config.url = BASE_URL + config.url;
  }

  // 处理GET请求的参数
  const method = (config.method || 'GET').toUpperCase();
  if (method === 'GET' && config.data) {
    // 将data参数转换为查询字符串
    const params = new URLSearchParams();
    Object.keys(config.data).forEach(key => {
      if (config.data[key] !== undefined && config.data[key] !== null) {
        params.append(key, config.data[key]);
      }
    });
    const queryString = params.toString();
    if (queryString) {
      config.url += (config.url.includes('?') ? '&' : '?') + queryString;
    }
    // 清除data，因为GET请求不应该有body
    delete config.data;
  }

  // 开发环境日志
  if (process.env.NODE_ENV === 'development') {
    console.log(`${method} 请求:`, config.url);
    if (config.data) {
      console.log('请求数据:', config.data);
    }
  }

  return config;
};

// 响应拦截器
const responseInterceptor = (response) => {
  // 开发环境日志
  if (process.env.NODE_ENV === 'development') {
    console.log('响应状态:', response.statusCode);
    console.log('响应数据:', response.data);
  }
  
  // 如果返回401，可能是token过期，尝试使用refreshToken
  if (response.statusCode === 401) {
    // 可以在这里实现自动刷新token的逻辑
    const refreshToken = uni.getStorageSync('refreshToken');
    if (refreshToken) {
      // 自动刷新token的逻辑
      // ...
    } else {
      // 跳转到登录页
      uni.redirectTo({
        url: '/pages/login/login'
      });
    }
  }
  
  return response;
};

// 封装请求方法
const request = (options) => {
  // 如果使用模拟API，直接返回模拟数据
  if (USE_MOCK) {
    console.log('使用模拟API:', options.url);
    return mockApi.request(options);
  }
  
  // 应用请求拦截器
  const config = requestInterceptor(options);
  
  return new Promise((resolve, reject) => {
    uni.request({
      ...config,
      success: (res) => {
        // 应用响应拦截器
        const response = responseInterceptor(res);
        resolve(response);
      },
      fail: (err) => {
        console.error('请求失败:', err);
        reject(err);
      }
    });
  });
};

export default {
  BASE_URL,
  request,
  USE_MOCK
}; 