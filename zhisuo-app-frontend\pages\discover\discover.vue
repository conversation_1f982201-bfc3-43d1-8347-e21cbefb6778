<template>
	<view class="page">
		<!-- 顶部导航栏 -->
		<view class="navbar">
			<view class="title">热点发现</view>
		</view>
		
		<!-- 搜索框 -->
		<view class="search-box">
			<view class="search-input" @click="focusSearch">
				<!-- 使用uni-icons的搜索图标 -->
				<uni-icons type="search" size="18" color="#722ED1"></uni-icons>
				<!-- 备选方案：如果uni-icons有问题，可以取消注释下面这行 -->
				<!-- <CustomIcon type="search" size="18" color="#722ED1"></CustomIcon> -->
				<input 
					class="input" 
					type="text" 
					v-model="searchKeyword" 
					placeholder="搜索热点话题" 
					confirm-type="search"
					@confirm="doSearch"
					@input="onSearchInput"
					ref="searchInput"
				/>
				<view v-if="searchKeyword" class="clear-btn" @click.stop="clearSearch">
					<CustomIcon type="clear" size="16" color="#999"></CustomIcon>
				</view>
			</view>

			<!-- 搜索建议 -->
			<view v-if="searchSuggestions.length > 0 && searchKeyword && !showSearchResults" class="search-suggestions">
				<view
					v-for="(suggestion, index) in searchSuggestions"
					:key="index"
					class="suggestion-item"
					:class="{ 'topic-suggestion': suggestion.type === 'topic' }"
					@click="selectSuggestion(suggestion)"
				>
					<uni-icons
						:type="suggestion.type === 'topic' ? 'fire' : (suggestion.type === 'hot_keyword' ? 'star' : 'search')"
						size="14"
						:color="suggestion.type === 'topic' ? '#722ED1' : '#999'"
					></uni-icons>
					<text class="suggestion-text">{{ suggestion.displayText }}</text>
					<view v-if="suggestion.type === 'topic'" class="topic-info">
						<text class="hot-value">{{ suggestion.hotValue }}</text>
					</view>
				</view>
			</view>


		</view>

		<!-- 主内容区 -->
		<view class="content-container">
			<!-- 搜索结果区域 -->
			<view class="search-results" v-if="showSearchResults">
				<!-- 相关热点列表 -->
				<view class="card related-hot-list" v-if="relatedHotList.length > 0">
					<view class="card-header">
						<view class="title-area">
							<uni-icons type="star-filled" size="16" color="#3264ED"></uni-icons>
							<text class="title">相关热点</text>
						</view>
					</view>
					
					<view class="hot-item-container">
						<view class="hot-item" v-for="(item, index) in relatedHotList" :key="index" @click="navigateToHotDetail(item.id)">
							<view class="hot-item-header">
								<text class="hot-title">{{item.title}}</text>
								<view class="hot-tags">
									<text class="tag" v-for="(tag, tagIndex) in item.tags" :key="tagIndex">{{tag}}</text>
								</view>
							</view>
							<view class="hot-item-content">
								<text class="hot-desc">{{item.description}}</text>
							</view>
							<view class="hot-item-footer">
								<view class="hot-stats">
									<view class="stat-item">
										<uni-icons type="eye" size="14" color="#999"></uni-icons>
										<text>{{item.viewCount}}</text>
									</view>
									<view class="stat-item">
										<uni-icons type="chat" size="14" color="#999"></uni-icons>
										<text>{{item.commentCount}}</text>
									</view>
								</view>
								<view class="hot-actions">
									<view class="action-btn" @click.stop="toggleFavorite(item)">
										<uni-icons :type="item.isFavorite ? 'star-filled' : 'star'" size="16" :color="item.isFavorite ? '#FFB800' : '#999'"></uni-icons>
									</view>
									<view class="action-btn" @click.stop="toggleLike(item)">
										<uni-icons :type="item.isLiked ? 'heart-filled' : 'heart'" size="16" :color="item.isLiked ? '#FF5151' : '#999'"></uni-icons>
									</view>
									<view class="action-btn">
										<uni-icons type="more-filled" size="16" color="#999"></uni-icons>
									</view>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 未搜索时显示的内容 -->
			<view v-else>
				<!-- 历史搜索记录 -->
				<view class="card history-section" v-if="historyList.length > 0">
					<view class="card-header">
						<view class="title-area">
							<uni-icons type="time" size="16" color="#3264ED"></uni-icons>
							<text class="title">历史搜索</text>
						</view>
						<view class="action-area">
							<text class="action-btn" @click="clearAllHistory">清空</text>
							<text class="divider">|</text>
							<view class="action-btn-wrapper" @click="toggleExpandHistory" v-if="historyList.length > 2">
								<text class="action-btn">{{ isHistoryExpanded ? '收起' : '展开' }}</text>
								<CustomIcon :type="isHistoryExpanded ? 'top' : 'bottom'" size="12" color="#999"></CustomIcon>
							</view>
						</view>
					</view>
					<view class="history-list">
						<view
							class="history-item"
							:class="{
								'expanded': expandedHistoryIndex === index,
								'long-press-active': longPressActiveIndex === index
							}"
							v-for="(item, index) in displayHistoryList"
							:key="index"
						>
							<text
								class="item-text"
								:class="{ 'expanded': expandedHistoryIndex === index }"
								@click="handleHistoryClick(item, index)"
								@longpress.500="toggleFullText(index)"
							>{{item}}</text>
							<view class="delete-btn" @click.stop="deleteHistory(historyList.indexOf(item))">
								<CustomIcon type="close" size="14" color="#666"></CustomIcon>
							</view>
						</view>
					</view>
				</view>
				
				<!-- 热搜榜 -->
				<view class="card">
					<view class="card-header">
						<view class="title-area">
							<uni-icons type="fire-filled" size="16" color="#722ED1"></uni-icons>
							<text class="title">热搜榜</text>
						</view>
						<view class="action-area">
							<view class="refresh-btn" :class="{ 'refreshing': isRefreshing }" @click="refreshHotList">
								<uni-icons type="reload" size="16" color="#722ED1"></uni-icons>
							</view>
							<text class="update-time">{{updateTime}}</text>
						</view>
					</view>
					
					<!-- 选项卡切换 -->
					<view class="tabs">
						<view class="tab-item" :class="{ active: activeTab === 'today' }" @click="switchTab('today')">
							<text>今日热搜</text>
						</view>
						<view class="tab-item" :class="{ active: activeTab === 'yesterday' }" @click="switchTab('yesterday')">
							<text>昨日热搜</text>
						</view>
					</view>
					
					<!-- 热搜列表 -->
					<view class="hot-search-list">
						<!-- 加载指示器 -->
						<view v-if="loading" class="loading-container">
							<view class="loading-spinner"></view>
							<text class="loading-text">加载中...</text>
						</view>
						
						<!-- 数据列表 -->
						<view v-else>
							<view class="hot-item" v-for="(item, index) in hotSearchList" :key="index" @click="navigateToHotDetail(item.id)">
								<view class="rank" :class="'rank-'+(index+1)">{{index+1}}</view>
								<view class="hot-title text-ellipsis">{{item.title}}</view>
								<view class="hot-value" :class="item.trend">{{item.value}}</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>
	</view>

	<!-- AI智能助手 -->
	<AIAssistant :show-analysis="false" />
</template>

<script>
import api from '@/common/api.js';
import CustomIcon from '@/components/CustomIcon.vue';
import AIAssistant from '@/components/AIAssistant.vue';

export default {
	components: {
		CustomIcon,
		AIAssistant
	},
	data() {
		return {
			searchKeyword: '', // 搜索关键词
			showSearchResults: false, // 是否显示搜索结果
			relatedHotList: [], // 相关热点列表
			historyList: [], // 历史搜索记录
			searchSuggestions: [], // 搜索建议列表
			suggestionTimer: null, // 搜索建议防抖定时器
			isHistoryExpanded: false,
			expandedHistoryIndex: -1, // 当前展开显示完整内容的历史记录索引
			expandTimer: null, // 展开状态自动收起定时器
			longPressTimer: null, // 长按防抖定时器
			longPressActiveIndex: -1, // 长按激活状态的索引
			searchCountCache: {}, // 搜索量记录缓存，防止重复调用
			hotSearchList: [], // 热搜榜列表
			updateTime: this.formatTime(new Date()), // 更新时间
			page: 0,
			size: 10,
			loading: false,
			activeTab: 'today', // 当前激活的选项卡，默认为今日热搜
			isRefreshing: false // 是否正在刷新热搜榜
		}
	},
	computed: {
		// 计算当前显示的历史记录列表
		displayHistoryList() {
			if (this.isHistoryExpanded || this.historyList.length <= 2) {
				return this.historyList;
			} else {
				return this.historyList.slice(0, 2);
			}
		}
	},
	onLoad() {
		console.log('Discover页面初始化');

		// 检查登录状态
		const token = uni.getStorageSync('token');
		if (!token) {
			console.log('用户未登录，跳转到登录页');
			// 未登录，跳转到登录页
			uni.redirectTo({
				url: '/pages/login/login'
			});
			return;
		}

		// 从服务器获取历史搜索记录
		console.log('开始加载搜索历史');
		this.loadSearchHistory();

		// 确保 showSearchResults 为 false
		this.showSearchResults = false;

		// 获取热搜榜数据
		console.log('开始加载热搜榜');
		this.getHotSearchList();
	},
	mounted() {
		// 确保初始状态下搜索结果不显示
		this.showSearchResults = false;
	},
	onUnload() {
		// 清理定时器，防止内存泄漏
		this.clearExpandTimer();
		if (this.suggestionTimer) {
			clearTimeout(this.suggestionTimer);
		}
		if (this.longPressTimer) {
			clearTimeout(this.longPressTimer);
		}
		// 清理搜索量缓存
		this.searchCountCache = {};
	},
	onShow() {
		// 页面显示时，如果不是首次加载，则刷新热搜榜数据
		if (this.hotSearchList.length > 0) {
			console.log('从其他页面返回，刷新热搜榜数据');
			this.getHotSearchList();
		}
	},
	onHide() {
		// 页面隐藏时的处理
	},
	onUnload() {
		// 页面卸载时的处理
	},
	methods: {
		// 格式化时间
		formatTime(date) {
			const hours = date.getHours().toString().padStart(2, '0');
			const minutes = date.getMinutes().toString().padStart(2, '0');
			return `${hours}:${minutes}`;
		},
		
		// 聚焦搜索框
		focusSearch() {
			// 注意：在不同平台上聚焦方法可能不同
			// 方法1：尝试使用 $refs
			try {
				if (this.$refs.searchInput && typeof this.$refs.searchInput.focus === 'function') {
					this.$refs.searchInput.focus();
					return;
				}
			} catch (e) {
				// 忽略错误
			}

			// 方法2：使用 uni.createSelectorQuery
			try {
				const query = uni.createSelectorQuery().in(this);
				query.select('.search-input .input').boundingClientRect(data => {
					if (data) {
						// 在某些平台上可以直接显示键盘
						uni.showKeyboard && uni.showKeyboard();
						// 或者尝试添加 focus 属性（动态设置）
						this.$nextTick(() => {
							const inputEl = document.querySelector('.search-input .input');
							if (inputEl && typeof inputEl.focus === 'function') {
								inputEl.focus();
							}
						});
					}
				}).exec();
			} catch (e) {
				// 忽略错误
			}
		},
		
		// 搜索输入事件
		onSearchInput() {
			// 如果清空了搜索框，隐藏搜索结果
			if (!this.searchKeyword) {
				this.showSearchResults = false;
				this.searchSuggestions = [];
			} else {
				// 获取搜索建议
				this.getSearchSuggestions(this.searchKeyword.trim());
			}
		},

		// 获取搜索建议
		getSearchSuggestions(keyword) {
			// 只有输入1个或更多字符时才触发搜索建议
			if (!keyword || keyword.length < 1) {
				this.searchSuggestions = [];
				return;
			}

			// 防抖处理
			if (this.suggestionTimer) {
				clearTimeout(this.suggestionTimer);
			}

			// 防抖延迟
			this.suggestionTimer = setTimeout(() => {
				api.request({
					url: '/v1/search/suggestions',
					method: 'GET',
					data: {
						keyword: keyword,
						limit: 8,
						excludeHistory: true  // 明确排除历史搜索
					}
				}).then(res => {
					if (res.data && res.data.code === 0 && res.data.data) {
						const suggestions = res.data.data.suggestions || [];
						const hotKeywords = res.data.data.hotKeywords || [];
						const topicSuggestions = res.data.data.topicSuggestions || [];

						// 构建搜索建议列表，优先显示热点话题，排除历史搜索
						let allSuggestions = [];

						// 添加热点话题建议（包含ID和标题）
						topicSuggestions.forEach(topic => {
							allSuggestions.push({
								type: 'topic',
								topicId: topic.topicId,
								title: topic.title,
								hotValue: topic.hotValue,
								searchCount: topic.searchCount,
								displayText: topic.title
							});
						});

						// 添加普通关键词建议（排除历史搜索）
						suggestions.forEach(suggestion => {
							// 确保不是历史搜索记录
							if (!this.isHistoryKeyword(suggestion)) {
								allSuggestions.push({
									type: 'keyword',
									displayText: suggestion
								});
							}
						});

						// 添加热门搜索词（排除历史搜索）
						hotKeywords.forEach(keyword => {
							// 确保不是历史搜索记录
							if (!this.isHistoryKeyword(keyword)) {
								allSuggestions.push({
									type: 'hot_keyword',
									displayText: keyword
								});
							}
						});

						// 去重并限制数量
						const uniqueSuggestions = allSuggestions.filter((item, index, self) =>
							index === self.findIndex(t => t.displayText === item.displayText)
						);

						this.searchSuggestions = uniqueSuggestions.slice(0, 8);
					} else {
						this.searchSuggestions = [];
					}
				}).catch(err => {
					console.error('获取搜索建议失败:', err);
					this.searchSuggestions = [];
				});
			}, 300);
		},

		// 检查是否为历史搜索关键词
		isHistoryKeyword(keyword) {
			return this.historyList.includes(keyword);
		},

		// 选择搜索建议
		selectSuggestion(suggestion) {
			// 设置搜索关键词
			this.searchKeyword = suggestion.displayText;
			this.searchSuggestions = [];

			// 如果是热点话题，直接使用topicId增加搜索量
			if (suggestion.type === 'topic' && suggestion.topicId) {
				this.recordSearchCountByTopicId(suggestion.topicId, suggestion.displayText);
			} else {
				// 对于普通关键词，尝试查找对应的热点话题
				this.recordSearchCountForSuggestion(suggestion.displayText);
			}

			this.doSearch();
		},

		// 清空搜索
		clearSearch() {
			this.searchKeyword = '';
			this.showSearchResults = false;
			this.searchSuggestions = [];
		},
		
		// 执行搜索
		doSearch() {
			if (!this.searchKeyword.trim()) return;

			uni.showLoading({
				title: '搜索中...'
			});

			// 显示搜索结果区域
			this.showSearchResults = true;

			// 调用搜索API
			this.performSearch(this.searchKeyword);
		},

		// 执行搜索请求
		performSearch(keyword) {
			// 记录搜索行为
			api.request({
				url: '/v1/search/record',
				method: 'POST',
				data: {
					keyword: keyword,
					type: 'all'
				}
			}).catch(err => {
				console.error('记录搜索行为失败:', err);
			});

			// 执行搜索
			api.request({
				url: '/v1/search',
				method: 'GET',
				data: {
					keyword: keyword,
					type: 'all',
					page: 0,
					size: 10
				}
			}).then(res => {
				if (res.data && res.data.code === 0 && res.data.data) {
					const searchResult = res.data.data;

					// 设置相关热点列表 - 修复数据结构问题
					let topicsList = [];
					if (searchResult.topics) {
						// MyBatis-Plus的Page对象，数据在records字段中
						topicsList = searchResult.topics.records || searchResult.topics.content || [];
					}

					if (topicsList.length > 0) {
						this.relatedHotList = topicsList.map(item => ({
							id: item.topicId,
							title: item.title,
							tags: item.tags || [], // 直接使用后端返回的tags字段
							description: item.description || '暂无描述',
							viewCount: this.formatCount(item.viewCount),
							commentCount: '0',
							isFavorite: item.isFavorite === 1,
							isLiked: false
						}));
					} else {
						this.relatedHotList = [];
					}

					// 添加到历史记录
					this.addSearchHistory(keyword);

				} else {
					console.error('搜索失败:', res.data);
					uni.showToast({
						title: '搜索失败',
						icon: 'none'
					});
				}

				uni.hideLoading();
			}).catch(err => {
				console.error('搜索请求失败:', err);
				uni.showToast({
					title: '网络请求失败',
					icon: 'none'
				});
				uni.hideLoading();
			});
		},
		
		// 使用历史搜索
		useHistorySearch(keyword) {
			this.searchKeyword = keyword;

			// 增加搜索量：通过标题查找对应的热点话题ID并增加搜索量
			this.recordSearchCountForSuggestion(keyword);

			this.doSearch();
		},
		
		// 删除单条历史记录
		deleteHistory(index) {
			const keyword = this.historyList[index];

			// 从服务器删除
			api.request({
				url: '/v1/search/history',
				method: 'DELETE',
				data: {
					keyword: keyword
				}
			}).then(res => {
				if (res.data && res.data.code === 0) {
					// 服务器删除成功后，从本地列表中删除
					this.historyList.splice(index, 1);
					console.log('删除搜索历史成功:', keyword);
				}
			}).catch(err => {
				console.error('删除搜索历史失败:', err);
				uni.showToast({
					title: '删除失败',
					icon: 'none'
				});
			});
		},
		
		// 清空所有历史记录
		clearAllHistory() {
			uni.showModal({
				title: '提示',
				content: '确定要清空所有历史记录吗？',
				success: (res) => {
					if (res.confirm) {
						// 从服务器清空
						api.request({
							url: '/v1/search/history/all',
							method: 'DELETE'
						}).then(res => {
							if (res.data && res.data.code === 0) {
								// 服务器清空成功后，清空本地列表
								this.historyList = [];
								console.log('清空搜索历史成功');
								uni.showToast({
									title: '清空成功',
									icon: 'success'
								});
							}
						}).catch(err => {
							console.error('清空搜索历史失败:', err);
							uni.showToast({
								title: '清空失败',
								icon: 'none'
							});
						});
					}
				}
			});
		},
		
		// 从服务器加载搜索历史
		loadSearchHistory() {
			api.request({
				url: '/v1/search/history',
				method: 'GET',
				data: {
					limit: 10
				}
			}).then(res => {
				if (res.data && res.data.code === 0 && res.data.data) {
					this.historyList = res.data.data;
					console.log('加载搜索历史成功，共', this.historyList.length, '条记录');
				} else {
					this.historyList = [];
					console.log('搜索历史为空');
				}
			}).catch(err => {
				console.error('加载搜索历史失败:', err);
				this.historyList = [];
			});
		},

		// 添加搜索记录（后端会自动处理重复和排序）
		addSearchHistory(keyword) {
			// 后端recordSearch已经处理了重复记录的问题
			// 这里只需要重新加载历史记录即可
			this.loadSearchHistory();
		},
		
		// 切换历史记录展开/收起状态
		toggleExpandHistory() {
			this.isHistoryExpanded = !this.isHistoryExpanded;
		},

		// 处理历史记录点击
		handleHistoryClick(item, index) {
			if (this.expandedHistoryIndex === index) {
				// 如果当前项已展开，点击时收起
				this.expandedHistoryIndex = -1;
			} else {
				// 如果未展开，执行搜索
				this.useHistorySearch(item);
			}
		},

		// 切换显示完整文本
		toggleFullText(index) {
			// 防抖处理，避免快速重复触发
			if (this.longPressTimer) {
				return;
			}

			this.longPressTimer = setTimeout(() => {
				this.longPressTimer = null;
			}, 300);

			// 添加触觉反馈（仅在非H5环境下）
			// #ifndef H5
			try {
				uni.vibrateShort({
					type: 'light'
				});
			} catch (e) {
				// 忽略振动错误
			}
			// #endif

			// H5环境下的视觉反馈（简化版本，使用CSS动画）
			// #ifdef H5
			this.longPressActiveIndex = index;
			// 使用requestAnimationFrame优化性能
			this.$nextTick(() => {
				setTimeout(() => {
					if (this.longPressActiveIndex === index) {
						this.longPressActiveIndex = -1;
					}
				}, 120);
			});
			// #endif

			if (this.expandedHistoryIndex === index) {
				// 如果当前项已展开，则收起
				this.expandedHistoryIndex = -1;
				this.clearExpandTimer();
			} else {
				// 展开当前项，收起其他项
				this.expandedHistoryIndex = index;
				this.startExpandTimer();
			}
		},

		// 清除展开定时器
		clearExpandTimer() {
			if (this.expandTimer) {
				clearTimeout(this.expandTimer);
				this.expandTimer = null;
			}
		},

		// 开始展开定时器（自动收起）
		startExpandTimer() {
			this.clearExpandTimer();
			this.expandTimer = setTimeout(() => {
				this.expandedHistoryIndex = -1;
			}, 3000); // 3秒后自动收起，时间更合理
		},

		// 直接通过topicId记录搜索量（用于热点话题建议）
		recordSearchCountByTopicId(topicId, title) {
			// 防抖处理，避免短时间内重复调用
			const cacheKey = `search_count_${topicId}`;
			const now = Date.now();
			const lastCallTime = this.searchCountCache && this.searchCountCache[cacheKey];

			// 如果5秒内已经调用过，则跳过
			if (lastCallTime && (now - lastCallTime) < 5000) {
				return;
			}

			// 记录调用时间
			if (!this.searchCountCache) {
				this.searchCountCache = {};
			}
			this.searchCountCache[cacheKey] = now;

			// 直接调用API增加搜索量
			api.request({
				url: `/v1/hot-topics/${topicId}/search`,
				method: 'POST'
			}).then(res => {
				console.log(`热点话题"${title}"搜索量增加成功`);
			}).catch(err => {
				console.error(`增加热点话题"${title}"搜索量失败:`, err);
			});
		},

		// 为搜索建议记录搜索量（用于普通关键词）
		recordSearchCountForSuggestion(suggestion) {
			// 防抖处理，避免短时间内重复调用
			const cacheKey = `search_count_${suggestion}`;
			const now = Date.now();
			const lastCallTime = this.searchCountCache && this.searchCountCache[cacheKey];

			// 如果5秒内已经调用过，则跳过
			if (lastCallTime && (now - lastCallTime) < 5000) {
				return;
			}

			// 记录调用时间
			if (!this.searchCountCache) {
				this.searchCountCache = {};
			}
			this.searchCountCache[cacheKey] = now;

			// 通过标题查找对应的热点话题ID
			this.findTopicIdByTitle(suggestion).then(topicId => {
				if (topicId) {
					// 调用API增加搜索量
					api.request({
						url: `/v1/hot-topics/${topicId}/search`,
						method: 'POST'
					}).then(res => {
						console.log(`热点话题"${suggestion}"搜索量增加成功`);
					}).catch(err => {
						console.error(`增加热点话题"${suggestion}"搜索量失败:`, err);
					});
				}
			}).catch(err => {
				console.error('查找热点话题ID失败:', err);
			});
		},

		// 通过标题查找热点话题ID
		findTopicIdByTitle(title) {
			return new Promise((resolve, reject) => {
				// 先从当前热搜榜中查找（精确匹配）
				const foundTopic = this.hotSearchList.find(item => item.title === title);
				if (foundTopic) {
					resolve(foundTopic.topicId);
					return;
				}

				// 如果当前热搜榜中没有，则调用搜索API查找
				api.request({
					url: '/v1/search/topics',
					method: 'GET',
					data: {
						keyword: title,
						page: 0,
						size: 5 // 增加搜索结果数量，提高匹配概率
					}
				}).then(res => {
					if (res.data && res.data.code === 0 && res.data.data && res.data.data.records) {
						const topics = res.data.data.records;
						// 查找完全匹配的标题
						const exactMatch = topics.find(topic => topic.title === title);
						if (exactMatch) {
							resolve(exactMatch.topicId);
						} else {
							// 如果没有完全匹配，尝试部分匹配
							const partialMatch = topics.find(topic =>
								topic.title.includes(title) || title.includes(topic.title)
							);
							if (partialMatch) {
								resolve(partialMatch.topicId);
							} else {
								resolve(null);
							}
						}
					} else {
						resolve(null);
					}
				}).catch(err => {
					console.error(`搜索热点话题失败: ${title}`, err);
					reject(err);
				});
			});
		},


		
		// 获取热搜榜数据
		getHotSearchList(needRefresh = false) {
			this.loading = true;
			
			// 根据当前选中的选项卡确定API路径
			const apiPath = this.activeTab === 'today' ? '/v1/hot-topics/today' : '/v1/hot-topics/yesterday';
			
			// 如果需要刷新趋势数据，则先调用刷新接口
			if (needRefresh) {
				// 先刷新趋势数据
				const refreshTrendsPath = this.activeTab === 'today' ? '/v1/hot-topics/refresh-trends' : '/v1/hot-topics/refresh-yesterday-trends';
				api.request({
					url: refreshTrendsPath,
					method: 'POST'
				}).finally(() => {
					// 无论成功失败，都获取最新热点数据
					this.fetchHotTopics(apiPath);
				});
			} else {
				// 直接获取数据，不刷新趋势
				this.fetchHotTopics(apiPath);
			}
		},
		
		// 从API获取热点话题数据
		fetchHotTopics(apiPath) {
			// 调用热点话题列表API
			api.request({
				url: apiPath,
				method: 'GET',
				data: {
					page: this.page,
					size: this.size
				}
			}).then(res => {
				if (res.data && res.data.code === 0 && res.data.data) {
					const hotTopics = res.data.data.content || [];
					console.log('热搜榜加载成功，共', hotTopics.length, '条数据');

					// 将API返回的数据转换为界面所需格式
					this.hotSearchList = hotTopics.map((item, index) => {
						return {
							id: item.topicId, // 保持id字段与后端的topicId一致
							topicId: item.topicId, // 添加一个明确的topicId字段
							title: item.title,
							value: item.hotValue || '0',
							trend: item.trend === 1 ? 'increase' : (item.trend === -1 ? 'decrease' : '')
						};
					});
					
					// 更新时间
					this.updateTime = this.formatTime(new Date());
				} else {
					console.error('获取热点列表失败:', res.data);
					uni.showToast({
						title: '获取热点列表失败',
						icon: 'none'
					});
				}
				this.loading = false;
			}).catch(err => {
				console.error('请求热点列表失败:', err);
				uni.showToast({
					title: '网络请求失败',
					icon: 'none'
				});
				this.loading = false;
			});
		},
		
		// 刷新热搜榜
		refreshHotList(silent = false) {
			if (this.loading || this.isRefreshing) return;

			// 开始刷新动画
			this.isRefreshing = true;

			if (!silent) {
				uni.showLoading({
					title: '刷新中...'
				});
			}
			
			// 根据当前选中的选项卡确定刷新接口
			const refreshPath = this.activeTab === 'today' ? '/v1/hot-topics/refresh' : '/v1/hot-topics/refresh-yesterday';
			
			// 先调用刷新接口，然后再获取热点列表（需要刷新趋势）
			api.request({
				url: refreshPath,
				method: 'POST',
			}).then(res => {
				// 刷新成功后，获取最新的热点列表（需要刷新趋势）
				this.getHotSearchList(true);
				
				if (!silent) {
					setTimeout(() => {
						uni.hideLoading();
						uni.showToast({
							title: '刷新成功',
							icon: 'none'
						});
						// 停止刷新动画
						this.isRefreshing = false;
					}, 800);
				} else {
					// 静默刷新也要停止动画
					setTimeout(() => {
						this.isRefreshing = false;
					}, 1000);
				}
			}).catch(err => {
				console.error('刷新热点失败:', err);
				// 停止刷新动画
				this.isRefreshing = false;

				if (!silent) {
					uni.hideLoading();
					uni.showToast({
						title: '刷新失败',
						icon: 'none'
					});
				}
			});
		},

		// 提取标签
		extractTags(item) {
			const tags = [];
			if (item.source) {
				tags.push(item.source);
			}
			if (item.category) {
				tags.push(item.category);
			}
			// 如果有标签关联数据，可以在这里处理
			return tags;
		},

		// 格式化数字
		formatCount(count) {
			if (!count) return '0';
			if (count >= 10000) {
				return (count / 10000).toFixed(1) + 'w';
			} else if (count >= 1000) {
				return (count / 1000).toFixed(1) + 'k';
			}
			return count.toString();
		},


		
		// 导航到热点详情页
		navigateToHotDetail(id) {
			console.log('跳转到热点详情页, ID:', id);
			
			// 清除可能存在的旧状态，防止影响新页面
			uni.removeStorageSync('hotDetailPageInfo');
			uni.removeStorageSync('visitingExternalUrl');
			uni.removeStorageSync('hotDetailReturnUrl');
			
			// 添加时间戳参数，确保每次都是新跳转
			uni.navigateTo({
				url: `/pages/discover/hotDetail?id=${id}&from=discover&t=${new Date().getTime()}`
			});
		},
		
		// 切换收藏状态
		toggleFavorite(item) {
			item.isFavorite = !item.isFavorite;
			
			// 显示操作提示
			uni.showToast({
				title: item.isFavorite ? '已收藏' : '已取消收藏',
				icon: 'none'
			});
			
			// 这里可以添加API请求，保存收藏状态
		},
		
		// 切换点赞状态
		toggleLike(item) {
			item.isLiked = !item.isLiked;
			
			// 显示操作提示
			uni.showToast({
				title: item.isLiked ? '已点赞' : '已取消点赞',
				icon: 'none'
			});
			
			// 这里可以添加API请求，保存点赞状态
		},
		
		// 切换选项卡
		switchTab(tab) {
			if (this.activeTab === tab) return;
			
			this.activeTab = tab;
			// 切换选项卡后重新获取数据（不需要刷新趋势）
			this.getHotSearchList(false);
		}
	}
}
</script>

<style lang="scss" scoped>
	.page {
		background-color: #f5f7fa;
		min-height: 100vh;
		padding-bottom: 30rpx;
	}
	
	.navbar {
		display: flex;
		align-items: center;
		justify-content: center;
		padding: 20rpx 30rpx;
		background-color: #fff;
		position: sticky;
		top: 0;
		z-index: 100;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
		
		.title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			background: linear-gradient(to right, #722ED1, #B37FEB);
			-webkit-background-clip: text;
			background-clip: text;
			color: transparent;
		}
	}
	
	.search-box {
		padding: 20rpx 30rpx;
		background-color: #fff;
		position: relative;

		.search-input {
			height: 80rpx;
			background-color: #f5f7fa;
			border-radius: 40rpx;
			padding: 0 30rpx;
			display: flex;
			align-items: center;
			box-shadow: inset 0 2rpx 5rpx rgba(0,0,0,0.05);
			position: relative;
			
			.input {
				flex: 1;
				height: 80rpx;
				font-size: 28rpx;
				color: #333;
				margin-left: 10rpx;
			}
			
			.placeholder {
				color: #999;
				font-size: 28rpx;
				margin-left: 10rpx;
			}
			
			.clear-btn {
				width: 40rpx;
				height: 40rpx;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 50%;
				transition: all 0.2s ease;

				&:active {
					background-color: rgba(114, 46, 209, 0.1);
					transform: scale(0.9);
				}
			}
		}

		/* 搜索建议样式 */
		.search-suggestions {
			position: absolute;
			top: 100%;
			left: 30rpx;
			right: 30rpx;
			background: white;
			border-radius: 20rpx;
			box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
			z-index: 1000;
			max-height: 400rpx;
			overflow-y: auto;
			margin-top: 10rpx;

			.suggestion-item {
				display: flex;
				align-items: center;
				padding: 24rpx 30rpx;
				border-bottom: 1rpx solid #f5f5f5;
				transition: background-color 0.2s ease;

				&:first-child {
					border-radius: 20rpx 20rpx 0 0;
				}

				&:last-child {
					border-bottom: none;
					border-radius: 0 0 20rpx 20rpx;
				}

				&:only-child {
					border-radius: 20rpx;
				}

				&:active {
					background-color: rgba(114, 46, 209, 0.1);
				}

				&.topic-suggestion {
					background-color: rgba(114, 46, 209, 0.02);

					&:active {
						background-color: rgba(114, 46, 209, 0.08);
					}
				}

				.suggestion-text {
					margin-left: 20rpx;
					font-size: 28rpx;
					color: #333;
					flex: 1;
				}

				.topic-info {
					display: flex;
					align-items: center;
					margin-left: 10rpx;

					.hot-value {
						font-size: 22rpx;
						color: #722ED1;
						background: linear-gradient(135deg, rgba(114, 46, 209, 0.1), rgba(179, 127, 235, 0.1));
						padding: 4rpx 8rpx;
						border-radius: 10rpx;
						font-weight: 500;
					}
				}
			}
		}
	}
	
	.content-container {
		padding: 20rpx 30rpx;
	}
	
	.card {
		background-color: #fff;
		border-radius: 20rpx;
		padding: 25rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 2rpx 15rpx rgba(0,0,0,0.05);
		
		.card-header {
			display: flex;
			justify-content: space-between;
			align-items: center;
			margin-bottom: 20rpx;
			
			.title-area {
				display: flex;
				align-items: center;
				
				.title {
					font-size: 30rpx;
					font-weight: 600;
					color: #333;
					margin-left: 10rpx;
				}
				
				.refresh-countdown {
					margin-left: 10rpx;
					background-color: rgba(114, 46, 209, 0.1);
					border-radius: 10rpx;
					padding: 2rpx 8rpx;
					
					.countdown-text {
						font-size: 20rpx;
						color: #722ED1;
					}
				}
			}
			
			.action-area {
				display: flex;
				align-items: center;
				
				.action-btn {
					font-size: 24rpx;
					color: #999;
					transition: color 0.3s ease;

					&:active {
						color: #722ED1;
					}
				}

				.action-btn-wrapper {
					display: flex;
					align-items: center;
					padding: 5rpx 10rpx;
					border-radius: 15rpx;
					transition: all 0.3s ease;

					&:active {
						background-color: rgba(114, 46, 209, 0.1);
					}

					.action-btn {
						margin-right: 5rpx;
					}
				}
				
				.divider {
					font-size: 24rpx;
					color: #ddd;
					margin: 0 10rpx;
				}

				.refresh-btn {
					padding: 8rpx;
					border-radius: 50%;
					background-color: rgba(114, 46, 209, 0.1);
					transition: all 0.2s ease;
					display: flex;
					align-items: center;
					justify-content: center;
					cursor: pointer;

					&:active {
						background-color: rgba(114, 46, 209, 0.2);
						transform: scale(0.95);
					}

					&:hover {
						background-color: rgba(114, 46, 209, 0.15);
					}

					&.refreshing {
						animation: spin 1s linear infinite;
					}
				}

				.update-time {
					font-size: 24rpx;
					color: #999;
					margin-left: 10rpx;
				}
			}
		}
	}
	
	.history-section {
		.history-list {
			display: flex;
			flex-wrap: wrap;
			transition: all 0.3s ease;
			overflow: hidden;

			.history-item {
				display: flex;
				align-items: center;
				background-color: #f5f7fa;
				border-radius: 30rpx;
				padding: 10rpx 20rpx;
				margin-right: 20rpx;
				margin-bottom: 15rpx;
				transition: all 0.3s ease;
				animation: fadeInUp 0.3s ease;
				max-width: 260rpx;
				min-width: 80rpx;
				position: relative;

				&.expanded {
					max-width: 100%;
					width: 100%;
					margin-right: 0;
					background: linear-gradient(135deg, rgba(114, 46, 209, 0.1), rgba(179, 127, 235, 0.1));
					border: 1rpx solid #722ED1;
					box-shadow: 0 4rpx 12rpx rgba(114, 46, 209, 0.3);
					z-index: 10;
					will-change: transform, opacity;
					transform: translateZ(0);
				}

				&.long-press-active {
					background-color: rgba(114, 46, 209, 0.08);
					transform: scale(0.98);
					transition: transform 0.1s ease, background-color 0.1s ease;
				}
				
				&:active {
					background-color: rgba(114, 46, 209, 0.1);
				}
				
				.item-text {
					font-size: 26rpx;
					color: #666;
					max-width: 180rpx;
					overflow: hidden;
					text-overflow: ellipsis;
					white-space: nowrap;
					flex-shrink: 1;
					cursor: pointer;
					transition: all 0.3s ease;

					&:active {
						color: #722ED1;
					}

					&.expanded {
						max-width: none;
						white-space: normal;
						word-break: break-all;
						line-height: 1.4;
						background: linear-gradient(to right, #722ED1, #B37FEB);
						-webkit-background-clip: text;
						background-clip: text;
						color: transparent;
						font-weight: 600;
						will-change: transform;
						transform: translateZ(0);
					}
				}
				
				.delete-btn {
					margin-left: 12rpx;
					width: 32rpx;
					height: 32rpx;
					display: flex;
					align-items: center;
					justify-content: center;
					flex-shrink: 0;
					border-radius: 50%;
					background-color: rgba(0, 0, 0, 0.05);
					transition: all 0.2s ease;

					&:active {
						background-color: rgba(255, 81, 81, 0.15);
						transform: scale(0.9);
					}

					&:hover {
						background-color: rgba(255, 81, 81, 0.1);
					}
				}
			}
		}
	}
	
	.hot-search-list {
		.hot-item {
			display: flex;
			align-items: center;
			padding: 20rpx 0;
			border-bottom: 1px solid #f5f7fa;
			transition: all 0.3s;
			
			&:active {
				background-color: rgba(114, 46, 209, 0.05);
			}
			
			&:last-child {
				border-bottom: none;
			}
			
			.rank {
				width: 50rpx;
				font-size: 28rpx;
				font-weight: bold;
				color: #999;
				text-align: center;
				
				&.rank-1 {
					color: #ff4d4f;
				}
				
				&.rank-2 {
					color: #ff7a45;
				}
				
				&.rank-3 {
					color: #ffa940;
				}
			}
			
			.hot-title {
				flex: 1;
				font-size: 28rpx;
				color: #333;
				padding: 0 20rpx;
			}
			
			.hot-value {
				font-size: 26rpx;
				display: flex;
				align-items: center;
				
				&.increase {
					color: #ff4d4f;
					
					&::before {
						content: '';
						width: 0;
						height: 0;
						border-left: 6rpx solid transparent;
						border-right: 6rpx solid transparent;
						border-bottom: 10rpx solid #ff4d4f;
						margin-right: 6rpx;
					}
				}
				
				&.decrease {
					color: #52c41a;
					
					&::before {
						content: '';
						width: 0;
						height: 0;
						border-left: 6rpx solid transparent;
						border-right: 6rpx solid transparent;
						border-top: 10rpx solid #52c41a;
						margin-right: 6rpx;
					}
				}
			}
		}
	}
	
	/* 搜索结果区域样式 */
	.search-results {
		.related-hot-list {
			.hot-item-container {
				.hot-item {
					border-bottom: 1px solid #f5f7fa;
					padding: 20rpx 0;
					
					&:last-child {
						border-bottom: none;
					}
					
					.hot-item-header {
						display: flex;
						flex-direction: column;
						margin-bottom: 15rpx;
						
						.hot-title {
							font-size: 30rpx;
							font-weight: 600;
							color: #333;
							margin-bottom: 10rpx;
						}
						
						.hot-tags {
							display: flex;
							flex-wrap: wrap;
							
							.tag {
								font-size: 22rpx;
								color: #3264ED;
								background-color: rgba(50, 100, 237, 0.1);
								border-radius: 20rpx;
								padding: 4rpx 15rpx;
								margin-right: 15rpx;
								margin-bottom: 8rpx;
							}
						}
					}
					
					.hot-item-content {
						margin-bottom: 15rpx;
						
						.hot-desc {
							font-size: 26rpx;
							color: #666;
							line-height: 1.5;
							display: -webkit-box;
							-webkit-box-orient: vertical;
							-webkit-line-clamp: 2;
							line-clamp: 2;
							overflow: hidden;
						}
					}
					
					.hot-item-footer {
						display: flex;
						justify-content: space-between;
						align-items: center;
						
						.hot-stats {
							display: flex;
							align-items: center;
							
							.stat-item {
								display: flex;
								align-items: center;
								margin-right: 20rpx;
								
								text {
									font-size: 24rpx;
									color: #999;
									margin-left: 5rpx;
								}
							}
						}
						
						.hot-actions {
							display: flex;
							align-items: center;
							
							.action-btn {
								margin-left: 25rpx;
								width: 40rpx;
								height: 40rpx;
								display: flex;
								align-items: center;
								justify-content: center;
								border-radius: 50%;
								transition: all 0.2s ease;

								&:active {
									background-color: rgba(114, 46, 209, 0.1);
									transform: scale(0.9);
								}
							}
						}
					}
				}
			}
		}
	}
	
	/* 加载指示器样式 */
	.loading-container {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 40rpx 0;
		
		.loading-spinner {
			width: 60rpx;
			height: 60rpx;
			border: 4rpx solid rgba(50, 100, 237, 0.2);
			border-top: 4rpx solid #3264ED;
			border-radius: 50%;
			animation: spin 1s linear infinite;
		}
		
		.loading-text {
			margin-top: 20rpx;
			font-size: 26rpx;
			color: #999;
		}
	}
	
	@keyframes spin {
		0% { transform: rotate(0deg); }
		100% { transform: rotate(360deg); }
	}

	.text-ellipsis {
		max-width: 70%;
		white-space: nowrap;
		overflow: hidden;
		text-overflow: ellipsis;
	}

	.tabs {
		display: flex;
		margin-bottom: 15rpx;
		border-bottom: 1px solid #f0f0f0;
		
		.tab-item {
			flex: 1;
			text-align: center;
			padding: 15rpx 0;
			position: relative;
			
			text {
				font-size: 28rpx;
				color: #666;
			}
			
			&.active {
				text {
					color: #722ED1;
					font-weight: 600;
				}
				
				&::after {
					content: '';
					position: absolute;
					bottom: -1px;
					left: 50%;
					transform: translateX(-50%);
					width: 40rpx;
					height: 4rpx;
					background-color: #722ED1;
					border-radius: 2rpx;
				}
			}
		}
	}

	/* 动画效果 */
	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(20rpx);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	@keyframes pulse {
		0%, 100% {
			opacity: 0.4;
			transform: scale(1);
		}
		50% {
			opacity: 0.9;
			transform: scale(1.1);
		}
	}

	@keyframes spin {
		from {
			transform: rotate(0deg);
		}
		to {
			transform: rotate(360deg);
		}
	}
</style>