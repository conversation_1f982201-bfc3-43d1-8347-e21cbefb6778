package com.zhisuo.app.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 热点话题实体类
 */
@Data
@TableName("hot_topics")
public class HotTopic {
    
    @TableId
    private String topicId;       // 话题ID
    
    private String title;         // 话题标题
    
    private String description;   // 话题描述
    
    private String source;        // 来源平台(baidu/tieba/toutiao/ithome/zhihu等)
    
    private String sourceUrl;     // 来源URL(链接)
    
    private String hotValue;      // 热度值(0.4阅读量+0.6搜索量)
    
    private Integer viewCount;    // 阅读量
    
    private Integer searchCount;  // 搜索量
    
    private Integer trend;        // 趋势(1:上升,0:持平,-1:下降)
    
    @TableField("`rank`")         // MySQL关键字需要特殊处理
    private Integer rank;         // 热榜排名(根据热度值排名)

    private Date collectTime;     // 收集时间
    
    private Date updateTime;      // 更新时间
    
    private Integer status;       // 状态(1:显示,0:不显示)
} 