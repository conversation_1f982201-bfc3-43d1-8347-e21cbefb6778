package com.zhisuo.app.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.zhisuo.app.config.AIConfig;
import com.zhisuo.app.dto.request.AIAnalysisRequest;
import com.zhisuo.app.dto.request.AIChatRequest;
import com.zhisuo.app.dto.response.AIAnalysisResponse;
import com.zhisuo.app.dto.response.AIChatResponse;
import com.zhisuo.app.entity.UserAnalysis;
import com.zhisuo.app.mapper.UserAnalysisMapper;
import com.zhisuo.app.service.AIService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * AI服务实现类
 */
@Slf4j
@Service
public class AIServiceImpl implements AIService {
    
    @Autowired
    private AIConfig aiConfig;

    @Autowired
    private UserAnalysisMapper userAnalysisMapper;
    
    @Override
    public AIChatResponse chat(AIChatRequest request) {
        try {
            log.info("开始处理AI对话请求: {}", request.getMessage());

            // 检查配置
            if (aiConfig == null) {
                log.error("AI配置为空");
                return AIChatResponse.fallback(generateFallbackReply(request.getMessage()));
            }

            log.info("AI配置: baseUrl={}, model={}", aiConfig.getBaseUrl(), aiConfig.getModel());

            // 根据是否有上下文信息决定使用哪种提示词
            String systemPrompt;
            if (request.getPageType() != null && request.getContentData() != null) {
                // 有上下文信息，使用分析提示词
                systemPrompt = buildAnalysisSystemPrompt(request.getPageType(), request.getContentData());
                log.info("使用分析模式系统提示词");
            } else {
                // 无上下文信息，使用纯聊天提示词
                systemPrompt = buildChatSystemPrompt();
                log.info("使用聊天模式系统提示词");
            }
            log.info("系统提示词: {}", systemPrompt);

            // 构建消息列表
            JSONArray messages = new JSONArray();

            // 添加系统消息
            JSONObject systemMessage = new JSONObject();
            systemMessage.put("role", "system");
            systemMessage.put("content", systemPrompt);
            messages.add(systemMessage);

            // 添加历史消息 (最多保留10条)
            if (request.getMessageHistory() != null && !request.getMessageHistory().isEmpty()) {
                List<AIChatRequest.ChatMessage> history = request.getMessageHistory();
                int startIndex = Math.max(0, history.size() - 10);
                for (int i = startIndex; i < history.size(); i++) {
                    AIChatRequest.ChatMessage msg = history.get(i);
                    JSONObject historyMessage = new JSONObject();
                    historyMessage.put("role", msg.getRole());
                    historyMessage.put("content", msg.getContent());
                    messages.add(historyMessage);
                }
            }

            // 添加当前用户消息
            JSONObject userMessage = new JSONObject();
            userMessage.put("role", "user");
            userMessage.put("content", request.getMessage());
            messages.add(userMessage);

            log.info("构建的消息数组: {}", messages.toString());

            // 调用硅基流动API
            String reply = callSiliconFlowAPI(messages);

            // 构建响应
            String messageId = UUID.randomUUID().toString();
            return new AIChatResponse(reply, messageId);

        } catch (Exception e) {
            log.error("AI对话失败: {}", e.getMessage(), e);
            // 返回备用回复
            return AIChatResponse.fallback(generateFallbackReply(request.getMessage()));
        }
    }
    
    @Override
    public AIAnalysisResponse analyze(AIAnalysisRequest request) {
        try {
            // 构建分析提示词
            String analysisPrompt = buildAnalysisPrompt(request.getPageType(), request.getContentData());

            // 构建消息列表
            JSONArray messages = new JSONArray();
            JSONObject systemMessage = new JSONObject();
            systemMessage.put("role", "system");
            systemMessage.put("content", "你是一个专业的内容分析师，请根据用户提供的内容进行深度分析。");
            messages.add(systemMessage);

            JSONObject userMessage = new JSONObject();
            userMessage.put("role", "user");
            userMessage.put("content", analysisPrompt);
            messages.add(userMessage);

            // 调用硅基流动API
            String analysisResult = callSiliconFlowAPI(messages);

            // 解析分析结果
            return parseAnalysisResult(analysisResult);

        } catch (Exception e) {
            log.error("AI分析失败", e);
            // 返回备用分析
            return AIAnalysisResponse.fallback();
        }
    }


    
    /**
     * 调用硅基流动API
     */
    private String callSiliconFlowAPI(JSONArray messages) {
        try {
            String url = aiConfig.getBaseUrl() + "/chat/completions";
            log.info("调用硅基流动API: {}", url);

            // 构建请求体
            JSONObject requestBody = new JSONObject();
            requestBody.put("model", aiConfig.getModel());
            requestBody.put("max_tokens", aiConfig.getMaxTokens());
            requestBody.put("enable_thinking", true);
            requestBody.put("thinking_budget", 4096);
            requestBody.put("min_p", 0.05);
            requestBody.put("temperature", aiConfig.getTemperature());
            requestBody.put("top_p", aiConfig.getTopP());
            requestBody.put("top_k", 50);
            requestBody.put("frequency_penalty", 0.5);
            requestBody.put("n", 1);
            requestBody.put("stream", false);
            requestBody.put("messages", messages);

            log.info("请求体: {}", requestBody.toString());

            // 发送请求
            HttpResponse response = HttpRequest.post(url)
                    .header("Authorization", "Bearer " + aiConfig.getApiKey())
                    .header("Content-Type", "application/json")
                    .body(requestBody.toString())
                    .timeout(aiConfig.getTimeout() != null ? aiConfig.getTimeout() : 60000)
                    .execute();

            log.info("API响应状态: {}", response.getStatus());
            log.info("API响应内容: {}", response.body());

            if (!response.isOk()) {
                String errorMsg = "API调用失败: " + response.getStatus() + " " + response.body();
                log.error(errorMsg);
                throw new RuntimeException(errorMsg);
            }

            // 解析响应
            JSONObject responseJson = JSONUtil.parseObj(response.body());
            JSONArray choices = responseJson.getJSONArray("choices");
            if (choices == null || choices.isEmpty()) {
                log.error("API响应格式错误，没有choices字段");
                throw new RuntimeException("API响应格式错误");
            }

            JSONObject firstChoice = choices.getJSONObject(0);
            JSONObject message = firstChoice.getJSONObject("message");
            String content = message.getStr("content");

            log.info("AI回复内容: {}", content);
            return content;

        } catch (Exception e) {
            log.error("调用硅基流动API异常: {}", e.getMessage(), e);
            throw new RuntimeException("调用硅基流动API失败: " + e.getMessage(), e);
        }
    }
    
    /**
     * 构建聊天系统提示词 - 纯聊天，不包含上下文
     */
    private String buildChatSystemPrompt() {
        return "你是智索APP的AI小助手，一个专业、友好、有帮助的智能助手。请为用户提供有价值的帮助和建议，回答要简洁明了，语气友好自然。";
    }

    /**
     * 构建分析系统提示词 - 包含上下文信息
     */
    private String buildAnalysisSystemPrompt(String pageType, AIChatRequest.ContentData contentData) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("你是智索APP的AI小助手，一个专业、友好、有帮助的智能助手。");

        if ("article".equals(pageType) && contentData != null) {
            prompt.append("当前用户正在浏览文章：《").append(contentData.getTitle()).append("》");
            if (StrUtil.isNotBlank(contentData.getDescription())) {
                prompt.append("，文章描述：").append(contentData.getDescription());
            }
        } else if ("hotDetail".equals(pageType) && contentData != null) {
            prompt.append("当前用户正在浏览热点：《").append(contentData.getTitle()).append("》");
            if (StrUtil.isNotBlank(contentData.getDescription())) {
                prompt.append("，热点描述：").append(contentData.getDescription());
            }
        }

        prompt.append("请根据上下文为用户提供有价值的帮助和建议。回答要简洁明了，语气友好自然。");

        return prompt.toString();
    }
    
    /**
     * 构建分析提示词
     */
    private String buildAnalysisPrompt(String pageType, AIAnalysisRequest.ContentData contentData) {
        StringBuilder prompt = new StringBuilder();
        prompt.append("请对以下内容进行深度智能分析：\n\n");

        if (contentData != null) {
            prompt.append("=== 基本信息 ===\n");
            prompt.append("标题：").append(contentData.getTitle()).append("\n");

            if (StrUtil.isNotBlank(contentData.getDescription())) {
                prompt.append("描述：").append(contentData.getDescription()).append("\n");
            }

            if (StrUtil.isNotBlank(contentData.getContent())) {
                // 限制内容长度，避免token过多
                String content = contentData.getContent();
                if (content.length() > 2000) {
                    content = content.substring(0, 2000) + "...";
                }
                prompt.append("文章内容：").append(content).append("\n");
            }

            if (StrUtil.isNotBlank(contentData.getAuthor())) {
                prompt.append("作者：").append(contentData.getAuthor()).append("\n");
            }

            prompt.append("\n=== 数据指标 ===\n");
            if (contentData.getViewCount() != null) {
                prompt.append("浏览量：").append(contentData.getViewCount()).append("\n");
            }
            if (contentData.getLikeCount() != null) {
                prompt.append("点赞数：").append(contentData.getLikeCount()).append("\n");
            }
            if (contentData.getCommentCount() != null) {
                prompt.append("评论数：").append(contentData.getCommentCount()).append("\n");
            }

            if (contentData.getTags() != null && !contentData.getTags().isEmpty()) {
                prompt.append("标签：").append(String.join(", ", contentData.getTags())).append("\n");
            }
        }

        prompt.append("\n=== 分析要求 ===\n");
        prompt.append("请根据标题、描述、文章内容、浏览量、点赞数等信息，从以下维度进行专业分析：\n\n");
        prompt.append("1. 内容摘要（50-80字，概括核心内容）\n");
        prompt.append("2. 关键词（3-5个核心关键词，用逗号分隔）\n");
        prompt.append("3. 未来趋势（50-80字，基于内容分析相关领域的发展趋势）\n");
        prompt.append("4. 影响力评估（30-50字，评估内容的潜在影响力和传播价值）\n");
        prompt.append("5. 相关话题（3-4个相关话题，用逗号分隔）\n");
        prompt.append("\n请严格按照以上格式逐项回答，每项占一行，格式为：序号. 标题：内容");

        return prompt.toString();
    }
    
    /**
     * 解析分析结果
     */
    private AIAnalysisResponse parseAnalysisResult(String analysisText) {
        AIAnalysisResponse response = new AIAnalysisResponse();

        try {
            String[] lines = analysisText.split("\n");
            for (String line : lines) {
                line = line.trim();
                if (line.contains("内容摘要") || line.contains("摘要")) {
                    response.setSummary(extractContent(line));
                } else if (line.contains("关键词")) {
                    String keywords = extractContent(line);
                    response.setKeywords(Arrays.asList(keywords.split("[,，]")));
                } else if (line.contains("未来趋势") || line.contains("趋势")) {
                    response.setFutureTrends(extractContent(line));
                } else if (line.contains("影响力") || line.contains("评估")) {
                    response.setInfluenceAssessment(extractContent(line));
                } else if (line.contains("相关话题") || line.contains("话题")) {
                    String topics = extractContent(line);
                    response.setRelatedTopics(Arrays.asList(topics.split("[,，]")));
                }
            }
        } catch (Exception e) {
            log.warn("解析分析结果失败", e);
        }

        // 设置默认值
        if (response.getSummary() == null) {
            response.setSummary("AI正在学习中，暂时无法生成摘要");
        }
        if (response.getKeywords() == null) {
            response.setKeywords(new ArrayList<>());
        }
        if (response.getFutureTrends() == null) {
            response.setFutureTrends("暂时无法分析未来趋势");
        }
        if (response.getRelatedTopics() == null) {
            response.setRelatedTopics(new ArrayList<>());
        }
        if (response.getInfluenceAssessment() == null) {
            response.setInfluenceAssessment("暂时无法评估影响力");
        }

        return response;
    }

    @Override
    public AIAnalysisResponse saveAnalysisResult(AIAnalysisRequest request) {
        try {
            log.info("开始保存已有分析结果，用户ID: {}, 页面类型: {}", request.getUserId(), request.getPageType());

            // 检查是否提供了分析结果
            if (request.getAnalysisResult() == null) {
                throw new IllegalArgumentException("未提供分析结果数据");
            }

            // 构建AIAnalysisResponse对象
            AIAnalysisResponse response = new AIAnalysisResponse();
            AIAnalysisRequest.AnalysisResult analysisResult = request.getAnalysisResult();

            response.setSummary(analysisResult.getSummary());
            response.setKeywords(analysisResult.getKeywords());
            response.setFutureTrends(analysisResult.getFutureTrends());
            response.setInfluenceAssessment(analysisResult.getInfluenceAssessment());
            response.setRelatedTopics(analysisResult.getRelatedTopics());
            response.setTimestamp(System.currentTimeMillis());
            response.setIsFallback(false);

            // 如果用户ID不为空，则保存到数据库
            if (StrUtil.isNotBlank(request.getUserId())) {
                try {
                    String analysisId = saveAnalysisToDatabase(request, response);
                    response.setAnalysisId(analysisId);
                    response.setSavedToDatabase(true);
                    log.info("分析结果已保存到数据库，分析ID: {}", analysisId);
                } catch (Exception e) {
                    log.error("保存分析结果到数据库失败: {}", e.getMessage(), e);
                    throw new RuntimeException("保存分析结果到数据库失败: " + e.getMessage(), e);
                }
            } else {
                log.warn("用户ID为空，无法保存到数据库");
                throw new IllegalArgumentException("用户ID不能为空");
            }

            return response;

        } catch (Exception e) {
            log.error("保存分析结果失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存分析结果失败: " + e.getMessage(), e);
        }
    }

    /**
     * 保存分析结果到数据库
     */
    private String saveAnalysisToDatabase(AIAnalysisRequest request, AIAnalysisResponse response) {
        try {
            // 设置文章ID (便于在mine.vue中查看最近分析详情)
            String articleId = request.getArticleId();
            if (articleId == null || articleId.trim().isEmpty()) {
                // 如果没有提供articleId，生成一个默认值
                String tempId = UUID.randomUUID().toString().replace("-", "").substring(0, 8);
                articleId = "analysis_" + tempId;
                log.info("未提供articleId，生成默认值: {}", articleId);
            }

            // 检查是否已存在同一用户对同一文章的分析
            UserAnalysis existingAnalysis = userAnalysisMapper.selectByUserIdAndArticleId(request.getUserId(), articleId);

            UserAnalysis userAnalysis;
            String analysisId;
            boolean isUpdate = false;

            if (existingAnalysis != null) {
                // 如果存在，更新现有记录
                userAnalysis = existingAnalysis;
                analysisId = existingAnalysis.getAnalysisId();
                isUpdate = true;
                log.info("找到现有分析记录，将更新记录，用户ID: {}, 文章ID: {}, 分析ID: {}", request.getUserId(), articleId, analysisId);
            } else {
                // 如果不存在，创建新记录
                userAnalysis = new UserAnalysis();
                analysisId = UUID.randomUUID().toString().replace("-", "");
                userAnalysis.setAnalysisId(analysisId);
                userAnalysis.setUserId(request.getUserId());
                userAnalysis.setArticleId(articleId);
                userAnalysis.setCreateTime(new Date());
                userAnalysis.setStatus(1); // 1表示显示
                log.info("创建新分析记录，用户ID: {}, 文章ID: {}, 分析ID: {}", request.getUserId(), articleId, analysisId);
            }

            // 设置分析标题 (对应文章标题)
            AIAnalysisRequest.ContentData contentData = request.getContentData();
            String title = "未知标题";
            if (contentData != null && contentData.getTitle() != null && !contentData.getTitle().trim().isEmpty()) {
                title = contentData.getTitle();
            }
            userAnalysis.setTitle(title);
            log.info("设置分析标题: {}", title);

            // 设置分析描述 (对应内容摘要)
            String summary = response.getSummary();
            if (summary == null || summary.trim().isEmpty()) {
                summary = "AI正在学习中，暂时无法生成摘要";
            }
            userAnalysis.setDescription(summary);
            log.info("设置分析描述: {}", summary.length() > 50 ? summary.substring(0, 50) + "..." : summary);

            // 设置分析内容 (对应未来趋势、影响力评估、相关话题)
            StringBuilder contentBuilder = new StringBuilder();

            // 添加关键词
            if (response.getKeywords() != null && !response.getKeywords().isEmpty()) {
                contentBuilder.append("【关键词】\n");
                contentBuilder.append(String.join("、", response.getKeywords()));
                contentBuilder.append("\n\n");
            }

            // 添加未来趋势
            if (response.getFutureTrends() != null) {
                contentBuilder.append("【未来趋势】\n");
                contentBuilder.append(response.getFutureTrends());
                contentBuilder.append("\n\n");
            }

            // 添加影响力评估
            if (response.getInfluenceAssessment() != null) {
                contentBuilder.append("【影响力评估】\n");
                contentBuilder.append(response.getInfluenceAssessment());
                contentBuilder.append("\n\n");
            }

            // 添加相关话题
            if (response.getRelatedTopics() != null && !response.getRelatedTopics().isEmpty()) {
                contentBuilder.append("【相关话题】\n");
                contentBuilder.append(String.join("、", response.getRelatedTopics()));
            }

            String newContent = contentBuilder.toString();
            userAnalysis.setContent(newContent);
            log.info("设置分析内容长度: {}", newContent.length());

            // 设置更新时间
            Date updateTime = new Date();
            userAnalysis.setUpdateTime(updateTime);
            log.info("设置更新时间: {}", updateTime);

            // 保存或更新到数据库
            if (isUpdate) {
                // 更新现有记录，只更新title、description、content、update_time
                log.info("准备更新记录 - 标题: {}, 描述长度: {}, 内容长度: {}",
                    userAnalysis.getTitle(),
                    userAnalysis.getDescription() != null ? userAnalysis.getDescription().length() : 0,
                    userAnalysis.getContent() != null ? userAnalysis.getContent().length() : 0);

                // 使用自定义的更新方法，确保字段被正确更新
                int updateResult = userAnalysisMapper.updateAnalysisContent(
                    analysisId,
                    userAnalysis.getTitle(),
                    userAnalysis.getDescription(),
                    userAnalysis.getContent(),
                    userAnalysis.getUpdateTime()
                );
                log.info("更新结果: {} 行受影响，用户ID: {}, 文章ID: {}, 分析ID: {}",
                    updateResult, request.getUserId(), articleId, analysisId);

                if (updateResult == 0) {
                    log.warn("更新失败，没有行受影响，可能记录不存在");
                    throw new RuntimeException("更新分析记录失败，记录可能不存在");
                }
            } else {
                // 插入新记录
                int insertResult = userAnalysisMapper.insert(userAnalysis);
                log.info("插入结果: {} 行受影响，用户ID: {}, 文章ID: {}, 分析ID: {}",
                    insertResult, request.getUserId(), articleId, analysisId);

                if (insertResult == 0) {
                    log.warn("插入失败，没有行受影响");
                    throw new RuntimeException("插入分析记录失败");
                }
            }

            return analysisId;

        } catch (Exception e) {
            log.error("保存AI分析结果到数据库失败: {}", e.getMessage(), e);
            throw new RuntimeException("保存分析结果失败", e);
        }
    }

    /**
     * 提取内容（去除标签和序号）
     */
    private String extractContent(String line) {
        // 去除数字序号和冒号
        line = line.replaceAll("^\\d+[.、]\\s*", "");
        // 去除标签部分
        if (line.contains("：")) {
            line = line.substring(line.indexOf("：") + 1);
        } else if (line.contains(":")) {
            line = line.substring(line.indexOf(":") + 1);
        }
        return line.trim();
    }
    
    /**
     * 生成备用回复
     */
    private String generateFallbackReply(String userMessage) {
        String message = userMessage.toLowerCase();
        
        if (message.contains("你好") || message.contains("hello")) {
            return "你好！我是智索AI小助手，很高兴为您服务！有什么可以帮助您的吗？";
        } else if (message.contains("功能") || message.contains("能做什么")) {
            return "我可以帮您分析内容、回答问题、提供建议等。虽然现在网络有些不稳定，但我会尽力为您提供帮助！";
        } else if (message.contains("分析")) {
            return "我可以为您分析当前内容的关键信息、情感倾向和相关话题。请稍后重试分析功能。";
        } else {
            return "抱歉，我现在遇到了一些技术问题，无法正常回复。请稍后重试，或者尝试重新描述您的问题。";
        }
    }
}
