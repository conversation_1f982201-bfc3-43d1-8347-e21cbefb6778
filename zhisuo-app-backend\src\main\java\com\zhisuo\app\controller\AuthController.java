package com.zhisuo.app.controller;

import com.zhisuo.app.common.Result;
import com.zhisuo.app.common.context.UserContext;
import com.zhisuo.app.dto.request.PasswordLoginRequest;
import com.zhisuo.app.dto.request.SetPasswordRequest;
import com.zhisuo.app.dto.request.SmsCodeRequest;
import com.zhisuo.app.dto.request.SmsLoginRequest;
import com.zhisuo.app.dto.request.TokenRefreshRequest;
import com.zhisuo.app.dto.response.LoginResponse;
import com.zhisuo.app.dto.response.SmsCodeResponse;
import com.zhisuo.app.dto.response.TokenRefreshResponse;
import com.zhisuo.app.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/v1/auth")
public class AuthController {
    
    @Autowired
    private UserService userService;
    
    /**
     * 发送短信验证码
     * 注：测试环境下验证码固定为6666
     */
    @PostMapping("/sms/send")
    public Result<SmsCodeResponse> sendSmsCode(@RequestBody @Validated SmsCodeRequest request) {
        SmsCodeResponse response = userService.sendSmsCode(request);
        return Result.success(response);
    }
    
    /**
     * 短信验证码登录
     * 注：如果用户不存在将自动注册
     */
    @PostMapping("/login/sms")
    public Result<LoginResponse> loginBySms(@RequestBody @Validated SmsLoginRequest request) {
        LoginResponse response = userService.loginBySms(request);
        return Result.success(response);
    }
    
    /**
     * 密码登录
     */
    @PostMapping("/login/password")
    public Result<LoginResponse> loginByPassword(@RequestBody @Validated PasswordLoginRequest request) {
        LoginResponse response = userService.loginByPassword(request);
        return Result.success(response);
    }
    
    /**
     * 设置用户密码
     */
    @PostMapping("/password/set")
    public Result<LoginResponse> setPassword(@RequestBody @Validated SetPasswordRequest request) {
        LoginResponse response = userService.setPassword(request);
        return Result.success(response);
    }
    
    /**
     * 跳过密码设置，使用默认密码
     */
    @PostMapping("/password/skip")
    public Result<LoginResponse> skipSetPassword(@RequestBody SetPasswordRequest request) {
        LoginResponse response = userService.skipSetPassword(
                request.getUserId(), request.getDeviceId(), request.getDeviceType());
        return Result.success(response);
    }
    
    /**
     * 刷新令牌
     */
    @PostMapping("/token/refresh")
    public Result<TokenRefreshResponse> refreshToken(@RequestBody @Validated TokenRefreshRequest request) {
        TokenRefreshResponse response = userService.refreshToken(request);
        return Result.success(response);
    }
    
    /**
     * 退出登录
     */
    @PostMapping("/logout")
    public Result<Void> logout(HttpServletRequest request) {
        String userId = UserContext.getUserId();
        String token = getTokenFromRequest(request);
        userService.logout(userId, token);
        return Result.success();
    }
    
    /**
     * 从请求中获取token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (bearerToken != null && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
} 