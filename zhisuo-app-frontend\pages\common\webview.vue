<template>
	<view class="webview-container">
		<!-- 顶部导航栏 -->
		<view class="navbar">
			<view class="back-btn" @click="goBack">
				<uni-icons type="back" size="18" color="#333"></uni-icons>
				<text>返回</text>
			</view>
			<view class="title">{{pageTitle}}</view>
			<view class="action-btn" @click="openInBrowser">
				<uni-icons type="paperplane" size="18" color="#722ED1"></uni-icons>
			</view>
		</view>
		
		<!-- WebView组件 -->
		<view class="webview-wrapper">
			<web-view v-if="!shouldUseBrowser" :src="url" @message="handleMessage" @error="handleError"></web-view>
		</view>
		
		<!-- CSP限制提示 -->
		<view class="csp-warning" v-if="showCSPWarning">
			<view class="warning-icon">
				<uni-icons type="info" size="24" color="#FF9800"></uni-icons>
			</view>
			<view class="warning-content">
				<text class="warning-title">该网站不支持应用内打开</text>
				<text class="warning-desc">由于内容安全策略(CSP)限制，此网站已在浏览器中打开</text>
			</view>
		</view>
		
		<!-- 加载指示器 -->
		<view class="loading-overlay" v-if="loading && !shouldUseBrowser">
			<view class="loading-spinner"></view>
			<text class="loading-text">加载中...</text>
		</view>
	</view>
</template>

<script>
export default {
	data() {
		return {
			url: '',
			pageTitle: '网页浏览',
			loading: true,
			originalUrl: '', // 保存原始URL，用于在浏览器中打开
			shouldUseBrowser: false, // 是否应该使用系统浏览器打开
			showCSPWarning: false, // 是否显示CSP警告
			// 已知有CSP frame-ancestors限制的网站域名列表
			cspRestrictedDomains: [
				'zhihu.com',
				'www.zhihu.com',
				'weibo.com',
				'www.weibo.com',
				'weibo.cn',
				'www.baidu.com',
				'baidu.com',
				'qq.com',
				'bilibili.com',
				'www.bilibili.com'
				// 可以根据需要添加更多
			]
		}
	},
	onLoad(options) {
		if (options.url) {
			this.originalUrl = decodeURIComponent(options.url);
			this.url = this.originalUrl;
			console.log('WebView加载URL:', this.url);
			
			// 检查是否是有CSP限制的网站
			this.checkCSPRestriction(this.url);
		}
		
		if (options.title) {
			this.pageTitle = decodeURIComponent(options.title);
		}
		
		// 3秒后自动隐藏加载指示器（如果WebView的onLoad事件不可靠）
		setTimeout(() => {
			this.loading = false;
		}, 3000);
	},
	onUnload() {
		// 页面卸载时，清除访问外部链接的标记
		uni.removeStorageSync('visitingExternalUrl');
	},
	methods: {
		goBack() {
			uni.navigateBack();
		},
		
		// 检查URL是否属于有CSP限制的网站
		checkCSPRestriction(url) {
			try {
				const urlObj = new URL(url);
				const hostname = urlObj.hostname;
				
				// 检查域名是否在限制列表中
				const isRestricted = this.cspRestrictedDomains.some(domain => 
					hostname === domain || hostname.endsWith('.' + domain)
				);
				
				if (isRestricted) {
					console.log('检测到CSP限制网站:', hostname);
					this.shouldUseBrowser = true;
					this.showCSPWarning = true;
					
					// 延迟一下再打开浏览器，让用户看到提示
					setTimeout(() => {
						this.openInBrowser();
						
						// 提示显示3秒后自动返回
						setTimeout(() => {
							this.goBack();
						}, 3000);
					}, 1000);
				}
			} catch (e) {
				console.error('URL解析错误:', e);
			}
		},
		
		// 在系统浏览器中打开当前页面
		openInBrowser() {
			// #ifdef H5
			window.open(this.originalUrl, '_blank');
			// #endif
			
			// #ifdef APP-PLUS
			plus.runtime.openURL(this.originalUrl, (error) => {
				if (error) {
					uni.showToast({
						title: '打开链接失败',
						icon: 'none'
					});
					// 如果打开失败，清除访问标记
					uni.removeStorageSync('visitingExternalUrl');
				}
			});
			// #endif
			
			// #ifdef MP
			uni.setClipboardData({
				data: this.originalUrl,
				success: () => {
					uni.showToast({
						title: '链接已复制，请在浏览器中打开',
						icon: 'none'
					});
				}
			});
			// #endif
		},
		
		// 处理WebView消息
		handleMessage(event) {
			console.log('WebView消息:', event);
			// 可以处理来自网页的消息
			this.loading = false;
		},
		
		// 处理WebView错误
		handleError(event) {
			console.error('WebView错误:', event);
			this.loading = false;
			
			// 检查错误信息是否包含CSP frame-ancestors错误
			if (event && event.detail && event.detail.message && 
				(event.detail.message.includes('frame-ancestors') || 
				 event.detail.message.includes('Content Security Policy'))) {
				
				console.log('检测到CSP错误，切换到浏览器打开');
				this.shouldUseBrowser = true;
				this.showCSPWarning = true;
				
				// 延迟一下再打开浏览器
				setTimeout(() => {
					this.openInBrowser();
				}, 1000);
			} else {
				uni.showToast({
					title: '网页加载失败',
					icon: 'none'
				});
			}
		}
	}
}
</script>

<style>
.webview-container {
	position: relative;
	width: 100%;
	height: 100vh;
	display: flex;
	flex-direction: column;
}

.navbar {
	height: 90rpx;
	background-color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0 30rpx;
	box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
	position: relative;
	z-index: 100;
}

.back-btn {
	display: flex;
	align-items: center;
	font-size: 28rpx;
	color: #333333;
}

.title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
	max-width: 60%;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.action-btn {
	width: 60rpx;
	height: 60rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.webview-wrapper {
	flex: 1;
	width: 100%;
}

.loading-overlay {
	position: absolute;
	top: 90rpx;
	left: 0;
	right: 0;
	bottom: 0;
	background-color: rgba(255, 255, 255, 0.8);
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	z-index: 99;
}

.loading-spinner {
	width: 60rpx;
	height: 60rpx;
	border: 4rpx solid #f3f3f3;
	border-top: 4rpx solid #722ED1;
	border-radius: 50%;
	margin: 0 auto 20rpx;
	animation: spin 1s linear infinite;
}

.loading-text {
	font-size: 28rpx;
	color: #666666;
}

.csp-warning {
	position: absolute;
	top: 50%;
	left: 50%;
	transform: translate(-50%, -50%);
	width: 80%;
	background-color: #FFF9E6;
	border-radius: 16rpx;
	padding: 30rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
	z-index: 101;
}

.warning-icon {
	margin-bottom: 20rpx;
}

.warning-content {
	text-align: center;
}

.warning-title {
	font-size: 32rpx;
	font-weight: bold;
	color: #333333;
	margin-bottom: 10rpx;
	display: block;
}

.warning-desc {
	font-size: 26rpx;
	color: #666666;
	line-height: 1.5;
	display: block;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}
</style> 