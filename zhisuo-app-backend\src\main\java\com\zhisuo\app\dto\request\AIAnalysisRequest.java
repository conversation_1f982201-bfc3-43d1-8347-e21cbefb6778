package com.zhisuo.app.dto.request;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * AI分析请求DTO
 */
@Data
public class AIAnalysisRequest {
    
    /**
     * 页面类型 (article, hotDetail等)
     */
    @NotBlank(message = "页面类型不能为空")
    private String pageType;

    /**
     * 内容数据
     */
    private ContentData contentData;

    /**
     * 用户ID (用于保存分析结果)
     */
    private String userId;

    /**
     * 文章ID (便于在mine.vue中查看最近分析详情)
     */
    private String articleId;

    /**
     * 已有的分析结果 (用于保存现有分析结果，不重新分析)
     */
    private AnalysisResult analysisResult;
    
    /**
     * 内容数据
     */
    @Data
    public static class ContentData {
        /**
         * 标题
         */
        private String title;
        
        /**
         * 描述
         */
        private String description;

        /**
         * 文章内容 (完整内容用于深度分析)
         */
        private String content;

        /**
         * 作者
         */
        private String author;
        
        /**
         * 发布时间
         */
        private String publishTime;
        
        /**
         * 浏览量
         */
        private Integer viewCount;
        
        /**
         * 点赞数
         */
        private Integer likeCount;
        
        /**
         * 评论数
         */
        private Integer commentCount;
        
        /**
         * 内容摘要
         */
        private String summary;
        
        /**
         * 标签
         */
        private List<String> tags;
    }

    /**
     * 分析结果数据 (用于保存已有分析结果)
     */
    @Data
    public static class AnalysisResult {
        /**
         * 内容摘要
         */
        private String summary;

        /**
         * 关键词
         */
        private List<String> keywords;

        /**
         * 未来趋势分析
         */
        private String futureTrends;

        /**
         * 影响力评估
         */
        private String influenceAssessment;

        /**
         * 相关话题
         */
        private List<String> relatedTopics;
    }
}
