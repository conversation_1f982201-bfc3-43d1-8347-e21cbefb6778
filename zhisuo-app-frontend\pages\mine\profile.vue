<template>
	<view class="page">
		<!-- 顶部导航栏 -->
		<view class="navbar">
			<view class="navbar-left" @click="goBack">
				<uni-icons type="left" size="24" color="#333"></uni-icons>
			</view>
			<view class="navbar-title">个人主页</view>
			<view class="navbar-right"></view>
		</view>
		
		<!-- 内容区 -->
		<view class="content">
			<!-- 用户基本信息卡片 -->
			<view class="info-card">
				<view class="card-header">
					<text class="card-title">基本信息</text>
				</view>
				
				<view class="info-form">
					<!-- 头像设置 -->
					<view class="form-item avatar-item">
						<view class="form-label">头像</view>
						<view class="avatar-wrapper" @click="chooseAvatar">
							<view class="avatar">
								<image :src="userInfo?.avatar || '/static/logo.png'" mode="aspectFill"></image>
								<view class="avatar-overlay">
									<uni-icons type="camera-filled" size="20" color="#fff"></uni-icons>
								</view>
							</view>
							<view class="form-value">
								<text>点击更换头像</text>
								<uni-icons type="right" size="16" color="#ccc"></uni-icons>
							</view>
						</view>
					</view>
					
					<!-- 昵称设置 -->
					<view class="form-item">
						<view class="form-label">昵称</view>
						<view class="form-input-wrapper" @click="showNicknamePopup">
							<view class="form-value">{{userInfo?.nickname || '未设置'}}</view>
							<uni-icons type="right" size="16" color="#ccc"></uni-icons>
						</view>
					</view>
					
					<!-- 手机号显示 -->
					<view class="form-item">
						<view class="form-label">手机号</view>
						<view class="form-value phone">{{formatPhone(userInfo?.phone) || '未绑定'}}</view>
					</view>
					
					<!-- 会员等级展示 -->
					<view class="form-item">
						<view class="form-label">会员等级</view>
						<view class="form-value member-level">
							<view class="level-badge" :class="'level-' + (userInfo?.memberLevel || 0)">
								{{getMemberLevelText}}
							</view>
						</view>
					</view>
				</view>
			</view>
			
			<!-- 账号安全卡片 -->
			<view class="info-card">
				<view class="card-header">
					<text class="card-title">账号安全</text>
				</view>
				
				<view class="security-list">
					<!-- 修改密码 -->
					<view class="security-item" @click="navigateToPasswordPage">
						<view class="security-icon">
							<uni-icons type="locked" size="24" color="#3264ED"></uni-icons>
						</view>
						<view class="security-content">
							<view class="security-title">修改密码</view>
							<view class="security-desc">定期修改密码可以保护账号安全</view>
						</view>
						<uni-icons type="right" size="16" color="#ccc"></uni-icons>
					</view>
				</view>
			</view>
		</view>
		
		<!-- 保存按钮 -->
		<view class="save-button" @click="saveProfile" :class="{'loading': loading}">
			{{ saveButtonText }}
		</view>

		<!-- 修改昵称弹窗 -->
		<uni-popup ref="nicknamePopup" type="dialog">
			<view class="popup-content">
				<view class="popup-header">
					<view class="popup-title">修改昵称</view>
				</view>
				<view class="popup-body">
					<input class="popup-input" type="text" v-model="newNickname" placeholder="请输入新的昵称" maxlength="12" :focus="nicknameFocus" />
					<view class="input-counter">{{newNickname.length}}/12</view>
				</view>
				<view class="popup-buttons">
					<button class="popup-button cancel" @click="cancelNickname">取消</button>
					<button class="popup-button confirm" @click="confirmNickname">确定</button>
				</view>
			</view>
		</uni-popup>
		
		<!-- 图片裁剪组件 -->
		<image-cropper
			v-if="cropperVisible"
			:visible="cropperVisible"
			:src="cropperSrc"
			:width="300"
			:height="300"
			@cancel="cancelCropper"
			@confirm="confirmCropper">
		</image-cropper>
		
		<!-- 裁剪用的隐藏canvas -->
		<canvas canvas-id="cropper-canvas" style="position:fixed; left:-1000px; top:-1000px; width:300px; height:300px;"></canvas>
	</view>
</template>

<script>
	import Api from '../../common/api.js';
	import ImageCropper from '../../components/ImageCropper.vue';
	import uniPopup from '@dcloudio/uni-ui/lib/uni-popup/uni-popup.vue';
	import uniPopupDialog from '@dcloudio/uni-ui/lib/uni-popup-dialog/uni-popup-dialog.vue';
	
	export default {
		components: {
			ImageCropper,
			uniPopup,
			uniPopupDialog
		},
		data() {
			return {
				userInfo: null,
				avatarChanged: false,
				newAvatar: '',
				newNickname: '',
				nicknameFocus: false,
				loading: false,
				cropperVisible: false,
				cropperSrc: '',
				isRefreshing: false,
				refreshLoading: false, // 刷新用户信息的加载状态
				saveButtonText: '保存个人信息' // 保存按钮文本
			}
		},
		onLoad() {
			// 加载用户信息
			try {
				this.loadUserInfo();
			} catch (err) {
				console.error('加载用户信息失败:', err);
				// 确保隐藏loading
				uni.hideLoading();
				// 延迟显示错误提示
				setTimeout(() => {
					uni.showToast({
						title: '加载用户信息失败',
						icon: 'none'
					});
				}, 100);
			}
		},
		onReady() {
			// 页面渲染完成后，确保事件监听器正确设置
			this.$nextTick(() => {
				this.fixPassiveEventListeners();
			});
		},
		onUnload() {
			// 确保页面卸载时隐藏loading
			uni.hideLoading();
			this.loading = false;
			this.isRefreshing = false;

			// 如果有未保存的头像更改，保留临时数据；否则清除
			if (!this.avatarChanged) {
				uni.removeStorageSync('tempAvatarData');
			}
		},
		computed: {
			// 获取会员等级文本
			getMemberLevelText() {
				const level = this.userInfo?.memberLevel || 0;
				switch(level) {
					case 1:
						return '黄金会员';
					case 2:
						return '铂金会员';
					case 3:
						return '钻石会员';
					default:
						return '普通会员';
				}
			}
		},
		methods: {
			// 加载用户信息
			loadUserInfo() {
				const token = uni.getStorageSync('token');
				if (!token) {
					// 未登录，跳转到登录页
					uni.redirectTo({
						url: '/pages/login/login'
					});
					return;
				}

				// 从本地存储获取用户信息
				let userInfo = uni.getStorageSync('userInfo');
				if (userInfo) {
					this.userInfo = userInfo;
				}

				// 检查是否有未保存的头像更改（从本地存储恢复状态）
				const tempAvatarData = uni.getStorageSync('tempAvatarData');
				if (tempAvatarData && tempAvatarData.newAvatar) {
					//console.log('恢复未保存的头像更改:', tempAvatarData);
					this.newAvatar = tempAvatarData.newAvatar;
					this.avatarChanged = true;
					if (this.userInfo) {
						this.userInfo.avatar = this.newAvatar;
					}
				}

				// 从服务器获取最新用户信息
				try {
					this.refreshUserInfo();
				} catch (err) {
					console.error('刷新用户信息失败:', err);
				}
			},
			
			// 从服务器刷新用户信息
			async refreshUserInfo(showLoading = true) {
				// 防止重复调用
				if (this.isRefreshing) {
					return Promise.resolve();
				}

				this.isRefreshing = true;
				// 使用本地加载状态变量，不使用showLoading
				this.refreshLoading = showLoading;

				try {
					const res = await Api.request({
						url: '/v1/user/info',
						method: 'GET'
					});

					if (res.data && res.data.code === 0) {
						// 保存当前的临时头像（如果有的话）
						const currentAvatar = this.avatarChanged && this.userInfo ? this.userInfo.avatar : null;

						//console.log('刷新用户信息前:', {
							//avatarChanged: this.avatarChanged,
							//currentAvatar: currentAvatar,
							//serverAvatar: res.data.data.avatar
						//});

						this.userInfo = res.data.data;

						// 如果有待保存的头像更改，保持显示临时头像
						if (this.avatarChanged && currentAvatar) {
							this.userInfo.avatar = currentAvatar;
							//console.log('保持临时头像:', currentAvatar);
						}

						uni.setStorageSync('userInfo', this.userInfo);
					}
					return res;
				} catch (err) {
					console.error('获取用户信息失败:', err);
					// 显示错误提示
					uni.showToast({
						title: '获取用户信息失败',
						icon: 'none'
					});
					throw err;
				} finally {
					// 重置加载状态
					this.refreshLoading = false;
					this.isRefreshing = false;
				}
			},
			
			// 返回上一页
			goBack() {
				//直接跳转到个人中心
				uni.switchTab({
						url: '/pages/mine/mine'
					});
			},
			
			// 选择头像
			chooseAvatar() {
				uni.chooseImage({
					count: 1,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						//console.log('选择图片成功:', res.tempFilePaths[0]);
						
						// 先关闭裁剪组件（如果已打开）
						this.cropperVisible = false;
						this.cropperSrc = '';
						
						// 延迟一下再打开裁剪组件，确保DOM已更新
						setTimeout(() => {
							// 设置图片路径
							this.cropperSrc = res.tempFilePaths[0];
							// 显示裁剪组件
							this.$nextTick(() => {
								this.cropperVisible = true;
								//console.log('裁剪组件显示状态:', this.cropperVisible);
								//console.log('裁剪图片路径:', this.cropperSrc);
							});
						}, 300);
					},
					fail: (err) => {
						console.error('选择图片失败:', err);
						uni.showToast({
							title: '选择图片失败',
							icon: 'none'
						});
					}
				});
			},
			
			// 取消裁剪
			cancelCropper() {
				//console.log('取消裁剪');
				this.cropperVisible = false;
				this.cropperSrc = '';
			},
			
			// 确认裁剪
			confirmCropper(tempFilePath) {
				//console.log('确认裁剪, 裁剪后图片:', tempFilePath);

				// 先关闭裁剪组件
				this.cropperVisible = false;
				this.cropperSrc = '';

				// 确保图片路径有效
				if (!tempFilePath) {
					uni.showToast({
						title: '裁剪失败',
						icon: 'none'
					});
					return;
				}

				// 直接使用临时文件路径，不再调用saveFile
				this.newAvatar = tempFilePath;
				this.avatarChanged = true;

				// 保存临时头像数据到本地存储，防止页面刷新丢失
				uni.setStorageSync('tempAvatarData', {
					newAvatar: this.newAvatar,
					timestamp: Date.now()
				});

				// 显示临时头像
				if (this.userInfo) {
					this.userInfo.avatar = this.newAvatar;
				} else {
					// 如果 userInfo 为空，创建一个基本的用户信息对象
					this.userInfo = {
						avatar: this.newAvatar
					};
				}

				//console.log('头像已更新:', {
					//newAvatar: this.newAvatar,
					//avatarChanged: this.avatarChanged,
					//userInfoAvatar: this.userInfo.avatar
				//});

				// 强制刷新页面
				this.$forceUpdate();

				uni.showToast({
					title: '头像已选择，请点击保存',
					icon: 'none',
					duration: 2000
				});
			},
			
			// 显示昵称修改弹窗
			showNicknamePopup() {
				this.newNickname = this.userInfo?.nickname || '';
				this.$refs.nicknamePopup.open();
				// 打开弹窗后延迟聚焦输入框
				setTimeout(() => {
					this.nicknameFocus = true;
				}, 300);
			},
			
			// 取消昵称修改
			cancelNickname() {
				this.newNickname = '';
				this.nicknameFocus = false;
				this.$refs.nicknamePopup.close();
			},
			
			// 确认昵称修改
			confirmNickname() {
				if (this.newNickname && this.newNickname.trim().length > 0) {
					this.userInfo.nickname = this.newNickname.trim();
					this.$refs.nicknamePopup.close();
					this.nicknameFocus = false;
					uni.showToast({
						title: '昵称已更新，请点击保存',
						icon: 'none'
					});
				} else {
					uni.showToast({
						title: '昵称不能为空',
						icon: 'none'
					});
				}
			},
			
			// 跳转到密码修改页面
			navigateToPasswordPage() {
				uni.navigateTo({
					url: '/pages/mine/password'
				});
			},
			
			// 格式化手机号(中间4位显示*)
			formatPhone(phone) {
				if (!phone) return '';
				if (phone.length !== 11) return phone;

				return phone.substring(0, 3) + '****' + phone.substring(7);
			},

			// 修复 passive 事件监听器警告
			fixPassiveEventListeners() {
				// #ifdef H5
				if (typeof window !== 'undefined') {
					// 简单地为页面添加 touch-action CSS 属性
					const style = document.createElement('style');
					style.textContent = `
						.uni-popup, .popup-content, [class*="popup"] {
							touch-action: manipulation !important;
						}
					`;
					document.head.appendChild(style);
				}
				// #endif
			},
			
			// 上传头像
			uploadAvatar() {
				return new Promise((resolve, reject) => {
					if (!this.avatarChanged || !this.newAvatar) {
						resolve(null);
						return;
					}
					
					// 不在这里显示loading，而是由调用方统一处理
					
					// 直接上传临时文件，不再检查文件信息
					uni.uploadFile({
						url: Api.BASE_URL + '/v1/upload/avatar',
						filePath: this.newAvatar,
						name: 'file',
						header: {
							'Authorization': 'Bearer ' + uni.getStorageSync('token')
						},
						success: (uploadRes) => {
							try {
								const result = JSON.parse(uploadRes.data);
								if (result.code === 0) {
									resolve(result.data.url);
								} else {
									reject(new Error(result.message || '上传头像失败'));
								}
							} catch (e) {
								reject(new Error('解析上传结果失败'));
							}
						},
						fail: (err) => {
							console.error('上传头像失败:', err);
							reject(err);
						}
					});
				});
			},
			
			// 保存个人信息
			async saveProfile() {
				if (this.loading) return;
				
				// 检查是否有修改
				const originalUserInfo = uni.getStorageSync('userInfo');
				const nicknameChanged = originalUserInfo && originalUserInfo.nickname !== this.userInfo.nickname;
				
				// 如果没有修改头像也没有修改昵称，直接提示无需保存
				if (!this.avatarChanged && !nicknameChanged) {
					uni.showToast({
						title: '没有修改任何信息',
						icon: 'none'
					});
					return;
				}
				
				// 设置加载状态
				this.loading = true;
				this.saveButtonText = '保存中...';
				
				try {
					// 上传头像（如果有更新）
					let avatarUrl = null;
					if (this.avatarChanged) {
						try {
							avatarUrl = await this.uploadAvatar();
						} catch (error) {
							console.error('上传头像失败:', error);
							throw new Error('上传头像失败');
						}
					}
					
					// 更新用户信息
					const updateData = {
						nickname: this.userInfo.nickname
					};
					
					if (avatarUrl) {
						updateData.avatar = avatarUrl;
					}
					
					const res = await Api.request({
						url: '/v1/user/info',
						method: 'PUT',
						data: updateData
					});
					
					if (res.data && res.data.code === 0) {
						// 更新成功，直接更新本地存储
						if (res.data.data) {
							// 如果返回了用户数据，使用返回的数据更新
							this.userInfo = res.data.data;
						}
						// 更新本地存储
						uni.setStorageSync('userInfo', this.userInfo);
						
						// 显示成功提示
						uni.showToast({
							title: '保存成功',
							icon: 'success'
						});
						
						// 重置状态
						this.avatarChanged = false;
						this.newAvatar = '';

						// 清除临时头像数据
						uni.removeStorageSync('tempAvatarData');
					} else {
						throw new Error(res.data?.message || '保存失败');
					}
				} catch (err) {
					console.error('保存个人信息失败:', err);
					// 显示错误提示
					uni.showToast({
						title: err.message || '保存失败',
						icon: 'none'
					});
				} finally {
					// 重置加载状态
					this.loading = false;
					this.saveButtonText = '保存个人信息';
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.page {
		background-color: #f2f5fc;
		min-height: 100vh;
		padding-bottom: 180rpx;
	}
	
	.navbar {
		display: flex;
		align-items: center;
		justify-content: space-between;
		height: 90rpx;
		padding: 0 30rpx;
		background-color: #fff;
		box-shadow: 0 2rpx 10rpx rgba(0,0,0,0.05);
		position: sticky;
		top: 0;
		z-index: 100;
		
		.navbar-title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
		}
		
		.navbar-left, .navbar-right {
			width: 60rpx;
			display: flex;
			align-items: center;
		}
	}
	
	.content {
		padding: 30rpx;
	}
	
	.info-card {
		background-color: #fff;
		border-radius: 20rpx;
		margin-bottom: 30rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
		padding: 30rpx;
		
		.card-header {
			margin-bottom: 30rpx;
			
			.card-title {
				font-size: 32rpx;
				font-weight: bold;
				color: #333;
				position: relative;
				padding-left: 20rpx;
				
				&:before {
					content: "";
					position: absolute;
					left: 0;
					top: 50%;
					transform: translateY(-50%);
					width: 8rpx;
					height: 32rpx;
					background: linear-gradient(to bottom, #4a89dc, #3264ED);
					border-radius: 4rpx;
				}
			}
		}
	}
	
	.info-form {
		.form-item {
			display: flex;
			align-items: center;
			padding: 26rpx 0;
			border-bottom: 1px solid #f0f0f0;
			
			&:last-child {
				border-bottom: none;
			}
			
			.form-label {
				width: 160rpx;
				color: #666;
				font-size: 30rpx;
			}
			
			.form-value {
				flex: 1;
				color: #333;
				font-size: 30rpx;
				display: flex;
				align-items: center;
				justify-content: space-between;
				
				&.phone {
					color: #999;
				}
			}
			
			.form-input-wrapper {
				flex: 1;
				display: flex;
				align-items: center;
				justify-content: space-between;
			}
			
			&.avatar-item {
				align-items: flex-start;
				
				.avatar-wrapper {
					flex: 1;
					display: flex;
					align-items: center;
					justify-content: space-between;
					
					.avatar {
						width: 120rpx;
						height: 120rpx;
						border-radius: 50%;
						overflow: hidden;
						position: relative;
						border: 2rpx solid #eee;
						
						image {
							width: 100%;
							height: 100%;
						}
						
						.avatar-overlay {
							position: absolute;
							bottom: 0;
							left: 0;
							right: 0;
							height: 40rpx;
							background: rgba(0,0,0,0.5);
							display: flex;
							align-items: center;
							justify-content: center;
						}
					}
					
					.form-value {
						text {
							color: #3264ED;
							font-size: 28rpx;
							margin-right: 10rpx;
						}
					}
				}
			}
			
			.member-level {
				.level-badge {
					padding: 6rpx 20rpx;
					border-radius: 30rpx;
					font-size: 26rpx;
					background-color: #f5f5f5;
					color: #999;
					
					&.level-1 {
						background: linear-gradient(to right, rgba(250, 140, 22, 0.1), rgba(250, 173, 20, 0.2));
						color: #fa8c16;
					}
					
					&.level-2 {
						background: linear-gradient(to right, rgba(138, 138, 138, 0.1), rgba(179, 179, 179, 0.2));
						color: #8a8a8a;
					}
					
					&.level-3 {
						background: linear-gradient(to right, rgba(89, 188, 246, 0.1), rgba(77, 166, 253, 0.2));
						color: #4da6fd;
					}
				}
			}
		}
	}
	
	.security-list {
		.security-item {
			display: flex;
			align-items: center;
			padding: 26rpx 0;
			
			.security-icon {
				margin-right: 20rpx;
				width: 50rpx;
				height: 50rpx;
				border-radius: 50%;
				background-color: rgba(50, 100, 237, 0.1);
				display: flex;
				align-items: center;
				justify-content: center;
			}
			
			.security-content {
				flex: 1;
				
				.security-title {
					font-size: 30rpx;
					color: #333;
					margin-bottom: 6rpx;
				}
				
				.security-desc {
					font-size: 24rpx;
					color: #999;
				}
			}
		}
	}
	
	.save-button {
		position: fixed;
		bottom: 50rpx;
		left: 50rpx;
		right: 50rpx;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		background: linear-gradient(to right, #4a89dc, #3264ED);
		color: white;
		font-size: 32rpx;
		font-weight: bold;
		border-radius: 45rpx;
		box-shadow: 0 10rpx 20rpx rgba(50, 100, 237, 0.3);
		transition: opacity 0.3s;
		
		&.loading {
			opacity: 0.8;
		}
	}
	
	/* 弹窗样式 */
	.popup-content {
		width: 600rpx;
		background-color: #fff;
		border-radius: 20rpx;
		overflow: hidden;
		box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.1);
		animation: popup-in 0.3s ease-out;
	}
	
	@keyframes popup-in {
		from {
			transform: scale(0.9);
			opacity: 0;
		}
		to {
			transform: scale(1);
			opacity: 1;
		}
	}
	
	.popup-header {
		padding: 40rpx 0 20rpx;
		text-align: center;
		position: relative;
		
		&::after {
			content: '';
			position: absolute;
			left: 0;
			right: 0;
			bottom: 0;
			height: 1px;
			background-color: #f0f0f0;
			transform: scaleY(0.5);
		}
	}
	
	.popup-title {
		font-size: 36rpx;
		font-weight: 600;
		color: #333;
		letter-spacing: 1px;
	}
	
	.popup-body {
		padding: 40rpx 40rpx 20rpx;
		position: relative;
	}
	
	.popup-input {
		width: 100%;
		height: 90rpx;
		border: none;
		border-bottom: 1px solid #e0e0e0;
		padding: 0 20rpx;
		font-size: 32rpx;
		color: #333;
		background-color: transparent;
		transition: border-color 0.3s;
		
		&:focus {
			border-bottom: 1px solid #3264ED;
		}
	}
	
	.input-counter {
		position: absolute;
		right: 40rpx;
		bottom: 20rpx;
		font-size: 24rpx;
		color: #999;
	}
	
	.popup-buttons {
		display: flex;
		padding: 30rpx 40rpx 40rpx;
	}
	
	.popup-button {
		flex: 1;
		height: 90rpx;
		line-height: 90rpx;
		text-align: center;
		font-size: 32rpx;
		border-radius: 45rpx;
		border: none;
		transition: transform 0.2s, opacity 0.2s;
		
		&:active {
			transform: scale(0.98);
			opacity: 0.9;
		}
		
		&.cancel {
			margin-right: 30rpx;
			background-color: #f5f5f5;
			color: #666;
			font-weight: 500;
		}
		
		&.confirm {
			background: linear-gradient(to right, #4a89dc, #3264ED);
			color: #fff;
			box-shadow: 0 5rpx 15rpx rgba(50, 100, 237, 0.3);
			font-weight: 500;
		}
	}
</style> 