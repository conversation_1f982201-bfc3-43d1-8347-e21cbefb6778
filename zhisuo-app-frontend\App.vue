<script>
	export default {
		onLaunch: function() {
			console.log('App Launch')
			// 全局处理 passive 事件监听器
			this.setupPassiveEventListeners();
		},
		onShow: function() {
			console.log('App Show')
		},
		onHide: function() {
			console.log('App Hide')
		},
		methods: {
			// 设置全局 passive 事件监听器处理
			setupPassiveEventListeners() {
				// #ifdef H5
				if (typeof window !== 'undefined') {
					// 监听 DOM 变化，为新添加的元素设置 passive 事件
					const observer = new MutationObserver((mutations) => {
						mutations.forEach((mutation) => {
							if (mutation.type === 'childList') {
								mutation.addedNodes.forEach((node) => {
									if (node.nodeType === Node.ELEMENT_NODE) {
										this.processElementForPassiveEvents(node);
									}
								});
							}
						});
					});

					// 开始观察 DOM 变化
					observer.observe(document.body, {
						childList: true,
						subtree: true
					});
				}
				// #endif
			},

			// 处理元素的 passive 事件
			processElementForPassiveEvents(element) {
				// #ifdef H5
				if (typeof window !== 'undefined') {
					// 检查是否是弹窗相关元素
					if (element.classList && (
						element.classList.contains('uni-popup') ||
						element.classList.contains('popup-content') ||
						element.querySelector('.uni-popup') ||
						element.querySelector('.popup-content')
					)) {
						// 为弹窗元素添加 passive 事件处理
						const touchElements = element.querySelectorAll('*');
						touchElements.forEach(el => {
							// 确保触摸事件使用 passive 模式
							el.style.touchAction = 'manipulation';
						});
					}
				}
				// #endif
			}
		}
	}
</script>

<style>
	/*每个页面公共css */

	/* 优化uni-icons字体加载 */
	@font-face {
		font-family: 'uniicons';
		src: url('/uni_modules/uni-icons/components/uni-icons/uniicons.ttf') format('truetype');
		font-display: swap; /* 使用swap策略，优先显示fallback字体 */
	}

	/* 全局字体优化 */
	* {
		font-display: swap;
	}

	/* 全局触摸事件优化 */
	.uni-popup,
	.popup-content,
	[class*="popup"] {
		touch-action: manipulation;
		-webkit-touch-callout: none;
		-webkit-user-select: none;
		user-select: none;
	}

	/* 优化滚动性能 */
	.uni-scroll-view,
	scroll-view {
		-webkit-overflow-scrolling: touch;
		overflow-scrolling: touch;
	}
</style>
