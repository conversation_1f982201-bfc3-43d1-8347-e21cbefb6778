package com.zhisuo.app.dto.response;

import lombok.Data;

/**
 * AI对话响应DTO
 */
@Data
public class AIChatResponse {
    
    /**
     * AI回复内容
     */
    private String reply;
    
    /**
     * 消息ID (用于追踪)
     */
    private String messageId;
    
    /**
     * 响应时间戳
     */
    private Long timestamp;
    
    /**
     * 是否为备用回复 (当API调用失败时)
     */
    private Boolean isFallback;
    
    public AIChatResponse() {
        this.timestamp = System.currentTimeMillis();
        this.isFallback = false;
    }
    
    public AIChatResponse(String reply) {
        this();
        this.reply = reply;
    }
    
    public AIChatResponse(String reply, String messageId) {
        this(reply);
        this.messageId = messageId;
    }
    
    public static AIChatResponse fallback(String reply) {
        AIChatResponse response = new AIChatResponse(reply);
        response.setIsFallback(true);
        return response;
    }
}
