package com.zhisuo.app.entity;

import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

/**
 * 用户收藏实体类
 */
@Data
@TableName("user_favorites")
public class UserFavorite {
    
    @TableId
    private String favoriteId;    // 收藏ID
    
    private String userId;        // 用户ID
    
    private String contentId;     // 内容ID
    
    private String contentType;   // 内容类型(article/topic)
    
    private Date createTime;      // 收藏时间
} 